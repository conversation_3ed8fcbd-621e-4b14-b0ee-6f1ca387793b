from app import create_app, db
from app.models import Module, <PERSON><PERSON>

def init_modules_menus():
    app = create_app()
    with app.app_context():
        # 检查是否已有数据
        if Module.query.count() > 0:
            print("模块数据已存在，跳过初始化")
            return
        
        # 创建模块
        modules = [
            # 根据README.md中的菜单导航创建模块
            {"module_name": "home", "display_name": "首页", "description": "系统欢迎页面"},
            {"module_name": "resource_query", "display_name": "资源查询", "description": "提供对企业IT资源的综合查询功能"},
            {"module_name": "basic_data", "display_name": "基础数据管理", "description": "管理系统基础数据"},
            {"module_name": "user_management", "display_name": "用户管理", "description": "管理系统用户和权限"}
        ]
        
        module_dict = {}  # 用于存储模块ID映射
        
        for module_data in modules:
            module = Module(**module_data)
            db.session.add(module)
            db.session.flush()  # 获取ID
            module_dict[module.module_name] = module.id
        
        # 创建菜单
        menus = [
            # 资源查询模块下的菜单
            {"menu_name": "app_query", "menu_display_name": "应用查询", "module_id": module_dict["resource_query"]},
            {"menu_name": "host_query", "menu_display_name": "主机查询", "module_id": module_dict["resource_query"]},
            {"menu_name": "db_query", "menu_display_name": "数据库查询", "module_id": module_dict["resource_query"]},
            {"menu_name": "storage_query", "menu_display_name": "文件存储查询", "module_id": module_dict["resource_query"]},
            {"menu_name": "line_query", "menu_display_name": "专线查询", "module_id": module_dict["resource_query"]},
            {"menu_name": "phone_query", "menu_display_name": "分机号查询", "module_id": module_dict["resource_query"]},
            {"menu_name": "log_download", "menu_display_name": "日志下载", "module_id": module_dict["resource_query"]},
            {"menu_name": "user_permission_query", "menu_display_name": "用户权限汇总查询", "module_id": module_dict["resource_query"]},
            
            # 基础数据管理模块下的菜单
            {"menu_name": "system_management", "menu_display_name": "系统管理", "module_id": module_dict["basic_data"]},
            {"menu_name": "phone_management", "menu_display_name": "分机号码管理", "module_id": module_dict["basic_data"]},
            {"menu_name": "line_management", "menu_display_name": "专线信息管理", "module_id": module_dict["basic_data"]},
            
            # 用户管理模块下的菜单
            {"menu_name": "user_query", "menu_display_name": "用户查询", "module_id": module_dict["user_management"]},
            {"menu_name": "ldap_config", "menu_display_name": "LDAP配置管理", "module_id": module_dict["user_management"]},
            {"menu_name": "role_management", "menu_display_name": "角色管理", "module_id": module_dict["user_management"]},
            {"menu_name": "user_permission", "menu_display_name": "用户权限分配", "module_id": module_dict["user_management"]},
            {"menu_name": "ldap_user_import", "menu_display_name": "LDAP用户导入", "module_id": module_dict["user_management"]}
        ]
        
        for menu_data in menus:
            menu = Menu(**menu_data)
            db.session.add(menu)
        
        db.session.commit()
        print("模块和菜单数据初始化完成")

if __name__ == "__main__":
    init_modules_menus()
