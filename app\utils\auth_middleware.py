"""
认证中间件模块，提供全局认证功能
"""
from flask import request, g, current_app
import jwt
from ..models import User, LocalUser

def setup_auth_middleware(app):
    """
    设置认证中间件

    Args:
        app: Flask应用实例
    """
    @app.before_request
    def extract_user_from_token():
        """
        从请求中提取JWT令牌，解析用户信息并存储到g对象中
        """
        # 跳过OPTIONS请求（CORS预检请求）
        if request.method == 'OPTIONS':
            return

        # 跳过登录接口
        if request.path == '/api/login':
            current_app.logger.debug(f"跳过认证中间件: path={request.path}")
            return

        # 不再跳过验证令牌接口，让它也能设置g对象中的用户信息

        # 从请求头或cookie中获取token
        token = None
        auth_header = request.headers.get('Authorization')
        if auth_header and auth_header.startswith('Bearer '):
            token = auth_header.split(' ')[1]
            current_app.logger.debug(f"从Authorization头获取到token: {token[:10]}...")

        if not token:
            token = request.cookies.get('token')
            if token:
                current_app.logger.debug(f"从Cookie获取到token: {token[:10]}...")

        # 如果没有token，不设置用户信息，但允许请求继续（由路由函数决定是否需要认证）
        if not token:
            current_app.logger.debug(f"请求没有提供token: path={request.path}")
            return

        try:
            # 解码token
            payload = jwt.decode(
                token,
                current_app.config.get('SECRET_KEY'),
                algorithms=['HS256']
            )

            # 获取用户信息
            user_id = payload.get('user_id')
            user_type = payload.get('user_type', 'ldap')

            if not user_id:
                return

            # 根据用户类型获取用户对象
            if user_type == 'local':
                user = LocalUser.query.get(user_id)
            else:
                user = User.query.get(user_id)

            # 如果找到用户，将其存储到g对象中
            if user:
                g.user = user
                g.user_type = user_type

                # 添加用户名到g对象，方便日志记录
                g.username = user.username

                # 记录日志，方便调试
                current_app.logger.debug(f"认证中间件: 用户信息已设置, username={user.username}, user_id={user.id}, path={request.path}")

        except jwt.ExpiredSignatureError:
            # 令牌过期，不设置用户信息
            current_app.logger.debug(f"令牌已过期: path={request.path}")
            pass
        except jwt.InvalidTokenError:
            # 无效令牌，不设置用户信息
            current_app.logger.debug(f"无效的令牌: path={request.path}")
            pass
        except Exception as e:
            # 其他错误，记录日志但允许请求继续
            current_app.logger.error(f"解析令牌时出错: path={request.path}, error={str(e)}")
            pass
