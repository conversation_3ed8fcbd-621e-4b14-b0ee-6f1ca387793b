"""
日志工具模块，提供统一的日志记录接口
"""
import os
import logging
import re
from logging.handlers import TimedRotatingFileHandler
from flask import request, g, has_request_context
import functools
import traceback
from datetime import datetime
import pytz

# 定义北京时区
beijing_tz = pytz.timezone('Asia/Shanghai')

class DailyFileHandler(TimedRotatingFileHandler):
    """
    自定义日志处理器，每天创建新的日志文件，使用日期作为文件名的一部分
    例如：icms2-2025-05-23.log
    """
    def __init__(self, filename, base_filename, **kwargs):
        """
        初始化处理器

        Args:
            filename: 当前日志文件路径
            base_filename: 基础文件名（不含日期部分）
            **kwargs: 其他TimedRotatingFileHandler参数
        """
        self.base_filename = base_filename
        super().__init__(filename, **kwargs)

        # 禁用默认的文件重命名机制
        self.namer = None
        self.rotator = None

    def doRollover(self):
        """
        执行日志轮转，创建新的日志文件
        """
        # 关闭当前日志文件
        if self.stream:
            self.stream.close()
            self.stream = None

        # 计算下一个日志文件名（使用当前日期）
        current_time = datetime.now(beijing_tz)
        next_date_str = current_time.strftime("%Y-%m-%d")

        # 构建新的日志文件名
        dir_name, base_name = os.path.split(self.base_filename)
        name_parts = base_name.split('.')
        if len(name_parts) > 1:
            # 如果有扩展名
            name_without_ext = '.'.join(name_parts[:-1])
            ext = name_parts[-1]
            self.baseFilename = os.path.join(dir_name, f"{name_without_ext}-{next_date_str}.{ext}")
        else:
            # 如果没有扩展名
            self.baseFilename = os.path.join(dir_name, f"{base_name}-{next_date_str}")

        # 创建新的日志文件
        self.stream = self._open()

        # 删除过期的日志文件
        if self.backupCount > 0:
            # 获取所有匹配的日志文件
            dir_name, base_name = os.path.split(self.base_filename)
            name_parts = base_name.split('.')
            if len(name_parts) > 1:
                name_pattern = f"{name_parts[0]}-\\d{{4}}-\\d{{2}}-\\d{{2}}\\.{name_parts[-1]}"
            else:
                name_pattern = f"{base_name}-\\d{{4}}-\\d{{2}}-\\d{{2}}"

            file_pattern = re.compile(name_pattern)

            # 列出目录中的所有文件
            dir_path = dir_name or "."
            files = []
            for filename in os.listdir(dir_path):
                if file_pattern.match(filename):
                    files.append(os.path.join(dir_path, filename))

            # 按修改时间排序
            files.sort(key=lambda x: os.path.getmtime(x))

            # 删除过期的文件
            if len(files) > self.backupCount:
                for old_file in files[:-self.backupCount]:
                    try:
                        os.remove(old_file)
                    except (OSError, IOError):
                        # 忽略删除失败的错误
                        pass

# 日志级别映射
LOG_LEVELS = {
    'DEBUG': logging.DEBUG,
    'INFO': logging.INFO,
    'WARNING': logging.WARNING,
    'ERROR': logging.ERROR,
    'CRITICAL': logging.CRITICAL
}

class RequestFormatter(logging.Formatter):
    """自定义日志格式化器，添加请求上下文信息"""

    def format(self, record):
        """格式化日志记录"""
        # 添加请求信息
        if has_request_context():
            record.url = request.url
            record.remote_addr = request.remote_addr
            record.method = request.method

            # 添加用户信息
            if hasattr(g, 'user') and g.user:
                record.user_id = getattr(g.user, 'id', 'unknown')
                record.username = getattr(g.user, 'username', 'unknown')
            elif hasattr(g, 'username'):
                record.user_id = 'unknown'
                record.username = g.username
            else:
                record.user_id = 'unknown'
                record.username = 'unknown'
        else:
            record.url = 'N/A'
            record.remote_addr = 'N/A'
            record.method = 'N/A'
            record.user_id = 'N/A'
            record.username = 'N/A'

        # 调用父类方法完成格式化
        return super().format(record)

def setup_logger(app):
    """
    设置应用程序日志记录器

    Args:
        app: Flask应用实例
    """
    # 创建日志目录
    log_dir = app.config.get('LOG_DIR', 'logs')
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)

    # 获取日志配置
    log_file = os.path.join(log_dir, app.config.get('LOG_FILE', 'icms2.log'))
    log_level_name = app.config.get('LOG_LEVEL', 'INFO')
    log_level = LOG_LEVELS.get(log_level_name, logging.INFO)
    log_format = app.config.get('LOG_FORMAT',
                               '%(asctime)s [%(levelname)s] [%(remote_addr)s] [用户:%(user_id)s] '
                               '%(message)s [in %(pathname)s:%(lineno)d]')

    # 配置根日志记录器
    root_logger = logging.getLogger()
    root_logger.setLevel(log_level)

    # 清除现有处理器
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)

    # 获取当前日期
    current_date = datetime.now(beijing_tz).strftime('%Y-%m-%d')

    # 解析日志文件名和路径
    log_dir = os.path.dirname(log_file)
    log_filename = os.path.basename(log_file)
    name_parts = log_filename.split('.')

    # 构建当天的日志文件名
    if len(name_parts) > 1:
        # 如果有扩展名
        name_without_ext = '.'.join(name_parts[:-1])
        ext = name_parts[-1]
        current_log_file = os.path.join(log_dir, f"{name_without_ext}-{current_date}.{ext}")
    else:
        # 如果没有扩展名
        current_log_file = os.path.join(log_dir, f"{log_filename}-{current_date}")

    # 创建自定义的日志处理器
    file_handler = DailyFileHandler(
        current_log_file,  # 当前日期的日志文件
        log_file,          # 基础文件名
        when='midnight',   # 每天午夜轮转
        interval=1,        # 间隔为1天
        backupCount=app.config.get('LOG_BACKUP_COUNT', 30),  # 保留30天的日志
        encoding='utf-8'
    )

    # 设置日志格式
    formatter = RequestFormatter(log_format)
    file_handler.setFormatter(formatter)
    file_handler.setLevel(log_level)

    # 添加处理器到根日志记录器
    root_logger.addHandler(file_handler)

    # 如果配置了控制台输出，添加控制台处理器
    if app.config.get('LOG_TO_CONSOLE', True):
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(formatter)
        console_handler.setLevel(log_level)
        root_logger.addHandler(console_handler)

    # 设置Flask应用日志记录器
    app.logger.setLevel(log_level)

    # 记录应用启动信息
    app.logger.info(f"应用启动成功，环境: {app.config.get('ENV', 'development')}, 日志级别: {log_level_name}")

def log_exception(func):
    """
    装饰器：记录函数执行过程中的异常

    Args:
        func: 要装饰的函数

    Returns:
        装饰后的函数
    """
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except Exception as e:
            # 获取当前时间（北京时间）
            current_time = datetime.now(beijing_tz).strftime('%Y-%m-%d %H:%M:%S')

            # 获取异常堆栈
            stack_trace = traceback.format_exc()

            # 获取请求信息
            if has_request_context():
                client_ip = request.remote_addr
                user_id = getattr(g, 'user', None) and getattr(g.user, 'id', 'unknown') or 'unknown'
                endpoint = request.endpoint
                method = request.method
                url = request.url

                # 记录详细的异常信息
                logging.error(
                    f"异常发生 - 时间: {current_time}, IP: {client_ip}, 用户: {user_id}, "
                    f"端点: {endpoint}, 方法: {method}, URL: {url}\n"
                    f"异常类型: {type(e).__name__}, 异常信息: {str(e)}\n"
                    f"堆栈跟踪:\n{stack_trace}"
                )
            else:
                # 非请求上下文中的异常
                logging.error(
                    f"异常发生 - 时间: {current_time}, 上下文: 非HTTP请求\n"
                    f"异常类型: {type(e).__name__}, 异常信息: {str(e)}\n"
                    f"堆栈跟踪:\n{stack_trace}"
                )

            # 重新抛出异常，让上层处理
            raise

    return wrapper
