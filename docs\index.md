# ICMS2 文档

欢迎查阅ICMS2（集成内容管理系统2）文档。

## 文档内容

- [概述](README.md)
- [安装指南](安装指南.md)
- [系统架构](系统架构.md)
- [API参考](API参考.md)
- [数据模型](数据模型.md)
- [认证系统](认证系统.md)
- [配置指南](配置指南.md)

## 快速链接

- [入门指南](#入门指南)
- [主要特点](#主要特点)
- [系统要求](#系统要求)
- [支持](#支持)

## 入门指南

要开始使用ICMS2：

1. 按照[安装指南](安装指南.md)设置系统
2. 使用[配置指南](配置指南.md)配置应用程序
3. 通过[系统架构](系统架构.md)文档了解系统结构
4. 在[API参考](API参考.md)中探索可用的API

## 主要特点

ICMS2提供以下主要特点：

- **双重认证**：支持LDAP和本地用户认证
- **基于角色的访问控制**：通过角色进行细粒度的权限管理
- **模块化系统**：模块化架构便于扩展
- **RESTful API**：结构良好的API便于前端集成
- **可配置性**：多种环境配置（开发、测试、生产）

## 系统要求

- **Python**：3.8或更高版本
- **数据库**：MySQL 5.7或更高版本
- **操作系统**：Windows、macOS或Linux

## 支持

有关ICMS2的支持或问题，请联系系统管理员或参考内部支持渠道。
