# 模块与菜单 API 文档

本文档描述了模块与菜单管理模块（`module_menu.py`）中的所有 API 接口。

## 目录

- [获取所有模块](#获取所有模块)
- [获取菜单列表](#获取菜单列表)
- [获取模块及其菜单](#获取模块及其菜单)

## 获取所有模块

获取系统中所有模块的基本信息。

**请求方式**：GET

**URL**：`/api/modules`

**响应示例**：
```json
[
  {
    "id": 1,
    "module_name": "resource_query",
    "display_name": "资源查询",
    "description": "提供对企业IT资源的综合查询功能"
  },
  {
    "id": 2,
    "module_name": "basic_data",
    "display_name": "基础数据管理",
    "description": "管理系统基础数据"
  },
  {
    "id": 3,
    "module_name": "user_management",
    "display_name": "用户管理",
    "description": "管理系统用户和权限"
  }
]
```

**说明**：
- 返回系统中所有模块的列表
- 每个模块包含ID、模块名称（英文标识）、显示名称和描述信息
- 此接口适用于需要获取所有模块基本信息的场景，如模块选择下拉框

## 获取菜单列表

获取系统中的菜单列表，可选择按模块ID过滤。

**请求方式**：GET

**URL**：`/api/menus`

**查询参数**：

| 参数名    | 类型    | 必填 | 描述     |
|-----------|---------|------|----------|
| module_id | integer | 否   | 模块ID，用于过滤特定模块下的菜单 |

**响应示例**：
```json
[
  {
    "id": 1,
    "menu_name": "app_query",
    "menu_display_name": "应用查询",
    "module_id": 1
  },
  {
    "id": 2,
    "menu_name": "host_query",
    "menu_display_name": "主机查询",
    "module_id": 1
  },
  {
    "id": 3,
    "menu_name": "db_query",
    "menu_display_name": "数据库查询",
    "module_id": 1
  }
]
```

**说明**：
- 返回系统中的菜单列表
- 如果提供了 `module_id` 参数，则只返回该模块下的菜单
- 如果未提供 `module_id` 参数，则返回所有菜单
- 每个菜单包含ID、菜单名称（英文标识）、显示名称和所属模块ID
- 此接口适用于需要获取菜单列表的场景，如菜单选择下拉框

## 获取模块及其菜单

获取所有模块及其下属菜单，以树形结构返回。

**请求方式**：GET

**URL**：`/api/module-menus`

**响应示例**：
```json
[
  {
    "id": 1,
    "module_name": "resource_query",
    "display_name": "资源查询",
    "description": "提供对企业IT资源的综合查询功能",
    "menus": [
      {
        "id": 1,
        "menu_name": "app_query",
        "menu_display_name": "应用查询"
      },
      {
        "id": 2,
        "menu_name": "host_query",
        "menu_display_name": "主机查询"
      },
      {
        "id": 3,
        "menu_name": "db_query",
        "menu_display_name": "数据库查询"
      }
    ]
  },
  {
    "id": 2,
    "module_name": "basic_data",
    "display_name": "基础数据管理",
    "description": "管理系统基础数据",
    "menus": [
      {
        "id": 8,
        "menu_name": "system_management",
        "menu_display_name": "系统管理"
      },
      {
        "id": 9,
        "menu_name": "phone_management",
        "menu_display_name": "分机号码管理"
      }
    ]
  }
]
```

**说明**：
- 返回所有模块及其下属菜单，以树形结构组织
- 每个模块包含基本信息（ID、模块名称、显示名称、描述）和该模块下的所有菜单
- 每个菜单包含基本信息（ID、菜单名称、显示名称）
- 此接口适用于构建系统导航菜单、权限配置界面等场景
- 树形结构便于前端直接渲染层级菜单