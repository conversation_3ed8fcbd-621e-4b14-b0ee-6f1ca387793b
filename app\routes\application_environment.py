"""
应用环境管理模块
"""
from flask import Blueprint, request, jsonify, current_app, g
from sqlalchemy import or_, and_, func
from datetime import datetime
import json
from .. import db, beijing_tz
from ..models import (
    Application, EnvironmentBase, DeployMethod, ApplicationEnvironment,
    ApplicationEnvLb, ApplicationEnvServer, ApplicationEnvDatabase,
    LoadBalancerResource, ServerResource, DatabaseResource,
    ResourceChangeHistory, OperationType
)
from ..utils.decorators import log_route, log_operation
from ..utils.user_utils import get_current_user_id

# 创建蓝图
application_environment = Blueprint('application_environment', __name__)

@application_environment.route('/application-environments', methods=['GET'])
@log_route
def get_application_environments():
    """
    查询应用环境列表，支持按应用名称进行模糊查询

    Query Parameters:
        app_en_name (str, optional): 应用英文名关键字
        app_cn_name (str, optional): 应用中文名关键字
        page (int, optional): 页码，默认为1
        per_page (int, optional): 每页记录数，默认为20

    Returns:
        JSON: 应用环境列表和分页信息
    """
    # 获取查询参数
    app_en_name = request.args.get('app_en_name', '')
    app_cn_name = request.args.get('app_cn_name', '')
    page = int(request.args.get('page', 1))
    per_page = int(request.args.get('per_page', 20))

    # 构建查询
    query = db.session.query(
        ApplicationEnvironment,
        Application,
        EnvironmentBase
    ).join(
        Application,
        ApplicationEnvironment.application_id == Application.id
    ).join(
        EnvironmentBase,
        ApplicationEnvironment.env_id == EnvironmentBase.id
    )

    # 应用过滤条件
    if app_en_name:
        query = query.filter(Application.app_en_name.like(f'%{app_en_name}%'))
    if app_cn_name:
        query = query.filter(Application.app_cn_name.like(f'%{app_cn_name}%'))

    # 分页
    pagination = query.paginate(page=page, per_page=per_page, error_out=False)
    total = pagination.total

    # 构建结果
    results = []
    for app_env, app, env in pagination.items:
        # 获取部署方式名称
        deploy_methods = []
        deploy_method_ids = app_env.app_deploy_method.split(',')
        for deploy_id in deploy_method_ids:
            if deploy_id:
                deploy = DeployMethod.query.get(int(deploy_id))
                if deploy:
                    deploy_methods.append(deploy.deploy_name)

        results.append({
            'id': app_env.id,
            'application_id': app_env.application_id,
            'app_en_name': app.app_en_name,
            'app_cn_name': app.app_cn_name,
            'env_id': app_env.env_id,
            'env_en_name': env.env_en_name,
            'env_cn_name': env.env_cn_name,
            'app_deploy_method': app_env.app_deploy_method,
            'deploy_method_names': deploy_methods,
            'app_env_url': app_env.app_env_url,
            'app_env_port': app_env.app_env_port,
            'app_env_status': app_env.app_env_status,
            'create_time': app_env.create_time.strftime('%Y-%m-%d %H:%M:%S') if app_env.create_time else None,
            'update_time': app_env.update_time.strftime('%Y-%m-%d %H:%M:%S') if app_env.update_time else None,
            'offline_time': app_env.offline_time.strftime('%Y-%m-%d %H:%M:%S') if app_env.offline_time else None,
            'app_env_remark': app_env.app_env_remark
        })

    return jsonify({
        'total': total,
        'page': page,
        'per_page': per_page,
        'pages': (total + per_page - 1) // per_page,
        'application_environments': results
    })

@application_environment.route('/applications-dropdown', methods=['GET'])
def get_applications_dropdown():
    """
    获取应用下拉列表

    Returns:
        JSON: 应用列表，包含id, app_en_name, app_cn_name
    """
    applications = Application.query.filter_by(app_status='online').all()
    return jsonify([
        {
            'id': app.id,
            'app_en_name': app.app_en_name,
            'app_cn_name': app.app_cn_name
        } for app in applications
    ])

@application_environment.route('/environments-dropdown', methods=['GET'])
def get_environments_dropdown():
    """
    获取环境下拉列表

    Returns:
        JSON: 环境列表，包含id, env_en_name, env_cn_name
    """
    environments = EnvironmentBase.query.all()
    return jsonify([
        {
            'id': env.id,
            'env_en_name': env.env_en_name,
            'env_cn_name': env.env_cn_name
        } for env in environments
    ])

@application_environment.route('/deploy-methods-dropdown', methods=['GET'])
def get_deploy_methods_dropdown():
    """
    获取部署方式下拉列表

    Returns:
        JSON: 部署方式列表，包含id, deploy_code, deploy_name
    """
    deploy_methods = DeployMethod.query.all()
    return jsonify([
        {
            'id': dm.id,
            'deploy_code': dm.deploy_code,
            'deploy_name': dm.deploy_name
        } for dm in deploy_methods
    ])

@application_environment.route('/application-environments', methods=['POST'])
@log_route
@log_operation('创建应用环境')
def create_application_environment():
    """
    创建新应用环境

    Request Body:
        application_id (int): 应用ID
        env_id (int): 环境ID
        app_deploy_method (str): 部署方式ID列表，逗号分隔
        app_env_url (str, optional): 应用URL
        app_env_port (int, optional): 应用端口
        app_env_remark (str, optional): 备注

    Returns:
        JSON: 创建结果
    """
    data = request.get_json()

    # 验证必填字段
    required_fields = ['application_id', 'env_id', 'app_deploy_method']
    for field in required_fields:
        if field not in data or not data[field]:
            return jsonify({'error': f'缺少必填字段: {field}'}), 400

    # 检查应用ID是否存在
    app = Application.query.get(data['application_id'])
    if not app:
        return jsonify({'error': f'应用ID {data["application_id"]} 不存在'}), 400

    # 检查环境ID是否存在
    env = EnvironmentBase.query.get(data['env_id'])
    if not env:
        return jsonify({'error': f'环境ID {data["env_id"]} 不存在'}), 400

    # 检查部署方式ID是否存在
    # 处理app_deploy_method可能是列表或字符串的情况
    if isinstance(data['app_deploy_method'], list):
        deploy_method_ids = [str(deploy_id) for deploy_id in data['app_deploy_method']]
        # 将列表转换为逗号分隔的字符串，以便存储
        data['app_deploy_method'] = ','.join(deploy_method_ids)
    elif isinstance(data['app_deploy_method'], str):
        deploy_method_ids = data['app_deploy_method'].split(',')
    else:
        return jsonify({'error': 'app_deploy_method 格式不正确，应为列表或逗号分隔的字符串'}), 400

    for deploy_id in deploy_method_ids:
        if deploy_id:
            try:
                deploy = DeployMethod.query.get(int(deploy_id))
                if not deploy:
                    return jsonify({'error': f'部署方式ID {deploy_id} 不存在'}), 400
            except ValueError:
                return jsonify({'error': f'部署方式ID {deploy_id} 格式不正确'}), 400

    # 检查该应用在该环境下是否已存在
    existing = ApplicationEnvironment.query.filter_by(
        application_id=data['application_id'],
        env_id=data['env_id']
    ).first()
    if existing:
        return jsonify({'error': f'该应用在该环境下已存在'}), 400

    # 创建新应用环境
    app_env = ApplicationEnvironment(
        application_id=data['application_id'],
        env_id=data['env_id'],
        app_deploy_method=data['app_deploy_method'],
        app_env_url=data.get('app_env_url', ''),
        app_env_port=data.get('app_env_port'),
        app_env_status='online',  # 默认为在线状态
        app_env_remark=data.get('app_env_remark', '')
    )

    db.session.add(app_env)
    db.session.flush()  # 获取ID

    # 记录变更历史
    history = ResourceChangeHistory(
        user_id=get_current_user_id(),  # 使用工具函数获取当前用户ID
        resource_type='application_environment',
        resource_id=app_env.id,
        operation_type=OperationType.CREATE.value,
        after_data=json.dumps({
            'application_id': app_env.application_id,
            'env_id': app_env.env_id,
            'app_deploy_method': app_env.app_deploy_method,
            'app_env_url': app_env.app_env_url,
            'app_env_port': app_env.app_env_port,
            'app_env_status': app_env.app_env_status,
            'app_env_remark': app_env.app_env_remark
        })
    )

    db.session.add(history)
    db.session.commit()

    return jsonify({
        'message': '应用环境创建成功',
        'id': app_env.id
    }), 201

@application_environment.route('/application-environments/<int:app_env_id>', methods=['PUT'])
def update_application_environment(app_env_id):
    """
    更新应用环境信息

    Path Parameters:
        app_env_id (int): 应用环境ID

    Request Body:
        app_deploy_method (str, optional): 部署方式ID列表，逗号分隔
        app_env_url (str, optional): 应用URL
        app_env_port (int, optional): 应用端口
        app_env_remark (str, optional): 备注

    Returns:
        JSON: 更新结果
    """
    app_env = ApplicationEnvironment.query.get_or_404(app_env_id)
    data = request.get_json()

    # 只允许更新部署方式、应用URL、应用端口和备注
    before_data = {
        'app_deploy_method': app_env.app_deploy_method,
        'app_env_url': app_env.app_env_url,
        'app_env_port': app_env.app_env_port,
        'app_env_remark': app_env.app_env_remark
    }

    changes_made = False

    if 'app_deploy_method' in data:
        # 处理app_deploy_method可能是列表或字符串的情况
        if isinstance(data['app_deploy_method'], list):
            deploy_method_ids = [str(deploy_id) for deploy_id in data['app_deploy_method']]
            # 将列表转换为逗号分隔的字符串，以便存储
            data['app_deploy_method'] = ','.join(deploy_method_ids)
        elif isinstance(data['app_deploy_method'], str):
            deploy_method_ids = data['app_deploy_method'].split(',')
        else:
            return jsonify({'error': 'app_deploy_method 格式不正确，应为列表或逗号分隔的字符串'}), 400

        if data['app_deploy_method'] != app_env.app_deploy_method:
            # 检查部署方式ID是否存在
            for deploy_id in deploy_method_ids:
                if deploy_id:
                    try:
                        deploy = DeployMethod.query.get(int(deploy_id))
                        if not deploy:
                            return jsonify({'error': f'部署方式ID {deploy_id} 不存在'}), 400
                    except ValueError:
                        return jsonify({'error': f'部署方式ID {deploy_id} 格式不正确'}), 400

            app_env.app_deploy_method = data['app_deploy_method']
            changes_made = True

    if 'app_env_url' in data and data['app_env_url'] != app_env.app_env_url:
        app_env.app_env_url = data['app_env_url']
        changes_made = True

    if 'app_env_port' in data and data['app_env_port'] != app_env.app_env_port:
        app_env.app_env_port = data['app_env_port']
        changes_made = True

    if 'app_env_remark' in data and data['app_env_remark'] != app_env.app_env_remark:
        app_env.app_env_remark = data['app_env_remark']
        changes_made = True

    if not changes_made:
        return jsonify({'message': '没有变更需要保存'}), 200

    # 更新时间
    app_env.update_time = datetime.now(beijing_tz).replace(tzinfo=None)

    # 记录变更历史
    history = ResourceChangeHistory(
        user_id=get_current_user_id(),  # 使用工具函数获取当前用户ID
        resource_type='application_environment',
        resource_id=app_env.id,
        operation_type=OperationType.UPDATE.value,
        before_data=json.dumps(before_data),
        after_data=json.dumps({
            'app_deploy_method': app_env.app_deploy_method,
            'app_env_url': app_env.app_env_url,
            'app_env_port': app_env.app_env_port,
            'app_env_remark': app_env.app_env_remark
        })
    )

    db.session.add(history)
    db.session.commit()

    return jsonify({'message': '应用环境更新成功'})

@application_environment.route('/application-environments/<int:app_env_id>/offline', methods=['PUT'])
@log_route
@log_operation('下线应用环境')
def offline_application_environment(app_env_id):
    """
    下线应用环境

    Path Parameters:
        app_env_id (int): 应用环境ID

    Returns:
        JSON: 下线结果
    """
    # 下线操作不需要请求体，忽略任何请求体内容

    app_env = ApplicationEnvironment.query.get_or_404(app_env_id)

    if app_env.app_env_status == 'offline':
        return jsonify({'message': '应用环境已经是下线状态'}), 400

    before_data = {
        'app_env_status': app_env.app_env_status,
        'offline_time': app_env.offline_time.strftime('%Y-%m-%d %H:%M:%S') if app_env.offline_time else None
    }

    try:
        # 更新状态和下线时间
        app_env.app_env_status = 'offline'
        app_env.offline_time = datetime.now(beijing_tz).replace(tzinfo=None)

        # 解除与所有资源的绑定关系
        # 1. 解除负载均衡资源绑定
        lb_relations = ApplicationEnvLb.query.filter_by(application_environment_id=app_env_id).all()
        for relation in lb_relations:
            db.session.delete(relation)

        # 2. 解除服务器资源绑定
        server_relations = ApplicationEnvServer.query.filter_by(application_environment_id=app_env_id).all()
        for relation in server_relations:
            db.session.delete(relation)

        # 3. 解除数据库资源绑定
        db_relations = ApplicationEnvDatabase.query.filter_by(application_environment_id=app_env_id).all()
        for relation in db_relations:
            db.session.delete(relation)

        # 记录变更历史
        history = ResourceChangeHistory(
            user_id=get_current_user_id(),  # 使用工具函数获取当前用户ID
            resource_type='application_environment',
            resource_id=app_env.id,
            operation_type=OperationType.UPDATE.value,
            before_data=json.dumps(before_data),
            after_data=json.dumps({
                'app_env_status': app_env.app_env_status,
                'offline_time': app_env.offline_time.strftime('%Y-%m-%d %H:%M:%S')
            })
        )

        db.session.add(history)
        db.session.commit()

        return jsonify({'message': '应用环境已成功下线，并已解除所有资源绑定'})

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"下线应用环境时出错: {str(e)}")
        return jsonify({'error': f'下线应用环境时出错: {str(e)}'}), 500

@application_environment.route('/application-environments/<int:app_env_id>/resources', methods=['GET'])
def get_application_environment_resources(app_env_id):
    """
    获取应用环境的资源列表

    Path Parameters:
        app_env_id (int): 应用环境ID

    Returns:
        JSON: 资源列表
    """
    app_env = ApplicationEnvironment.query.get_or_404(app_env_id)

    # 获取负载均衡资源
    lb_resources = []
    lb_relations = ApplicationEnvLb.query.filter_by(application_environment_id=app_env_id).all()
    for relation in lb_relations:
        lb = LoadBalancerResource.query.get(relation.lb_id)
        if lb:
            lb_resources.append(lb.to_dict())

    # 获取服务器资源
    server_resources = []
    server_relations = ApplicationEnvServer.query.filter_by(application_environment_id=app_env_id).all()
    for relation in server_relations:
        server = ServerResource.query.get(relation.server_id)
        if server:
            server_dict = {
                'id': server.id,
                'instance_id': server.instance_id,
                'hostname': server.hostname,
                'ipv4': server.ipv4,
                'os': server.os,
                'cpu': server.cpu,
                'memory': server.memory,
                'region': server.region,
                'server_port': relation.server_port
            }
            server_resources.append(server_dict)

    # 获取数据库资源
    db_resources = []
    db_relations = ApplicationEnvDatabase.query.filter_by(application_environment_id=app_env_id).all()
    for relation in db_relations:
        db_resource = DatabaseResource.query.get(relation.db_id)
        if db_resource:
            db_resources.append(db_resource.to_dict())

    return jsonify({
        'load_balancers': lb_resources,
        'servers': server_resources,
        'databases': db_resources
    })

@application_environment.route('/load-balancers/search', methods=['GET'])
def search_load_balancers():
    """
    搜索负载均衡资源

    Query Parameters:
        keyword (str): 关键字，用于匹配VIP或名称
        page (int, optional): 页码，默认为1
        per_page (int, optional): 每页记录数，默认为20

    Returns:
        JSON: 负载均衡资源列表和分页信息
    """
    keyword = request.args.get('keyword', '')
    page = int(request.args.get('page', 1))
    per_page = int(request.args.get('per_page', 20))

    # 构建查询
    query = LoadBalancerResource.query

    if keyword:
        query = query.filter(
            or_(
                LoadBalancerResource.vip.like(f'%{keyword}%'),
                LoadBalancerResource.lb_name.like(f'%{keyword}%'),
                LoadBalancerResource.loadbalancer_id.like(f'%{keyword}%')
            )
        )

    # 只查询在线状态的负载均衡资源
    query = query.filter(LoadBalancerResource.lb_status == 'online')

    # 分页
    pagination = query.paginate(page=page, per_page=per_page, error_out=False)
    total = pagination.total

    # 构建结果
    results = [lb.to_dict() for lb in pagination.items]

    return jsonify({
        'total': total,
        'page': page,
        'per_page': per_page,
        'pages': (total + per_page - 1) // per_page,
        'load_balancers': results
    })

@application_environment.route('/application-environments/<int:app_env_id>/load-balancers', methods=['POST'])
def assign_load_balancer(app_env_id):
    """
    为应用环境分配负载均衡资源

    Path Parameters:
        app_env_id (int): 应用环境ID

    Request Body:
        lb_id (int): 负载均衡资源ID

    Returns:
        JSON: 分配结果
    """
    app_env = ApplicationEnvironment.query.get_or_404(app_env_id)
    data = request.get_json()

    # 验证必填字段
    if 'lb_id' not in data or not data['lb_id']:
        return jsonify({'error': '缺少必填字段: lb_id'}), 400

    lb_id = data['lb_id']

    # 检查负载均衡资源是否存在
    lb = LoadBalancerResource.query.get(lb_id)
    if not lb:
        return jsonify({'error': f'负载均衡资源ID {lb_id} 不存在'}), 400

    # 检查是否已分配
    existing = ApplicationEnvLb.query.filter_by(
        application_environment_id=app_env_id,
        lb_id=lb_id
    ).first()
    if existing:
        return jsonify({'error': f'该负载均衡资源已分配给此应用环境'}), 400

    # 创建关联
    relation = ApplicationEnvLb(
        application_environment_id=app_env_id,
        lb_id=lb_id
    )

    db.session.add(relation)
    db.session.commit()

    return jsonify({'message': '负载均衡资源分配成功'})

@application_environment.route('/servers/search', methods=['GET'])
def search_servers():
    """
    搜索服务器资源

    Query Parameters:
        keyword (str): 关键字，用于匹配IP、主机名或实例ID
        page (int, optional): 页码，默认为1
        per_page (int, optional): 每页记录数，默认为20

    Returns:
        JSON: 服务器资源列表和分页信息
    """
    keyword = request.args.get('keyword', '')
    page = int(request.args.get('page', 1))
    per_page = int(request.args.get('per_page', 20))

    # 构建查询
    query = ServerResource.query

    if keyword:
        query = query.filter(
            or_(
                ServerResource.ipv4.like(f'%{keyword}%'),
                ServerResource.hostname.like(f'%{keyword}%'),
                ServerResource.instance_id.like(f'%{keyword}%')
            )
        )

    # 只查询在线状态的服务器资源
    query = query.filter(ServerResource.server_status == 'online')

    # 分页
    pagination = query.paginate(page=page, per_page=per_page, error_out=False)
    total = pagination.total

    # 构建结果
    results = [server.to_dict() for server in pagination.items]

    return jsonify({
        'total': total,
        'page': page,
        'per_page': per_page,
        'pages': (total + per_page - 1) // per_page,
        'servers': results
    })

@application_environment.route('/application-environments/<int:app_env_id>/servers', methods=['POST'])
def assign_server(app_env_id):
    """
    为应用环境分配服务器资源

    Path Parameters:
        app_env_id (int): 应用环境ID

    Request Body:
        server_id (int): 服务器资源ID
        server_port (str, optional): 应用端口

    Returns:
        JSON: 分配结果
    """
    app_env = ApplicationEnvironment.query.get_or_404(app_env_id)
    data = request.get_json()

    # 验证必填字段
    if 'server_id' not in data or not data['server_id']:
        return jsonify({'error': '缺少必填字段: server_id'}), 400

    server_id = data['server_id']
    server_port = data.get('server_port', '')

    # 检查服务器资源是否存在
    server = ServerResource.query.get(server_id)
    if not server:
        return jsonify({'error': f'服务器资源ID {server_id} 不存在'}), 400

    # 检查是否已分配
    existing = ApplicationEnvServer.query.filter_by(
        application_environment_id=app_env_id,
        server_id=server_id
    ).first()
    if existing:
        return jsonify({'error': f'该服务器资源已分配给此应用环境'}), 400

    # 创建关联
    relation = ApplicationEnvServer(
        application_environment_id=app_env_id,
        server_id=server_id,
        server_port=server_port
    )

    db.session.add(relation)
    db.session.commit()

    return jsonify({'message': '服务器资源分配成功'})

@application_environment.route('/databases/search', methods=['GET'])
def search_databases():
    """
    搜索数据库资源

    Query Parameters:
        keyword (str): 关键字，用于匹配数据库名称、IP或类型
        page (int, optional): 页码，默认为1
        per_page (int, optional): 每页记录数，默认为20

    Returns:
        JSON: 数据库资源列表和分页信息
    """
    keyword = request.args.get('keyword', '')
    page = int(request.args.get('page', 1))
    per_page = int(request.args.get('per_page', 20))

    # 构建查询
    query = DatabaseResource.query

    if keyword:
        query = query.filter(
            or_(
                DatabaseResource.db_name.like(f'%{keyword}%'),
                DatabaseResource.db_ip.like(f'%{keyword}%'),
                DatabaseResource.db_type.like(f'%{keyword}%')
            )
        )

    # 只查询在线状态的数据库资源
    query = query.filter(DatabaseResource.db_status == 'online')

    # 分页
    pagination = query.paginate(page=page, per_page=per_page, error_out=False)
    total = pagination.total

    # 构建结果
    results = [db_resource.to_dict() for db_resource in pagination.items]

    return jsonify({
        'total': total,
        'page': page,
        'per_page': per_page,
        'pages': (total + per_page - 1) // per_page,
        'databases': results
    })

@application_environment.route('/application-environments/<int:app_env_id>/databases', methods=['POST'])
def assign_database(app_env_id):
    """
    为应用环境分配数据库资源

    Path Parameters:
        app_env_id (int): 应用环境ID

    Request Body:
        db_id (int): 数据库资源ID

    Returns:
        JSON: 分配结果
    """
    app_env = ApplicationEnvironment.query.get_or_404(app_env_id)
    data = request.get_json()

    # 验证必填字段
    if 'db_id' not in data or not data['db_id']:
        return jsonify({'error': '缺少必填字段: db_id'}), 400

    db_id = data['db_id']

    # 检查数据库资源是否存在
    db_resource = DatabaseResource.query.get(db_id)
    if not db_resource:
        return jsonify({'error': f'数据库资源ID {db_id} 不存在'}), 400

    # 检查是否已分配
    existing = ApplicationEnvDatabase.query.filter_by(
        application_environment_id=app_env_id,
        db_id=db_id
    ).first()
    if existing:
        return jsonify({'error': f'该数据库资源已分配给此应用环境'}), 400

    # 创建关联
    relation = ApplicationEnvDatabase(
        application_environment_id=app_env_id,
        db_id=db_id
    )

    db.session.add(relation)
    db.session.commit()

    return jsonify({'message': '数据库资源分配成功'})

@application_environment.route('/application-environments/<int:app_env_id>/load-balancers/<int:lb_id>', methods=['DELETE'])
def remove_load_balancer(app_env_id, lb_id):
    """
    从应用环境中移除负载均衡资源

    Path Parameters:
        app_env_id (int): 应用环境ID
        lb_id (int): 负载均衡资源ID

    Returns:
        JSON: 移除结果
    """
    relation = ApplicationEnvLb.query.filter_by(
        application_environment_id=app_env_id,
        lb_id=lb_id
    ).first_or_404()

    db.session.delete(relation)
    db.session.commit()

    return jsonify({'message': '负载均衡资源移除成功'})

@application_environment.route('/application-environments/<int:app_env_id>/servers/<int:server_id>', methods=['DELETE'])
def remove_server(app_env_id, server_id):
    """
    从应用环境中移除服务器资源

    Path Parameters:
        app_env_id (int): 应用环境ID
        server_id (int): 服务器资源ID

    Returns:
        JSON: 移除结果
    """
    relation = ApplicationEnvServer.query.filter_by(
        application_environment_id=app_env_id,
        server_id=server_id
    ).first_or_404()

    db.session.delete(relation)
    db.session.commit()

    return jsonify({'message': '服务器资源移除成功'})

@application_environment.route('/application-environments/<int:app_env_id>/databases/<int:db_id>', methods=['DELETE'])
def remove_database(app_env_id, db_id):
    """
    从应用环境中移除数据库资源

    Path Parameters:
        app_env_id (int): 应用环境ID
        db_id (int): 数据库资源ID

    Returns:
        JSON: 移除结果
    """
    relation = ApplicationEnvDatabase.query.filter_by(
        application_environment_id=app_env_id,
        db_id=db_id
    ).first_or_404()

    db.session.delete(relation)
    db.session.commit()

    return jsonify({'message': '数据库资源移除成功'})

@application_environment.route('/application-environments/search', methods=['GET'])
def search_application_environments():
    """
    通过系统英文名、系统中文名进行模糊查询

    Query Parameters:
        keyword (str): 关键字，用于匹配系统英文名或中文名
        page (int, optional): 页码，默认为1
        per_page (int, optional): 每页记录数，默认为20

    Returns:
        JSON: 应用环境列表和分页信息
    """
    keyword = request.args.get('keyword', '')
    page = int(request.args.get('page', 1))
    per_page = int(request.args.get('per_page', 20))

    if not keyword:
        return jsonify({'error': '缺少查询关键字'}), 400

    # 构建查询
    query = db.session.query(
        ApplicationEnvironment,
        Application,
        EnvironmentBase
    ).join(
        Application,
        ApplicationEnvironment.application_id == Application.id
    ).join(
        EnvironmentBase,
        ApplicationEnvironment.env_id == EnvironmentBase.id
    ).filter(
        or_(
            Application.app_en_name.like(f'%{keyword}%'),
            Application.app_cn_name.like(f'%{keyword}%')
        )
    )

    # 分页
    pagination = query.paginate(page=page, per_page=per_page, error_out=False)
    total = pagination.total

    # 构建结果
    results = []
    for app_env, app, env in pagination.items:
        # 获取部署方式名称
        deploy_methods = []
        if app_env.app_deploy_method:
            deploy_method_ids = app_env.app_deploy_method.split(',')
            for deploy_id in deploy_method_ids:
                if deploy_id:
                    try:
                        deploy = DeployMethod.query.get(int(deploy_id))
                        if deploy:
                            deploy_methods.append(deploy.deploy_name)
                    except ValueError:
                        # 忽略无效的部署方式ID
                        continue

        results.append({
            'id': app_env.id,
            'application_id': app_env.application_id,
            'app_en_name': app.app_en_name,
            'app_cn_name': app.app_cn_name,
            'env_id': app_env.env_id,
            'env_en_name': env.env_en_name,
            'env_cn_name': env.env_cn_name,
            'app_deploy_method': app_env.app_deploy_method,
            'deploy_method_names': deploy_methods,
            'app_env_url': app_env.app_env_url,
            'app_env_port': app_env.app_env_port,
            'app_env_status': app_env.app_env_status,
            'create_time': app_env.create_time.strftime('%Y-%m-%d %H:%M:%S') if app_env.create_time else None,
            'update_time': app_env.update_time.strftime('%Y-%m-%d %H:%M:%S') if app_env.update_time else None,
            'offline_time': app_env.offline_time.strftime('%Y-%m-%d %H:%M:%S') if app_env.offline_time else None,
            'app_env_remark': app_env.app_env_remark
        })

    return jsonify({
        'total': total,
        'page': page,
        'per_page': per_page,
        'pages': (total + per_page - 1) // per_page,
        'application_environments': results
    })

@application_environment.route('/application-environments/search-by-resource', methods=['GET'])
def search_by_resource():
    """
    通过资源IP查询应用环境

    Query Parameters:
        resource_type (str): 资源类型，可选值：lb（负载均衡）、server（服务器）、db（数据库）
        ip (str): 资源IP地址
        page (int, optional): 页码，默认为1
        per_page (int, optional): 每页记录数，默认为20

    Returns:
        JSON: 应用环境列表和分页信息
    """
    resource_type = request.args.get('resource_type', '')
    ip = request.args.get('ip', '')
    page = int(request.args.get('page', 1))
    per_page = int(request.args.get('per_page', 20))

    if not resource_type or not ip:
        return jsonify({'error': '缺少必填参数: resource_type, ip'}), 400

    if resource_type not in ['lb', 'server', 'db']:
        return jsonify({'error': 'resource_type参数值无效，可选值：lb、server、db'}), 400

    # 根据资源类型和IP查询应用环境
    if resource_type == 'lb':
        # 查询负载均衡资源
        lb_resources = LoadBalancerResource.query.filter_by(vip=ip).all()
        if not lb_resources:
            return jsonify({
                'total': 0,
                'page': page,
                'per_page': per_page,
                'pages': 0,
                'application_environments': []
            })

        # 获取关联的应用环境ID列表
        app_env_ids = []
        for lb in lb_resources:
            relations = ApplicationEnvLb.query.filter_by(lb_id=lb.id).all()
            for relation in relations:
                app_env_ids.append(relation.application_environment_id)
    elif resource_type == 'server':
        # 查询服务器资源
        servers = ServerResource.query.filter_by(ipv4=ip).all()
        if not servers:
            return jsonify({
                'total': 0,
                'page': page,
                'per_page': per_page,
                'pages': 0,
                'application_environments': []
            })

        # 获取关联的应用环境ID列表
        app_env_ids = []
        for server in servers:
            relations = ApplicationEnvServer.query.filter_by(server_id=server.id).all()
            for relation in relations:
                app_env_ids.append(relation.application_environment_id)
    else:  # db
        # 查询数据库资源
        databases = DatabaseResource.query.filter_by(db_ip=ip).all()
        if not databases:
            return jsonify({
                'total': 0,
                'page': page,
                'per_page': per_page,
                'pages': 0,
                'application_environments': []
            })

        # 获取关联的应用环境ID列表
        app_env_ids = []
        for db_resource in databases:
            relations = ApplicationEnvDatabase.query.filter_by(db_id=db_resource.id).all()
            for relation in relations:
                app_env_ids.append(relation.application_environment_id)

    if not app_env_ids:
        return jsonify({
            'total': 0,
            'page': page,
            'per_page': per_page,
            'pages': 0,
            'application_environments': []
        })

    # 构建查询
    query = db.session.query(
        ApplicationEnvironment,
        Application,
        EnvironmentBase
    ).join(
        Application,
        ApplicationEnvironment.application_id == Application.id
    ).join(
        EnvironmentBase,
        ApplicationEnvironment.env_id == EnvironmentBase.id
    ).filter(
        ApplicationEnvironment.id.in_(app_env_ids)
    )

    # 分页
    pagination = query.paginate(page=page, per_page=per_page, error_out=False)
    total = pagination.total

    # 构建结果
    results = []
    for app_env, app, env in pagination.items:
        # 获取部署方式名称
        deploy_methods = []
        if app_env.app_deploy_method:
            deploy_method_ids = app_env.app_deploy_method.split(',')
            for deploy_id in deploy_method_ids:
                if deploy_id:
                    try:
                        deploy = DeployMethod.query.get(int(deploy_id))
                        if deploy:
                            deploy_methods.append(deploy.deploy_name)
                    except ValueError:
                        # 忽略无效的部署方式ID
                        continue

        results.append({
            'id': app_env.id,
            'application_id': app_env.application_id,
            'app_en_name': app.app_en_name,
            'app_cn_name': app.app_cn_name,
            'env_id': app_env.env_id,
            'env_en_name': env.env_en_name,
            'env_cn_name': env.env_cn_name,
            'app_deploy_method': app_env.app_deploy_method,
            'deploy_method_names': deploy_methods,
            'app_env_url': app_env.app_env_url,
            'app_env_port': app_env.app_env_port,
            'app_env_status': app_env.app_env_status,
            'create_time': app_env.create_time.strftime('%Y-%m-%d %H:%M:%S') if app_env.create_time else None,
            'update_time': app_env.update_time.strftime('%Y-%m-%d %H:%M:%S') if app_env.update_time else None,
            'offline_time': app_env.offline_time.strftime('%Y-%m-%d %H:%M:%S') if app_env.offline_time else None,
            'app_env_remark': app_env.app_env_remark
        })

    return jsonify({
        'total': total,
        'page': page,
        'per_page': per_page,
        'pages': (total + per_page - 1) // per_page,
        'application_environments': results
    })