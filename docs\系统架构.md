# 系统架构

本文档描述了ICMS2应用程序的架构，包括其组件、结构以及它们如何交互。

## 概述

ICMS2遵循基于Flask框架的模块化架构。应用程序的结构旨在分离关注点并促进可维护性。

## 高级架构

```
┌─────────────────┐
│    客户端       │
│  (Web浏览器)    │
└────────┬────────┘
         │
         ▼
┌─────────────────┐
│   Flask应用     │
│                 │
│  ┌───────────┐  │
│  │ 蓝图      │  │
│  └───────────┘  │
│                 │
│  ┌───────────┐  │
│  │  模型     │  │
│  └───────────┘  │
│                 │
│  ┌───────────┐  │
│  │  配置     │  │
│  └───────────┘  │
└────────┬────────┘
         │
         ▼
┌─────────────────┐
│   数据库        │
│    (MySQL)      │
└─────────────────┘
```

## 组件结构

### 应用程序初始化

应用程序在`run.py`中初始化，它使用`app/__init__.py`中定义的工厂模式创建Flask应用程序。

### 配置

配置通过`config.py`文件管理，该文件为各种环境定义了不同的配置类：

- `DevelopmentConfig`：在开发期间使用，启用调试
- `TestingConfig`：用于运行测试
- `ProductionConfig`：在生产环境中使用，具有优化设置

### 蓝图

应用程序使用Flask蓝图组织路由：

- `auth`：认证相关路由（`app/routes/auth.py`）
- `role`：角色管理路由（`app/routes/role.py`）
- `module_menu`：模块和菜单管理路由（`app/routes/module_menu.py`）

### 模型

数据库模型在`app/models.py`中使用SQLAlchemy ORM定义：

- `User`：LDAP用户信息
- `LocalUser`：本地用户账户
- `Role`：用于权限管理的用户角色
- `Module`：应用程序模块
- `Menu`：模块内的菜单项
- `UserRole`：用户和角色之间的多对多关系
- `RoleModule`：角色和模块之间的多对多关系
- `Ldap`：LDAP服务器配置

## 认证流程

ICMS2支持两种认证方法：

1. **LDAP认证**：
   - 用户凭据通过LDAP服务器验证
   - 如果成功，用户信息将存储/更新在本地数据库中
   - 生成JWT令牌用于后续请求

2. **本地认证**：
   - 用户凭据通过本地数据库验证
   - 如果成功，生成JWT令牌用于后续请求

## 授权流程

1. 用户请求使用JWT令牌进行认证
2. 系统检索用户的角色
3. 根据角色，系统确定用户可以访问哪些模块和菜单
4. 相应地授予或拒绝访问

## 数据流

1. 客户端向服务器发送请求
2. 请求使用JWT令牌进行认证
3. 请求被路由到适当的蓝图处理程序
4. 处理程序通过SQLAlchemy模型与数据库交互
5. 生成响应并发送回客户端

## 目录结构

```
icms2/
├── app/                    # 应用程序包
│   ├── routes/             # 路由蓝图
│   │   ├── __init__.py     # 蓝图注册
│   │   ├── auth.py         # 认证路由
│   │   ├── role.py         # 角色管理路由
│   │   └── module_menu.py  # 模块和菜单路由
│   ├── __init__.py         # 应用程序工厂
│   └── models.py           # 数据库模型
├── docs/                   # 文档
├── scripts/                # 实用脚本
├── .venv/                  # 虚拟环境（不在版本控制中）
├── config.py               # 配置设置
├── requirements.txt        # Python依赖项
└── run.py                  # 应用程序入口点
```

## 技术栈

- **Web框架**：Flask
- **ORM**：SQLAlchemy
- **数据库**：MySQL
- **认证**：JWT（JSON Web令牌）
- **目录服务**：LDAP3库用于LDAP集成
- **CORS支持**：Flask-CORS用于跨源资源共享
