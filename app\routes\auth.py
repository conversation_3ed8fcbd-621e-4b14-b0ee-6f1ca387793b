from flask import Blueprint, request, jsonify, current_app, session, g
from .. import db, beijing_tz
from ..models import (
    User, Role, Module, Menu, UserRole, RoleModule,
    Ldap, LocalUser, get_beijing_time
)
import jwt
from datetime import datetime, timedelta, timezone
from functools import wraps
from werkzeug.security import check_password_hash
# 添加 LDAP 相关导入
from ldap3 import Server, Connection, ALL, SUBTREE
# 导入日志装饰器
from ..utils.decorators import log_route, log_operation

# 创建 Blueprint
auth = Blueprint('auth', __name__)

# 添加 token 验证装饰器
def token_required(f):
    @wraps(f)
    def decorated(*args, **kwargs):
        token = None

        # 从请求头或 cookie 中获取 token
        auth_header = request.headers.get('Authorization')
        if auth_header and auth_header.startswith('Bearer '):
            token = auth_header.split(' ')[1]

        if not token:
            token = request.cookies.get('token')

        if not token:
            return jsonify({'error': '未提供认证令牌'}), 401

        try:
            # 解码 token
            payload = jwt.decode(
                token,
                current_app.config.get('SECRET_KEY'),
                algorithms=['HS256']
            )

            # 获取用户信息
            user_id = payload['user_id']
            user_type = payload.get('user_type', 'ldap')

            if user_type == 'local':
                user = LocalUser.query.get(user_id)
            else:
                user = User.query.get(user_id)

            if not user:
                return jsonify({'error': '无效的用户'}), 401

            # 检查用户是否被禁用
            if hasattr(user, 'is_active') and not user.is_active:
                return jsonify({'error': '用户已被禁用'}), 401

            # 将用户信息存储到g对象中，以便日志装饰器能够正确记录用户信息
            g.user = user
            g.user_type = user_type
            g.username = user.username

        except jwt.ExpiredSignatureError:
            return jsonify({'error': '认证令牌已过期'}), 401
        except jwt.InvalidTokenError:
            return jsonify({'error': '无效的认证令牌'}), 401

        return f(user, *args, **kwargs)
    return decorated

@auth.route('/validate-token', methods=['GET'])
@log_route
def validate_token():
    token = None

    # 从请求头或 cookie 中获取 token
    auth_header = request.headers.get('Authorization')
    if auth_header and auth_header.startswith('Bearer '):
        token = auth_header.split(' ')[1]
        current_app.logger.debug(f"从Authorization头获取到token: {token[:10]}...")

    if not token:
        token = request.cookies.get('token')
        if token:
            current_app.logger.debug(f"从Cookie获取到token: {token[:10]}...")
        else:
            current_app.logger.debug("Cookie中没有找到token")

    # 记录请求中的所有Cookie
    current_app.logger.debug(f"请求中的所有Cookie: {request.cookies}")

    if not token:
        current_app.logger.warning("validate-token接口未提供认证令牌")
        return jsonify({'valid': False, 'error': '未提供认证令牌'}), 401

    try:
        # 解码 token
        payload = jwt.decode(
            token,
            current_app.config.get('SECRET_KEY'),
            algorithms=['HS256']
        )

        # 获取用户信息
        user_id = payload['user_id']
        user_type = payload.get('user_type', 'ldap')

        current_app.logger.debug(f"从token中获取到用户信息: user_id={user_id}, user_type={user_type}")

        if user_type == 'local':
            user = LocalUser.query.get(user_id)
        else:
            user = User.query.get(user_id)

        if not user:
            current_app.logger.warning(f"无效的用户: user_id={user_id}, user_type={user_type}")
            return jsonify({'valid': False, 'error': '无效的用户'}), 401

        # 检查用户是否被禁用
        if hasattr(user, 'is_active') and not user.is_active:
            current_app.logger.warning(f"用户已被禁用: user_id={user_id}, username={user.username}")
            return jsonify({'valid': False, 'error': '用户已被禁用'}), 401

        # 将用户信息存储到g对象中，以便日志装饰器能够正确记录用户信息
        g.user = user
        g.user_type = user_type
        g.username = user.username

        current_app.logger.debug(f"用户信息已设置到g对象: username={user.username}, user_id={user.id}")

        # 获取用户权限
        permissions = get_user_permissions_data(user_id, user_type)

        # 如果 token 即将过期，生成新 token
        now = datetime.now(timezone.utc)
        exp_time = datetime.fromtimestamp(payload['exp'], tz=timezone.utc)

        # 如果 token 还有不到 30 分钟过期，生成新 token
        new_token = None
        if (exp_time - now) < timedelta(minutes=30):
            new_token = generate_token(user, user_type)

        response = jsonify({
            'valid': True,
            'user': {
                'id': user.id,
                'username': user.username,
                'display_name': user.display_name,
                'is_local_admin': user_type == 'local'
            },
            'permissions': permissions,
            'new_token': new_token
        })

        # 如果生成了新 token，更新 cookie
        if new_token:
            response.set_cookie(
                'token',
                new_token,
                max_age=86400,  # 24小时
                httponly=True,
                secure=request.is_secure,  # 在 HTTPS 下设置 secure
                samesite='None' if request.is_secure else 'Lax',  # 跨域请求需要 SameSite=None
                path='/'  # 确保 Cookie 在所有路径下可用
            )

        return response

    except jwt.ExpiredSignatureError:
        return jsonify({'valid': False, 'error': '认证令牌已过期'}), 401
    except jwt.InvalidTokenError:
        return jsonify({'valid': False, 'error': '无效的认证令牌'}), 401

# 添加生成 token 的函数
def generate_token(user, user_type='ldap'):
    # 设置 token 有效期为 24 小时
    expiration = datetime.now(timezone.utc) + timedelta(hours=24)

    payload = {
        'user_id': user.id,
        'user_type': user_type,
        'exp': expiration
    }

    token = jwt.encode(
        payload,
        current_app.config.get('SECRET_KEY'),
        algorithm='HS256'
    )

    return token

# 修改登录接口，使用 token 认证
@auth.route('/login', methods=['POST'])
@log_route
def login():
    data = request.get_json()
    username = data.get('username')
    password = data.get('password')
    client_ip = request.remote_addr  # 获取客户端 IP

    # 步骤 1：检查是否为本地用户
    local_user = LocalUser.query.filter_by(username=username).first()
    if local_user:
        if not local_user.is_active:
            current_app.logger.info(f"Login attempt failed: user={username}, ip={client_ip}, reason=User disabled")
            return jsonify({'error': '本地用户已被禁用'}), 401
        if check_password_hash(local_user.password_hash, password):
            # 获取用户权限信息 - 使用本地用户ID和类型
            permissions = get_user_permissions_data(local_user.id, 'local')

            # 生成 token
            token = generate_token(local_user, 'local')

            # 设置用户信息到g对象，以便日志装饰器能够正确记录用户信息
            g.user = local_user
            g.user_type = 'local'
            g.username = local_user.username

            current_app.logger.info(f"Login successful: user={username}, ip={client_ip}, type=Local")

            # 手动记录操作成功日志
            current_app.logger.info(f"操作成功: 类型=用户登录, endpoint=auth.login, method=POST, client_ip={client_ip}, username={local_user.username}, user_id={local_user.id}")

            response = jsonify({
                'id': local_user.id,  # 使用local_user的ID
                'username': local_user.username,
                'display_name': local_user.display_name,
                'is_local_admin': True,
                'permissions': permissions,
                'token': token
            })

            # 设置 cookie，有效期 24 小时
            response.set_cookie(
                'token',
                token,
                max_age=86400,  # 24小时
                httponly=True,
                secure=request.is_secure,  # 在 HTTPS 下设置 secure
                samesite='None' if request.is_secure else 'Lax',  # 跨域请求需要 SameSite=None
                path='/'  # 确保 Cookie 在所有路径下可用
            )

            return response
        else:
            current_app.logger.info(f"Login attempt failed: user={username}, ip={client_ip}, reason=Wrong password")
            return jsonify({'error': '本地用户密码错误'}), 401

    # 步骤 2：获取所有LDAP配置，优先使用活跃的配置
    # 首先获取活跃的LDAP配置
    active_ldap_configs = Ldap.query.filter_by(is_active=True).all()

    # 如果没有活跃的配置，获取所有配置
    if not active_ldap_configs:
        current_app.logger.info(f"No active LDAP configs found, trying all configs")
        active_ldap_configs = Ldap.query.all()

    if not active_ldap_configs:
        current_app.logger.info(f"Login attempt failed: user={username}, ip={client_ip}, reason=No LDAP configs")
        return jsonify({'error': '未配置LDAP，请联系管理员'}), 400

    # 步骤 3：尝试每个LDAP配置
    for ldap_config in active_ldap_configs:
        current_app.logger.info(f"Trying LDAP config: id={ldap_config.id}, server={ldap_config.server}")

        try:
            server = Server(ldap_config.server, port=ldap_config.port, get_info=ALL)
            conn = Connection(server, user=ldap_config.bind_dn, password=ldap_config.password)

            if not conn.bind():
                current_app.logger.info(f"LDAP connection failed for config id={ldap_config.id}: {conn.result}")
                continue  # 尝试下一个配置

            # 使用配置的 ou_path 限定搜索范围
            search_base = ldap_config.ou_path

            # 使用更通用的搜索过滤器，同时查找cn和sAMAccountName
            search_filter = f'(|(cn={username})(sAMAccountName={username}))'
            current_app.logger.info(f"LDAP search: config_id={ldap_config.id}, base={search_base}, filter={search_filter}")

            # 使用SUBTREE搜索范围，递归搜索所有子OU
            conn.search(
                search_base,
                search_filter,
                search_scope=SUBTREE,
                attributes=['cn', 'sAMAccountName', 'displayName', 'mail']
            )

            if conn.entries:
                # 找到用户，处理登录
                entry = conn.entries[0]

                # 尝试使用用户提供的密码进行身份验证
                user_dn = entry.entry_dn
                user_conn = Connection(server, user=user_dn, password=password)

                if not user_conn.bind():
                    current_app.logger.info(f"Login attempt failed: user={username}, ip={client_ip}, reason=Wrong password")
                    return jsonify({'error': '密码错误'}), 401

                # 身份验证成功，获取用户信息
                actual_username = entry.sAMAccountName.value if hasattr(entry, 'sAMAccountName') else entry.cn.value
                display_name = entry.displayName.value if hasattr(entry, 'displayName') else actual_username
                email = entry.mail.value if hasattr(entry, 'mail') else f'{actual_username}@example.com'

                # 检查用户是否存在，插入或更新
                user = User.query.filter_by(username=actual_username).first()
                if not user:
                    user = User(username=actual_username, display_name=display_name, email=email)
                    db.session.add(user)
                    db.session.commit()

                # 获取用户权限
                permissions = get_user_permissions_data(user.id)

                # 生成 token
                token = generate_token(user, 'ldap')

                # 设置用户信息到g对象，以便日志装饰器能够正确记录用户信息
                g.user = user
                g.user_type = 'ldap'
                g.username = user.username

                current_app.logger.info(f"Login successful: user={actual_username}, ip={client_ip}, type=LDAP, config_id={ldap_config.id}")

                # 手动记录操作成功日志
                current_app.logger.info(f"操作成功: 类型=用户登录, endpoint=auth.login, method=POST, client_ip={client_ip}, username={user.username}, user_id={user.id}")

                response = jsonify({
                    'id': user.id,
                    'username': user.username,
                    'display_name': user.display_name,
                    'is_local_admin': False,
                    'permissions': permissions,
                    'token': token
                })

                # 设置 cookie，有效期 24 小时
                response.set_cookie(
                    'token',
                    token,
                    max_age=86400,  # 24小时
                    httponly=True,
                    secure=request.is_secure,  # 在 HTTPS 下设置 secure
                    samesite='None' if request.is_secure else 'Lax',  # 跨域请求需要 SameSite=None
                    path='/'  # 确保 Cookie 在所有路径下可用
                )

                return response
        except Exception as e:
            current_app.logger.error(f"Error during LDAP authentication for config id={ldap_config.id}: {str(e)}")
            continue  # 尝试下一个配置

    # 如果所有LDAP配置都尝试失败，返回错误
    current_app.logger.info(f"Login attempt failed: user={username}, ip={client_ip}, reason=User not found in any LDAP")
    return jsonify({'error': '用户不存在或密码错误'}), 401


@auth.route('/ldap-config', methods=['GET', 'POST'])
@log_route
def save_ldap_config():
    if request.method == 'GET':
        # Return all LDAP configs instead of just the active one
        ldap_configs = Ldap.query.all()
        return jsonify([{
            'id': config.id,
            'server': config.server,
            'port': config.port,
            'bind_dn': config.bind_dn,
            'ou_path': config.ou_path,
            'is_active': config.is_active
        } for config in ldap_configs])

    # POST method handling - create new LDAP config
    data = request.get_json()
    if not data or not all(k in data for k in ['server', 'port', 'bind_dn', 'password', 'ou_path']):
        return jsonify({'error': '参数缺失'}), 400

    # Create new LDAP config
    ldap_config = Ldap(
        server=data['server'],
        port=data['port'],
        bind_dn=data['bind_dn'],
        password=data['password'],
        ou_path=data['ou_path'],
        is_active=data.get('is_active', True)
    )

    try:
        db.session.add(ldap_config)
        db.session.commit()
        current_app.logger.info(f"New LDAP config created: server={ldap_config.server}")
        return jsonify({
            'id': ldap_config.id,
            'message': 'LDAP 配置保存成功'
        })
    except Exception as e:
        db.session.rollback()
        error_msg = str(e)
        current_app.logger.error(f"Failed to create LDAP config: {error_msg}")

        # Check for unique constraint violation
        if "Duplicate entry" in error_msg and "unique_ou_path" in error_msg:
            print("Duplicate entry detected")
            return jsonify({'error': 'LDAP配置已存在，服务器、端口和绑定DN的组合必须唯一'}), 400

        return jsonify({'error': f'保存LDAP配置失败: {error_msg}'}), 500


@auth.route('/users', methods=['GET'])
@log_route
def get_users():
    username = request.args.get('username', '')
    query = User.query
    if username:
        query = query.filter(User.username.ilike(f'%{username}%'))
    users = query.all()
    return jsonify([
        {
            'id': user.id,
            'username': user.username,
            'display_name': user.display_name,
            'roles': [role.role_name for role in user.roles]
        } for user in users
    ])


@auth.route('/users/<int:user_id>/roles', methods=['PUT'])
@log_route
@log_operation('分配用户角色')
def assign_roles(user_id):
    data = request.get_json()
    roles = data.get('roles', [])
    user_type = data.get('user_type', 'ldap')  # 默认为LDAP用户

    # 验证用户存在
    if user_type == 'local':
        user = LocalUser.query.get_or_404(user_id)
    else:
        user = User.query.get_or_404(user_id)

    # 删除现有角色
    UserRole.query.filter_by(user_id=user_id, user_type=user_type).delete()

    # 分配新角色
    for role_id in roles:
        user_role = UserRole(user_id=user_id, user_type=user_type, role_id=role_id)
        db.session.add(user_role)

    db.session.commit()
    current_app.logger.info(f"User roles updated: user_id={user_id}, user_type={user_type}, roles={roles}")
    return jsonify({'message': '角色分配成功'})

@auth.route('/ldap-config/<int:config_id>', methods=['PUT', 'DELETE'])
@log_route
@log_operation('管理LDAP配置')
def manage_ldap_config(config_id):
    # Get the LDAP config by ID
    ldap_config = Ldap.query.get_or_404(config_id)

    if request.method == 'PUT':
        # Update LDAP configuration
        data = request.get_json()
        if not data:
            return jsonify({'error': '无效的请求数据'}), 400

        # Update fields if provided
        if 'server' in data:
            ldap_config.server = data['server']
        if 'port' in data:
            ldap_config.port = data['port']
        if 'bind_dn' in data:
            ldap_config.bind_dn = data['bind_dn']
        if 'password' in data:
            ldap_config.password = data['password']
        if 'ou_path' in data:
            ldap_config.ou_path = data['ou_path']
        if 'is_active' in data:
            ldap_config.is_active = data['is_active']

        ldap_config.update_time = get_beijing_time()
        db.session.commit()

        current_app.logger.info(f"LDAP config updated: id={config_id}")
        return jsonify({'message': 'LDAP 配置更新成功'})

    elif request.method == 'DELETE':
        # Delete LDAP configuration
        db.session.delete(ldap_config)
        db.session.commit()

        current_app.logger.info(f"LDAP config deleted: id={config_id}")
        return jsonify({'message': 'LDAP 配置删除成功'})

@auth.route('/ldap-configs', methods=['GET'])
@log_route
def get_all_ldap_configs():
    # Get all LDAP configurations
    ldap_configs = Ldap.query.all()

    return jsonify([{
        'id': config.id,
        'server': config.server,
        'port': config.port,
        'bind_dn': config.bind_dn,
        'ou_path': config.ou_path,
        'is_active': config.is_active,
        'create_time': config.create_time.isoformat() if config.create_time else None,
        'update_time': config.update_time.isoformat() if config.update_time else None
    } for config in ldap_configs])

@auth.route('/ldap-fetch-users', methods=['POST'])
@log_route
@log_operation('获取LDAP用户')
def fetch_ldap_users():
    data = request.get_json()
    print(data)
    # 通过ID获取LDAP配置
    ldap_config = Ldap.query.get_or_404(data['config_id'])

    try:
        # 连接LDAP服务器
        server = Server(ldap_config.server, port=ldap_config.port, get_info=ALL)
        conn = Connection(server, user=ldap_config.bind_dn, password=ldap_config.password)

        if not conn.bind():
            current_app.logger.error(f"LDAP服务器连接失败: {conn.result}")
            return jsonify({'error': 'LDAP服务器连接失败'}), 500

        # 搜索用户
        ou_path = ldap_config.ou_path
        current_app.logger.info(f"正在搜索OU中的用户: {ou_path}")
        conn.search(
            ou_path,
            '(&(objectClass=user)(objectCategory=person))',
            search_scope=SUBTREE,
            attributes=['sAMAccountName', 'displayName', 'mail']
        )

        if not conn.entries:
            current_app.logger.info(f"在OU中未找到用户: {ou_path}")
            return jsonify({'message': '未找到用户', 'count': 0}), 200

        # 处理结果
        users_added = 0
        users_updated = 0

        for entry in conn.entries:
            sam_account_name = entry.sAMAccountName.value if hasattr(entry, 'sAMAccountName') else None
            display_name = entry.displayName.value if hasattr(entry, 'displayName') else None
            email = entry.mail.value if hasattr(entry, 'mail') else None

            if not sam_account_name:
                continue

            # 设置默认值
            if not display_name:
                display_name = sam_account_name
            if not email:
                email = f"{sam_account_name}@example.com"

            # 检查用户是否已存在
            user = User.query.filter_by(username=sam_account_name).first()

            if user:
                # 更新现有用户
                user.display_name = display_name
                user.email = email
                users_updated += 1
            else:
                # 创建新用户
                new_user = User(
                    username=sam_account_name,
                    display_name=display_name,
                    email=email
                )
                db.session.add(new_user)
                users_added += 1

        db.session.commit()
        current_app.logger.info(f"LDAP用户导入完成: 新增 {users_added} 个, 更新 {users_updated} 个")

        return jsonify({
            'message': '用户导入成功',
            'added': users_added,
            'updated': users_updated,
            'total': users_added + users_updated
        })

    except Exception as e:
        current_app.logger.error(f"获取LDAP用户时出错: {str(e)}")
        db.session.rollback()
        return jsonify({'error': f'获取LDAP用户失败: {str(e)}'}), 500

@auth.route('/ldap-test-search', methods=['POST'])
def test_ldap_search():
    data = request.get_json()
    config_id = data.get('config_id')
    test_username = data.get('username')

    if not config_id or not test_username:
        return jsonify({'error': '参数缺失'}), 400

    # 获取LDAP配置
    ldap_config = Ldap.query.get_or_404(config_id)

    try:
        # 连接LDAP服务器
        server = Server(ldap_config.server, port=ldap_config.port, get_info=ALL)
        conn = Connection(server, user=ldap_config.bind_dn, password=ldap_config.password)

        if not conn.bind():
            return jsonify({'error': f'LDAP连接失败: {conn.result}'}), 500

        # 搜索用户
        search_base = ldap_config.ou_path
        search_filter = f'(|(cn={test_username})(sAMAccountName={test_username}))'

        conn.search(
            search_base,
            search_filter,
            search_scope=SUBTREE,
            attributes=['cn', 'sAMAccountName', 'displayName', 'mail']
        )

        if not conn.entries:
            return jsonify({'found': False, 'message': f'在LDAP中未找到用户: {test_username}'}), 200

        # 返回找到的用户信息
        user_entries = []
        for entry in conn.entries:
            user_data = {}
            if hasattr(entry, 'cn'):
                user_data['cn'] = entry.cn.value
            if hasattr(entry, 'sAMAccountName'):
                user_data['sAMAccountName'] = entry.sAMAccountName.value
            if hasattr(entry, 'displayName'):
                user_data['displayName'] = entry.displayName.value
            if hasattr(entry, 'mail'):
                user_data['mail'] = entry.mail.value

            user_entries.append(user_data)

        return jsonify({
            'found': True,
            'count': len(user_entries),
            'users': user_entries
        })

    except Exception as e:
        current_app.logger.error(f"LDAP测试搜索出错: {str(e)}")
        return jsonify({'error': f'LDAP测试搜索失败: {str(e)}'}), 500

@auth.route('/users/<int:user_id>/roles', methods=['GET', 'PUT'])
def manage_user_roles(user_id):
    # 检查用户是否存在
    user = User.query.get_or_404(user_id)

    if request.method == 'GET':
        # 获取用户当前的角色
        user_roles = UserRole.query.filter_by(user_id=user_id).all()
        role_ids = [ur.role_id for ur in user_roles]

        # 获取所有角色信息
        all_roles = Role.query.all()

        return jsonify({
            'user': {
                'id': user.id,
                'username': user.username,
                'display_name': user.display_name
            },
            'assigned_roles': role_ids,
            'all_roles': [
                {
                    'id': role.id,
                    'role_name': role.role_name,
                    'description': role.description
                } for role in all_roles
            ]
        })

    elif request.method == 'PUT':
        data = request.get_json()
        if not data or 'roles' not in data:
            return jsonify({'error': '角色数据不能为空'}), 400

        # 删除用户现有的角色
        UserRole.query.filter_by(user_id=user_id).delete()

        # 分配新角色
        for role_id in data['roles']:
            # 检查角色是否存在
            role = Role.query.get(role_id)
            if role:
                user_role = UserRole(user_id=user_id, role_id=role_id)
                db.session.add(user_role)

        db.session.commit()
        current_app.logger.info(f"User roles updated: user_id={user_id}, roles={data['roles']}")
        return jsonify({'message': '用户角色分配成功'})

@auth.route('/users/permissions', methods=['GET'])
def get_user_permissions_summary():
    """获取所有用户的权限汇总信息"""
    users = User.query.all()
    result = []

    for user in users:
        # 获取用户的角色
        roles = [role.role_name for role in user.roles]

        # 获取用户的模块权限和菜单权限
        modules = set()
        menus = set()

        # 遍历用户的所有角色
        for role in user.roles:
            # 获取角色的权限记录
            role_module = RoleModule.query.filter_by(role_id=role.id).first()
            if role_module:
                # 将逗号分隔的ID字符串转换为列表
                module_ids = RoleModule.str_to_list(role_module.module_id)
                menu_ids = RoleModule.str_to_list(role_module.menu_id)

                # 获取模块名称
                for module_id in module_ids:
                    module = Module.query.get(module_id)
                    if module:
                        modules.add(module.display_name)

                # 获取菜单名称
                for menu_id in menu_ids:
                    menu = Menu.query.get(menu_id)
                    if menu:
                        menus.add(menu.menu_display_name)

        result.append({
            'id': user.id,
            'username': user.username,
            'display_name': user.display_name,
            'roles': roles,
            'modules': list(modules),
            'menus': list(menus)
        })

    return jsonify(result)

@auth.route('/users/<int:user_id>/permissions', methods=['GET'])
def get_user_permissions(user_id):
    """获取特定用户的权限详情，包括模块和菜单的树形结构"""
    user = User.query.get_or_404(user_id)

    # 获取用户的角色
    roles = []
    for role in user.roles:
        roles.append({
            'id': role.id,
            'role_name': role.role_name
        })

    # 获取用户的模块和菜单权限
    modules = {}

    # 遍历用户的所有角色
    for role in user.roles:
        # 获取角色的权限记录
        role_module = RoleModule.query.filter_by(role_id=role.id).first()
        if role_module:
            # 将逗号分隔的ID字符串转换为列表
            module_ids = RoleModule.str_to_list(role_module.module_id)
            menu_ids = RoleModule.str_to_list(role_module.menu_id)

            # 获取模块信息
            for module_id in module_ids:
                module = Module.query.get(module_id)
                if module:
                    if module.id not in modules:
                        modules[module.id] = {
                            'id': module.id,
                            'name': module.display_name,
                            'menus': []
                        }

            # 获取菜单信息并关联到对应模块
            for menu_id in menu_ids:
                menu = Menu.query.get(menu_id)
                if menu and menu.module_id in module_ids:
                    # 检查菜单是否已添加
                    menu_exists = False
                    for m in modules[menu.module_id]['menus']:
                        if m['id'] == menu.id:
                            menu_exists = True
                            break

                    if not menu_exists:
                        modules[menu.module_id]['menus'].append({
                            'id': menu.id,
                            'name': menu.menu_display_name
                        })

    return jsonify({
        'user': {
            'id': user.id,
            'username': user.username,
            'display_name': user.display_name
        },
        'roles': roles,
        'modules': list(modules.values())
    })

# 辅助函数：获取用户权限数据
def get_user_permissions_data(user_id, user_type='ldap'):
    """获取用户的权限数据，包括角色、模块和菜单

    Args:
        user_id: 用户ID
        user_type: 用户类型，'ldap'或'local'
    """
    # 根据用户类型获取用户对象
    if user_type == 'local':
        user = LocalUser.query.get(user_id)
    else:
        user = User.query.get(user_id)

    if not user:
        return {'roles': [], 'modules': []}

    # 获取用户的角色
    roles = []
    user_roles = UserRole.query.filter_by(user_id=user_id, user_type=user_type).all()

    for user_role in user_roles:
        role = Role.query.get(user_role.role_id)
        if role:
            roles.append({
                'id': role.id,
                'role_name': role.role_name
            })

    # 获取用户的模块和菜单权限
    modules = {}

    # 遍历用户的所有角色
    for user_role in user_roles:
        role = Role.query.get(user_role.role_id)
        if not role:
            continue

        # 获取角色的权限记录
        role_module = RoleModule.query.filter_by(role_id=role.id).first()
        if not role_module:
            continue

        # 将逗号分隔的ID字符串转换为列表
        module_ids = RoleModule.str_to_list(role_module.module_id)
        menu_ids = RoleModule.str_to_list(role_module.menu_id)

        # 获取模块信息
        for module_id in module_ids:
            module = Module.query.get(module_id)
            if not module:
                continue

            if module.id not in modules:
                modules[module.id] = {
                    'id': module.id,
                    'name': module.display_name,
                    'module_name': module.module_name,
                    'menus': []
                }

            # 获取菜单信息并关联到对应模块
            for menu_id in menu_ids:
                menu = Menu.query.get(menu_id)
                if menu and menu.module_id == module.id:
                    # 检查菜单是否已添加
                    menu_exists = False
                    for m in modules[module.id]['menus']:
                        if m['id'] == menu.id:
                            menu_exists = True
                            break

                    if not menu_exists:
                        modules[module.id]['menus'].append({
                            'id': menu.id,
                            'name': menu.menu_display_name,
                            'menu_name': menu.menu_name
                        })

    return {
        'roles': roles,
        'modules': list(modules.values())
    }

@auth.route('/users/batch-roles', methods=['POST'])
def batch_assign_roles():
    """批量为用户分配角色"""
    data = request.get_json()
    if not data or 'users' not in data or 'roles' not in data:
        return jsonify({'error': '参数缺失'}), 400

    user_ids = data.get('users', [])
    role_ids = data.get('roles', [])

    if not user_ids or not role_ids:
        return jsonify({'error': '用户ID列表或角色ID列表不能为空'}), 400

    # 验证所有用户和角色是否存在
    users = User.query.filter(User.id.in_(user_ids)).all()
    roles = Role.query.filter(Role.id.in_(role_ids)).all()

    if len(users) != len(user_ids):
        return jsonify({'error': '部分用户ID不存在'}), 400

    if len(roles) != len(role_ids):
        return jsonify({'error': '部分角色ID不存在'}), 400

    # 批量分配角色
    success_count = 0
    for user_id in user_ids:
        # 删除用户现有的角色
        UserRole.query.filter_by(user_id=user_id).delete()

        # 分配新角色
        for role_id in role_ids:
            user_role = UserRole(user_id=user_id, role_id=role_id)
            db.session.add(user_role)

        success_count += 1

    db.session.commit()
    current_app.logger.info(f"Batch role assignment: users={user_ids}, roles={role_ids}")

    return jsonify({
        'message': f'成功为{success_count}个用户分配角色',
        'success_count': success_count
    })
