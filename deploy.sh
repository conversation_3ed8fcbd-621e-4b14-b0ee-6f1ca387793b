#!/bin/bash

# ICMS2 部署脚本

# 确保脚本在错误时退出
set -e

# 显示帮助信息
show_help() {
    echo "ICMS2 部署脚本"
    echo "用法: $0 [选项]"
    echo "选项:"
    echo "  -h, --help        显示帮助信息"
    echo "  -e, --env         设置环境 (development, production, testing)"
    echo "  -b, --build       构建Docker镜像"
    echo "  -r, --restart     重启服务"
    echo "  -d, --down        停止并移除容器"
    echo "  -l, --logs        查看应用日志"
    echo "  --backup          备份数据库"
    echo "  --restore FILE    从FILE恢复数据库"
    echo ""
    echo "示例:"
    echo "  $0 -e production -b    # 构建生产环境"
    echo "  $0 -r                  # 重启服务"
    echo "  $0 -l                  # 查看日志"
}

# 默认值
ENV="production"
BUILD=false
RESTART=false
DOWN=false
LOGS=false
BACKUP=false
RESTORE=false
RESTORE_FILE=""

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -e|--env)
            ENV="$2"
            shift 2
            ;;
        -b|--build)
            BUILD=true
            shift
            ;;
        -r|--restart)
            RESTART=true
            shift
            ;;
        -d|--down)
            DOWN=true
            shift
            ;;
        -l|--logs)
            LOGS=true
            shift
            ;;
        --backup)
            BACKUP=true
            shift
            ;;
        --restore)
            RESTORE=true
            RESTORE_FILE="$2"
            shift 2
            ;;
        *)
            echo "未知选项: $1"
            show_help
            exit 1
            ;;
    esac
done

# 检查环境文件
if [ ! -f .env ]; then
    echo "未找到.env文件，从示例创建..."
    cp .env.example .env
    echo "请编辑.env文件设置您的环境变量"
fi

# 设置环境变量
export FLASK_CONFIG=$ENV

# 执行操作
if [ "$DOWN" = true ]; then
    echo "停止并移除容器..."
    docker-compose down
    exit 0
fi

if [ "$BACKUP" = true ]; then
    echo "备份数据库..."
    BACKUP_FILE="backup_$(date +%Y%m%d_%H%M%S).sql"
    docker-compose exec -T db sh -c 'mysqldump -u root -p"$MYSQL_ROOT_PASSWORD" "$MYSQL_DATABASE"' > $BACKUP_FILE
    echo "数据库已备份到 $BACKUP_FILE"
    exit 0
fi

if [ "$RESTORE" = true ]; then
    if [ ! -f "$RESTORE_FILE" ]; then
        echo "错误: 备份文件 $RESTORE_FILE 不存在"
        exit 1
    fi
    echo "从 $RESTORE_FILE 恢复数据库..."
    cat $RESTORE_FILE | docker-compose exec -T db sh -c 'mysql -u root -p"$MYSQL_ROOT_PASSWORD" "$MYSQL_DATABASE"'
    echo "数据库恢复完成"
    exit 0
fi

if [ "$BUILD" = true ]; then
    echo "构建Docker镜像..."
    docker-compose build
fi

if [ "$RESTART" = true ] || [ "$BUILD" = true ]; then
    echo "重启服务..."
    docker-compose down
    docker-compose up -d
fi

if [ "$LOGS" = true ]; then
    echo "显示应用日志..."
    docker-compose logs -f app
fi

# 如果没有指定操作，启动服务
if [ "$BUILD" = false ] && [ "$RESTART" = false ] && [ "$LOGS" = false ]; then
    echo "启动服务..."
    docker-compose up -d
    echo "服务已启动，使用 '$0 -l' 查看日志"
fi
