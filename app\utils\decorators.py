"""
装饰器工具模块，提供各种装饰器
"""
import functools
import time
import logging
from flask import request, g, current_app, has_request_context
import traceback
from datetime import datetime
import pytz

# 定义北京时区
beijing_tz = pytz.timezone('Asia/Shanghai')

def log_route(func):
    """
    装饰器：记录路由函数的执行情况

    Args:
        func: 要装饰的路由函数

    Returns:
        装饰后的函数
    """
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        # 获取请求信息
        if not has_request_context():
            return func(*args, **kwargs)

        client_ip = request.remote_addr

        # 获取用户信息
        if hasattr(g, 'user') and g.user:
            user_id = getattr(g.user, 'id', 'unknown')
            username = getattr(g.user, 'username', 'unknown')
        elif hasattr(g, 'username'):
            user_id = 'unknown'
            username = g.username
        else:
            user_id = 'unknown'
            username = 'unknown'

        endpoint = request.endpoint
        method = request.method
        url = request.url

        # 记录请求开始
        start_time = time.time()
        current_app.logger.info(f"请求开始: endpoint={endpoint}, method={method}, url={url}, client_ip={client_ip}, username={username}, user_id={user_id}")

        try:
            # 执行路由函数
            response = func(*args, **kwargs)

            # 计算执行时间
            execution_time = time.time() - start_time

            # 记录请求结束
            status_code = getattr(response, 'status_code', 200)
            current_app.logger.info(
                f"请求结束: endpoint={endpoint}, method={method}, status={status_code}, "
                f"time={execution_time:.3f}s, client_ip={client_ip}, username={username}, user_id={user_id}"
            )

            return response
        except Exception as e:
            # 计算执行时间
            execution_time = time.time() - start_time

            # 获取异常堆栈
            stack_trace = traceback.format_exc()

            # 记录异常信息
            current_app.logger.error(
                f"请求异常: endpoint={endpoint}, method={method}, time={execution_time:.3f}s, "
                f"client_ip={client_ip}, username={username}, user_id={user_id}, error={str(e)}\n{stack_trace}"
            )

            # 重新抛出异常，让上层处理
            raise

    return wrapper

def log_operation(operation_type):
    """
    装饰器：记录业务操作

    Args:
        operation_type: 操作类型，如'创建'、'更新'、'删除'等

    Returns:
        装饰器函数
    """
    def decorator(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            # 获取请求信息
            if not has_request_context():
                return func(*args, **kwargs)

            client_ip = request.remote_addr

            # 获取用户信息
            if hasattr(g, 'user') and g.user:
                user_id = getattr(g.user, 'id', 'unknown')
                username = getattr(g.user, 'username', 'unknown')
            elif hasattr(g, 'username'):
                user_id = 'unknown'
                username = g.username
            else:
                user_id = 'unknown'
                username = 'unknown'

            endpoint = request.endpoint
            method = request.method

            # 获取请求数据
            request_data = {}
            if request.is_json:
                try:
                    json_data = request.get_json()
                    if json_data is not None:
                        request_data = json_data
                except Exception:
                    # 如果JSON解析失败，忽略请求体数据
                    pass
            elif request.form:
                request_data = request.form.to_dict()
            elif request.args:
                request_data = request.args.to_dict()

            # 记录操作开始
            current_app.logger.info(
                f"操作开始: 类型={operation_type}, endpoint={endpoint}, method={method}, "
                f"client_ip={client_ip}, username={username}, user_id={user_id}, 数据={request_data}"
            )

            try:
                # 执行函数
                response = func(*args, **kwargs)

                # 记录操作成功
                current_app.logger.info(
                    f"操作成功: 类型={operation_type}, endpoint={endpoint}, method={method}, "
                    f"client_ip={client_ip}, username={username}, user_id={user_id}"
                )

                return response
            except Exception as e:
                # 获取异常堆栈
                stack_trace = traceback.format_exc()

                # 记录操作失败
                current_app.logger.error(
                    f"操作失败: 类型={operation_type}, endpoint={endpoint}, method={method}, "
                    f"client_ip={client_ip}, username={username}, user_id={user_id}, error={str(e)}\n{stack_trace}"
                )

                # 重新抛出异常，让上层处理
                raise

        return wrapper

    return decorator
