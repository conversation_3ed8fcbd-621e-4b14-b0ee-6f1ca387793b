2025-05-22 21:23:16,699 [INFO] [N/A] [用户:N/A] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:102]
2025-05-22 21:23:17,118 [INFO] [N/A] [用户:N/A] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:102]
2025-05-22 21:23:17,364 [INFO] [N/A] [用户:N/A] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:102]
2025-05-22 21:23:17,568 [WARNING] [N/A] [用户:N/A]  * Debugger is active! [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:23:17,600 [INFO] [N/A] [用户:N/A]  * Debugger PIN: 131-026-009 [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:25:39,825 [INFO] [N/A] [用户:N/A]  * Detected change in 'D:\\PyProject\\icms2\\config.py', reloading [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:25:42,468 [INFO] [N/A] [用户:N/A] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:102]
2025-05-22 21:25:42,792 [INFO] [N/A] [用户:N/A] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:102]
2025-05-22 21:25:43,066 [INFO] [N/A] [用户:N/A] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:102]
2025-05-22 21:25:43,234 [WARNING] [N/A] [用户:N/A]  * Debugger is active! [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:25:43,258 [INFO] [N/A] [用户:N/A]  * Debugger PIN: 131-026-009 [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:26:05,235 [INFO] [N/A] [用户:N/A]  * Detected change in 'D:\\PyProject\\icms2\\app\\__init__.py', reloading [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:26:07,241 [INFO] [N/A] [用户:N/A] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:102]
2025-05-22 21:26:07,601 [INFO] [N/A] [用户:N/A] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:39]
2025-05-22 21:26:07,609 [INFO] [N/A] [用户:N/A] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:102]
2025-05-22 21:26:07,655 [INFO] [N/A] [用户:N/A] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:39]
2025-05-22 21:26:07,824 [INFO] [N/A] [用户:N/A] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:102]
2025-05-22 21:26:07,858 [INFO] [N/A] [用户:N/A] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:39]
2025-05-22 21:26:08,023 [WARNING] [N/A] [用户:N/A]  * Debugger is active! [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:26:08,055 [INFO] [N/A] [用户:N/A]  * Debugger PIN: 131-026-009 [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:26:16,353 [INFO] [N/A] [用户:N/A]  * Detected change in 'D:\\PyProject\\icms2\\app\\__init__.py', reloading [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:26:18,314 [INFO] [N/A] [用户:N/A] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:102]
2025-05-22 21:26:18,616 [INFO] [N/A] [用户:N/A] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:37]
2025-05-22 21:26:18,621 [INFO] [N/A] [用户:N/A] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:102]
2025-05-22 21:26:18,659 [INFO] [N/A] [用户:N/A] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:37]
2025-05-22 21:26:18,835 [INFO] [N/A] [用户:N/A] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:102]
2025-05-22 21:26:18,877 [INFO] [N/A] [用户:N/A] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:37]
2025-05-22 21:26:19,048 [WARNING] [N/A] [用户:N/A]  * Debugger is active! [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:26:19,096 [INFO] [N/A] [用户:N/A]  * Debugger PIN: 131-026-009 [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:27:04,984 [INFO] [N/A] [用户:N/A]  * Detected change in 'D:\\PyProject\\icms2\\app\\routes\\application.py', reloading [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:27:07,654 [INFO] [N/A] [用户:N/A] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:102]
2025-05-22 21:27:07,962 [INFO] [N/A] [用户:N/A] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:37]
2025-05-22 21:27:07,968 [INFO] [N/A] [用户:N/A] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:102]
2025-05-22 21:27:08,014 [INFO] [N/A] [用户:N/A] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:37]
2025-05-22 21:27:08,181 [INFO] [N/A] [用户:N/A] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:102]
2025-05-22 21:27:08,227 [INFO] [N/A] [用户:N/A] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:37]
2025-05-22 21:27:08,379 [WARNING] [N/A] [用户:N/A]  * Debugger is active! [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:27:08,406 [INFO] [N/A] [用户:N/A]  * Debugger PIN: 131-026-009 [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:27:15,459 [INFO] [N/A] [用户:N/A]  * Detected change in 'D:\\PyProject\\icms2\\app\\routes\\application.py', reloading [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:27:17,507 [INFO] [N/A] [用户:N/A] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:102]
2025-05-22 21:27:17,749 [INFO] [N/A] [用户:N/A] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:37]
2025-05-22 21:27:17,752 [INFO] [N/A] [用户:N/A] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:102]
2025-05-22 21:27:17,785 [INFO] [N/A] [用户:N/A] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:37]
2025-05-22 21:27:17,938 [INFO] [N/A] [用户:N/A] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:102]
2025-05-22 21:27:17,968 [INFO] [N/A] [用户:N/A] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:37]
2025-05-22 21:27:18,117 [WARNING] [N/A] [用户:N/A]  * Debugger is active! [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:27:18,144 [INFO] [N/A] [用户:N/A]  * Debugger PIN: 131-026-009 [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:27:23,297 [INFO] [N/A] [用户:N/A]  * Detected change in 'D:\\PyProject\\icms2\\app\\routes\\application.py', reloading [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:27:25,700 [INFO] [N/A] [用户:N/A] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:102]
2025-05-22 21:27:26,116 [INFO] [N/A] [用户:N/A] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:37]
2025-05-22 21:27:26,123 [INFO] [N/A] [用户:N/A] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:102]
2025-05-22 21:27:26,187 [INFO] [N/A] [用户:N/A] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:37]
2025-05-22 21:27:26,494 [INFO] [N/A] [用户:N/A] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:102]
2025-05-22 21:27:26,548 [INFO] [N/A] [用户:N/A] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:37]
2025-05-22 21:27:26,711 [WARNING] [N/A] [用户:N/A]  * Debugger is active! [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:27:26,751 [INFO] [N/A] [用户:N/A]  * Debugger PIN: 131-026-009 [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:27:30,457 [INFO] [N/A] [用户:N/A]  * Detected change in 'D:\\PyProject\\icms2\\app\\routes\\application.py', reloading [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:27:33,199 [INFO] [N/A] [用户:N/A] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:102]
2025-05-22 21:27:33,554 [INFO] [N/A] [用户:N/A] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:37]
2025-05-22 21:27:33,570 [INFO] [N/A] [用户:N/A] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:102]
2025-05-22 21:27:33,642 [INFO] [N/A] [用户:N/A] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:37]
2025-05-22 21:27:33,837 [INFO] [N/A] [用户:N/A] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:102]
2025-05-22 21:27:33,881 [INFO] [N/A] [用户:N/A] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:37]
2025-05-22 21:27:34,050 [WARNING] [N/A] [用户:N/A]  * Debugger is active! [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:27:34,090 [INFO] [N/A] [用户:N/A]  * Debugger PIN: 131-026-009 [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:27:41,593 [INFO] [N/A] [用户:N/A]  * Detected change in 'D:\\PyProject\\icms2\\app\\routes\\application.py', reloading [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:27:44,965 [INFO] [N/A] [用户:N/A] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:102]
2025-05-22 21:27:45,621 [INFO] [N/A] [用户:N/A] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:37]
2025-05-22 21:27:45,630 [INFO] [N/A] [用户:N/A] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:102]
2025-05-22 21:27:45,777 [INFO] [N/A] [用户:N/A] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:37]
2025-05-22 21:27:46,032 [INFO] [N/A] [用户:N/A] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:102]
2025-05-22 21:27:46,088 [INFO] [N/A] [用户:N/A] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:37]
2025-05-22 21:27:46,275 [WARNING] [N/A] [用户:N/A]  * Debugger is active! [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:27:46,321 [INFO] [N/A] [用户:N/A]  * Debugger PIN: 131-026-009 [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:27:50,515 [INFO] [127.0.0.1] [用户:unknown] 请求开始: endpoint=application.get_applications, method=GET, url=http://localhost:5000/api/applications?page=1&per_page=30&app_en_name=&app_cn_name=&app_admin=, client_ip=127.0.0.1, user_id=unknown [in D:\PyProject\icms2\app\utils\decorators.py:39]
2025-05-22 21:27:50,630 [INFO] [127.0.0.1] [用户:unknown] 请求结束: endpoint=application.get_applications, method=GET, status=200, time=0.114s, client_ip=127.0.0.1, user_id=unknown [in D:\PyProject\icms2\app\utils\decorators.py:50]
2025-05-22 21:27:50,643 [INFO] [N/A] [用户:N/A] 127.0.0.1 - - [22/May/2025 21:27:50] "GET /api/applications?page=1&per_page=30&app_en_name=&app_cn_name=&app_admin= HTTP/1.1" 200 - [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:27:50,766 [INFO] [127.0.0.1] [用户:unknown] 请求开始: endpoint=application.get_applications, method=GET, url=http://localhost:5000/api/applications?page=1&per_page=30&app_en_name=&app_cn_name=&app_admin=, client_ip=127.0.0.1, user_id=unknown [in D:\PyProject\icms2\app\utils\decorators.py:39]
2025-05-22 21:27:50,790 [INFO] [127.0.0.1] [用户:unknown] 请求结束: endpoint=application.get_applications, method=GET, status=200, time=0.025s, client_ip=127.0.0.1, user_id=unknown [in D:\PyProject\icms2\app\utils\decorators.py:50]
2025-05-22 21:27:50,806 [INFO] [N/A] [用户:N/A] 127.0.0.1 - - [22/May/2025 21:27:50] "GET /api/applications?page=1&per_page=30&app_en_name=&app_cn_name=&app_admin= HTTP/1.1" 200 - [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:30:08,204 [INFO] [N/A] [用户:N/A] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:102]
2025-05-22 21:30:08,510 [INFO] [N/A] [用户:N/A] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:37]
2025-05-22 21:30:08,514 [INFO] [N/A] [用户:N/A] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:102]
2025-05-22 21:30:08,562 [INFO] [N/A] [用户:N/A] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:37]
2025-05-22 21:30:08,747 [INFO] [N/A] [用户:N/A] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:102]
2025-05-22 21:30:08,790 [INFO] [N/A] [用户:N/A] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:37]
2025-05-22 21:30:08,997 [INFO] [N/A] [用户:N/A] [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://***********:5000 [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:30:08,998 [INFO] [N/A] [用户:N/A] [33mPress CTRL+C to quit[0m [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:30:09,002 [INFO] [N/A] [用户:N/A]  * Restarting with stat [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:30:11,257 [INFO] [N/A] [用户:N/A] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:102]
2025-05-22 21:30:11,649 [INFO] [N/A] [用户:N/A] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:37]
2025-05-22 21:30:11,653 [INFO] [N/A] [用户:N/A] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:102]
2025-05-22 21:30:11,707 [INFO] [N/A] [用户:N/A] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:37]
2025-05-22 21:30:11,884 [INFO] [N/A] [用户:N/A] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:102]
2025-05-22 21:30:11,924 [INFO] [N/A] [用户:N/A] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:37]
2025-05-22 21:30:12,078 [WARNING] [N/A] [用户:N/A]  * Debugger is active! [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:30:12,107 [INFO] [N/A] [用户:N/A]  * Debugger PIN: 131-026-009 [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:30:54,348 [INFO] [127.0.0.1] [用户:unknown] 请求开始: endpoint=application.update_application, method=PUT, url=http://localhost:5000/api/applications/4, client_ip=127.0.0.1, user_id=unknown [in D:\PyProject\icms2\app\utils\decorators.py:39]
2025-05-22 21:30:54,349 [INFO] [127.0.0.1] [用户:unknown] 操作开始: 类型=更新应用系统, endpoint=application.update_application, method=PUT, client_ip=127.0.0.1, user_id=unknown, 数据={'app_admin': '123', 'remark': ''} [in D:\PyProject\icms2\app\utils\decorators.py:106]
2025-05-22 21:30:54,470 [INFO] [127.0.0.1] [用户:unknown] 操作成功: 类型=更新应用系统, endpoint=application.update_application, method=PUT, client_ip=127.0.0.1, user_id=unknown [in D:\PyProject\icms2\app\utils\decorators.py:116]
2025-05-22 21:30:54,471 [INFO] [127.0.0.1] [用户:unknown] 请求结束: endpoint=application.update_application, method=PUT, status=200, time=0.123s, client_ip=127.0.0.1, user_id=unknown [in D:\PyProject\icms2\app\utils\decorators.py:50]
2025-05-22 21:30:54,472 [INFO] [N/A] [用户:N/A] 127.0.0.1 - - [22/May/2025 21:30:54] "PUT /api/applications/4 HTTP/1.1" 200 - [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:30:54,597 [INFO] [127.0.0.1] [用户:unknown] 请求开始: endpoint=application.get_applications, method=GET, url=http://localhost:5000/api/applications?page=1&per_page=30&app_en_name=&app_cn_name=&app_admin=, client_ip=127.0.0.1, user_id=unknown [in D:\PyProject\icms2\app\utils\decorators.py:39]
2025-05-22 21:30:54,628 [INFO] [127.0.0.1] [用户:unknown] 请求结束: endpoint=application.get_applications, method=GET, status=200, time=0.031s, client_ip=127.0.0.1, user_id=unknown [in D:\PyProject\icms2\app\utils\decorators.py:50]
2025-05-22 21:30:54,648 [INFO] [N/A] [用户:N/A] 127.0.0.1 - - [22/May/2025 21:30:54] "GET /api/applications?page=1&per_page=30&app_en_name=&app_cn_name=&app_admin= HTTP/1.1" 200 - [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:31:18,881 [INFO] [N/A] [用户:N/A] 127.0.0.1 - - [22/May/2025 21:31:18] "GET /api/validate-token HTTP/1.1" 200 - [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:31:19,444 [INFO] [N/A] [用户:N/A] 127.0.0.1 - - [22/May/2025 21:31:19] "GET /api/validate-token HTTP/1.1" 200 - [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:31:20,032 [INFO] [N/A] [用户:N/A] 127.0.0.1 - - [22/May/2025 21:31:20] "GET /api/validate-token HTTP/1.1" 200 - [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:31:20,577 [INFO] [N/A] [用户:N/A] 127.0.0.1 - - [22/May/2025 21:31:20] "GET /api/validate-token HTTP/1.1" 200 - [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:31:26,897 [INFO] [127.0.0.1] [用户:unknown] Trying LDAP config: id=5, server=*********** [in D:\PyProject\icms2\app\routes\auth.py:215]
2025-05-22 21:31:27,865 [INFO] [127.0.0.1] [用户:unknown] LDAP search: config_id=5, base=OU=信息技术部,OU=北京职场,OU=内部全体员工,OU=和泰人寿员工,DC=ht,DC=com, filter=(|(cn=gerujun)(sAMAccountName=gerujun)) [in D:\PyProject\icms2\app\routes\auth.py:230]
2025-05-22 21:31:27,926 [INFO] [127.0.0.1] [用户:unknown] Trying LDAP config: id=8, server=*********** [in D:\PyProject\icms2\app\routes\auth.py:215]
2025-05-22 21:31:28,939 [INFO] [127.0.0.1] [用户:unknown] LDAP search: config_id=8, base=OU=信息技术部,OU=深圳职场,OU=内部全体员工,OU=和泰人寿员工,DC=ht,DC=com, filter=(|(cn=gerujun)(sAMAccountName=gerujun)) [in D:\PyProject\icms2\app\routes\auth.py:230]
2025-05-22 21:31:30,126 [INFO] [127.0.0.1] [用户:unknown] Login successful: user=gerujun, ip=127.0.0.1, type=LDAP, config_id=8 [in D:\PyProject\icms2\app\routes\auth.py:270]
2025-05-22 21:31:30,137 [INFO] [N/A] [用户:N/A] 127.0.0.1 - - [22/May/2025 21:31:30] "POST /api/login HTTP/1.1" 200 - [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:31:34,638 [INFO] [127.0.0.1] [用户:unknown] 请求开始: endpoint=application.get_applications, method=GET, url=http://localhost:5000/api/applications?page=1&per_page=30&app_en_name=&app_cn_name=&app_admin=, client_ip=127.0.0.1, user_id=unknown [in D:\PyProject\icms2\app\utils\decorators.py:39]
2025-05-22 21:31:34,662 [INFO] [127.0.0.1] [用户:unknown] 请求结束: endpoint=application.get_applications, method=GET, status=200, time=0.024s, client_ip=127.0.0.1, user_id=unknown [in D:\PyProject\icms2\app\utils\decorators.py:50]
2025-05-22 21:31:34,681 [INFO] [N/A] [用户:N/A] 127.0.0.1 - - [22/May/2025 21:31:34] "GET /api/applications?page=1&per_page=30&app_en_name=&app_cn_name=&app_admin= HTTP/1.1" 200 - [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:31:34,887 [INFO] [127.0.0.1] [用户:unknown] 请求开始: endpoint=application.get_applications, method=GET, url=http://localhost:5000/api/applications?page=1&per_page=30&app_en_name=&app_cn_name=&app_admin=, client_ip=127.0.0.1, user_id=unknown [in D:\PyProject\icms2\app\utils\decorators.py:39]
2025-05-22 21:31:34,910 [INFO] [127.0.0.1] [用户:unknown] 请求结束: endpoint=application.get_applications, method=GET, status=200, time=0.023s, client_ip=127.0.0.1, user_id=unknown [in D:\PyProject\icms2\app\utils\decorators.py:50]
2025-05-22 21:31:34,924 [INFO] [N/A] [用户:N/A] 127.0.0.1 - - [22/May/2025 21:31:34] "GET /api/applications?page=1&per_page=30&app_en_name=&app_cn_name=&app_admin= HTTP/1.1" 200 - [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:31:42,365 [INFO] [127.0.0.1] [用户:unknown] 请求开始: endpoint=application.update_application, method=PUT, url=http://localhost:5000/api/applications/2, client_ip=127.0.0.1, user_id=unknown [in D:\PyProject\icms2\app\utils\decorators.py:39]
2025-05-22 21:31:42,366 [INFO] [127.0.0.1] [用户:unknown] 操作开始: 类型=更新应用系统, endpoint=application.update_application, method=PUT, client_ip=127.0.0.1, user_id=unknown, 数据={'app_admin': '456', 'remark': ''} [in D:\PyProject\icms2\app\utils\decorators.py:106]
2025-05-22 21:31:42,434 [INFO] [127.0.0.1] [用户:unknown] 操作成功: 类型=更新应用系统, endpoint=application.update_application, method=PUT, client_ip=127.0.0.1, user_id=unknown [in D:\PyProject\icms2\app\utils\decorators.py:116]
2025-05-22 21:31:42,436 [INFO] [127.0.0.1] [用户:unknown] 请求结束: endpoint=application.update_application, method=PUT, status=200, time=0.071s, client_ip=127.0.0.1, user_id=unknown [in D:\PyProject\icms2\app\utils\decorators.py:50]
2025-05-22 21:31:42,437 [INFO] [N/A] [用户:N/A] 127.0.0.1 - - [22/May/2025 21:31:42] "PUT /api/applications/2 HTTP/1.1" 200 - [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:31:42,617 [INFO] [127.0.0.1] [用户:unknown] 请求开始: endpoint=application.get_applications, method=GET, url=http://localhost:5000/api/applications?page=1&per_page=30&app_en_name=&app_cn_name=&app_admin=, client_ip=127.0.0.1, user_id=unknown [in D:\PyProject\icms2\app\utils\decorators.py:39]
2025-05-22 21:31:42,664 [INFO] [127.0.0.1] [用户:unknown] 请求结束: endpoint=application.get_applications, method=GET, status=200, time=0.047s, client_ip=127.0.0.1, user_id=unknown [in D:\PyProject\icms2\app\utils\decorators.py:50]
2025-05-22 21:31:42,679 [INFO] [N/A] [用户:N/A] 127.0.0.1 - - [22/May/2025 21:31:42] "GET /api/applications?page=1&per_page=30&app_en_name=&app_cn_name=&app_admin= HTTP/1.1" 200 - [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:32:15,872 [INFO] [N/A] [用户:N/A] 127.0.0.1 - - [22/May/2025 21:32:15] "GET /api/application-environments?page=1&per_page=20&app_en_name=&app_cn_name= HTTP/1.1" 200 - [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:32:16,125 [INFO] [N/A] [用户:N/A] 127.0.0.1 - - [22/May/2025 21:32:16] "GET /api/application-environments?page=1&per_page=20&app_en_name=&app_cn_name= HTTP/1.1" 200 - [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:32:39,420 [INFO] [N/A] [用户:N/A] 127.0.0.1 - - [22/May/2025 21:32:39] "GET /api/application-environments/1/resources HTTP/1.1" 200 - [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:32:47,208 [INFO] [N/A] [用户:N/A] 127.0.0.1 - - [22/May/2025 21:32:47] "GET /api/load-balancers/search?keyword=&limit=1000 HTTP/1.1" 200 - [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:32:55,528 [INFO] [N/A] [用户:N/A] 127.0.0.1 - - [22/May/2025 21:32:55] "POST /api/application-environments/1/load-balancers HTTP/1.1" 200 - [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:32:55,841 [INFO] [N/A] [用户:N/A] 127.0.0.1 - - [22/May/2025 21:32:55] "GET /api/application-environments/1/resources HTTP/1.1" 200 - [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:36:17,325 [INFO] [N/A] [用户:N/A]  * Detected change in 'D:\\PyProject\\icms2\\app\\routes\\auth.py', reloading [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:36:17,635 [INFO] [N/A] [用户:N/A]  * Restarting with stat [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:36:19,836 [INFO] [N/A] [用户:N/A] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:102]
2025-05-22 21:36:20,159 [INFO] [N/A] [用户:N/A] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:37]
2025-05-22 21:36:20,163 [INFO] [N/A] [用户:N/A] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:102]
2025-05-22 21:36:20,203 [INFO] [N/A] [用户:N/A] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:37]
2025-05-22 21:36:20,350 [INFO] [N/A] [用户:N/A] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:102]
2025-05-22 21:36:20,385 [INFO] [N/A] [用户:N/A] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:37]
2025-05-22 21:36:20,549 [WARNING] [N/A] [用户:N/A]  * Debugger is active! [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:36:20,584 [INFO] [N/A] [用户:N/A]  * Debugger PIN: 131-026-009 [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:36:29,034 [INFO] [N/A] [用户:N/A]  * Detected change in 'D:\\PyProject\\icms2\\app\\routes\\auth.py', reloading [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:36:29,202 [INFO] [N/A] [用户:N/A]  * Restarting with stat [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:36:30,891 [INFO] [N/A] [用户:N/A] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:102]
2025-05-22 21:36:31,187 [INFO] [N/A] [用户:N/A] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:37]
2025-05-22 21:36:31,191 [INFO] [N/A] [用户:N/A] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:102]
2025-05-22 21:36:31,226 [INFO] [N/A] [用户:N/A] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:37]
2025-05-22 21:36:31,442 [INFO] [N/A] [用户:N/A] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:102]
2025-05-22 21:36:31,487 [INFO] [N/A] [用户:N/A] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:37]
2025-05-22 21:36:31,677 [WARNING] [N/A] [用户:N/A]  * Debugger is active! [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:36:31,723 [INFO] [N/A] [用户:N/A]  * Debugger PIN: 131-026-009 [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:36:40,076 [INFO] [N/A] [用户:N/A]  * Detected change in 'D:\\PyProject\\icms2\\app\\routes\\auth.py', reloading [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:36:40,321 [INFO] [N/A] [用户:N/A]  * Restarting with stat [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:36:42,678 [INFO] [N/A] [用户:N/A] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:102]
2025-05-22 21:36:42,961 [INFO] [N/A] [用户:N/A] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:37]
2025-05-22 21:36:42,966 [INFO] [N/A] [用户:N/A] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:102]
2025-05-22 21:36:43,022 [INFO] [N/A] [用户:N/A] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:37]
2025-05-22 21:36:43,212 [INFO] [N/A] [用户:N/A] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:102]
2025-05-22 21:36:43,258 [INFO] [N/A] [用户:N/A] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:37]
2025-05-22 21:36:43,442 [WARNING] [N/A] [用户:N/A]  * Debugger is active! [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:36:43,492 [INFO] [N/A] [用户:N/A]  * Debugger PIN: 131-026-009 [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:36:48,766 [INFO] [N/A] [用户:N/A]  * Detected change in 'D:\\PyProject\\icms2\\app\\routes\\auth.py', reloading [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:36:49,077 [INFO] [N/A] [用户:N/A]  * Restarting with stat [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:36:51,793 [INFO] [N/A] [用户:N/A] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:102]
2025-05-22 21:36:52,445 [INFO] [N/A] [用户:N/A] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:37]
2025-05-22 21:36:52,472 [INFO] [N/A] [用户:N/A] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:102]
2025-05-22 21:36:52,602 [INFO] [N/A] [用户:N/A] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:37]
2025-05-22 21:36:52,892 [INFO] [N/A] [用户:N/A] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:102]
2025-05-22 21:36:52,962 [INFO] [N/A] [用户:N/A] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:37]
2025-05-22 21:36:53,153 [WARNING] [N/A] [用户:N/A]  * Debugger is active! [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:36:53,208 [INFO] [N/A] [用户:N/A]  * Debugger PIN: 131-026-009 [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:37:00,587 [INFO] [N/A] [用户:N/A]  * Detected change in 'D:\\PyProject\\icms2\\app\\routes\\auth.py', reloading [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:37:00,861 [INFO] [N/A] [用户:N/A]  * Restarting with stat [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:37:03,461 [INFO] [N/A] [用户:N/A] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:102]
2025-05-22 21:37:03,864 [INFO] [N/A] [用户:N/A] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:37]
2025-05-22 21:37:03,870 [INFO] [N/A] [用户:N/A] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:102]
2025-05-22 21:37:03,967 [INFO] [N/A] [用户:N/A] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:37]
2025-05-22 21:37:04,195 [INFO] [N/A] [用户:N/A] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:102]
2025-05-22 21:37:04,258 [INFO] [N/A] [用户:N/A] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:37]
2025-05-22 21:37:04,457 [WARNING] [N/A] [用户:N/A]  * Debugger is active! [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:37:04,531 [INFO] [N/A] [用户:N/A]  * Debugger PIN: 131-026-009 [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:37:13,335 [INFO] [N/A] [用户:N/A]  * Detected change in 'D:\\PyProject\\icms2\\app\\routes\\auth.py', reloading [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:37:13,594 [INFO] [N/A] [用户:N/A]  * Restarting with stat [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:37:15,663 [INFO] [N/A] [用户:N/A] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:102]
2025-05-22 21:37:15,983 [INFO] [N/A] [用户:N/A] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:37]
2025-05-22 21:37:15,987 [INFO] [N/A] [用户:N/A] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:102]
2025-05-22 21:37:16,040 [INFO] [N/A] [用户:N/A] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:37]
2025-05-22 21:37:16,237 [INFO] [N/A] [用户:N/A] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:102]
2025-05-22 21:37:16,305 [INFO] [N/A] [用户:N/A] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:37]
2025-05-22 21:37:16,490 [WARNING] [N/A] [用户:N/A]  * Debugger is active! [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:37:16,549 [INFO] [N/A] [用户:N/A]  * Debugger PIN: 131-026-009 [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:37:20,699 [INFO] [N/A] [用户:N/A]  * Detected change in 'D:\\PyProject\\icms2\\app\\routes\\auth.py', reloading [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:37:21,196 [INFO] [N/A] [用户:N/A]  * Restarting with stat [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:37:23,411 [INFO] [N/A] [用户:N/A] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:102]
2025-05-22 21:37:23,734 [INFO] [N/A] [用户:N/A] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:37]
2025-05-22 21:37:23,738 [INFO] [N/A] [用户:N/A] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:102]
2025-05-22 21:37:23,789 [INFO] [N/A] [用户:N/A] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:37]
2025-05-22 21:37:23,992 [INFO] [N/A] [用户:N/A] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:102]
2025-05-22 21:37:24,051 [INFO] [N/A] [用户:N/A] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:37]
2025-05-22 21:37:24,255 [WARNING] [N/A] [用户:N/A]  * Debugger is active! [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:37:24,367 [INFO] [N/A] [用户:N/A]  * Debugger PIN: 131-026-009 [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:37:30,894 [INFO] [N/A] [用户:N/A]  * Detected change in 'D:\\PyProject\\icms2\\app\\routes\\auth.py', reloading [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:37:31,324 [INFO] [N/A] [用户:N/A]  * Restarting with stat [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:37:33,971 [INFO] [N/A] [用户:N/A] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:102]
2025-05-22 21:37:34,291 [INFO] [N/A] [用户:N/A] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:37]
2025-05-22 21:37:34,298 [INFO] [N/A] [用户:N/A] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:102]
2025-05-22 21:37:34,352 [INFO] [N/A] [用户:N/A] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:37]
2025-05-22 21:37:34,575 [INFO] [N/A] [用户:N/A] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:102]
2025-05-22 21:37:34,634 [INFO] [N/A] [用户:N/A] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:37]
2025-05-22 21:37:34,892 [WARNING] [N/A] [用户:N/A]  * Debugger is active! [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:37:34,948 [INFO] [N/A] [用户:N/A]  * Debugger PIN: 131-026-009 [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:37:47,879 [INFO] [N/A] [用户:N/A]  * Detected change in 'D:\\PyProject\\icms2\\app\\routes\\auth.py', reloading [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:37:48,194 [INFO] [N/A] [用户:N/A]  * Restarting with stat [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:37:50,415 [INFO] [N/A] [用户:N/A] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:102]
2025-05-22 21:37:50,958 [INFO] [N/A] [用户:N/A] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:37]
2025-05-22 21:37:50,970 [INFO] [N/A] [用户:N/A] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:102]
2025-05-22 21:37:51,048 [INFO] [N/A] [用户:N/A] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:37]
2025-05-22 21:37:51,388 [INFO] [N/A] [用户:N/A] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:102]
2025-05-22 21:37:51,465 [INFO] [N/A] [用户:N/A] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:37]
2025-05-22 21:37:51,681 [WARNING] [N/A] [用户:N/A]  * Debugger is active! [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:37:51,753 [INFO] [N/A] [用户:N/A]  * Debugger PIN: 131-026-009 [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:38:04,863 [INFO] [N/A] [用户:N/A]  * Detected change in 'D:\\PyProject\\icms2\\app\\routes\\module_menu.py', reloading [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:38:05,331 [INFO] [N/A] [用户:N/A]  * Restarting with stat [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:38:07,923 [INFO] [N/A] [用户:N/A] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:102]
2025-05-22 21:38:08,344 [INFO] [N/A] [用户:N/A] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:37]
2025-05-22 21:38:08,349 [INFO] [N/A] [用户:N/A] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:102]
2025-05-22 21:38:08,416 [INFO] [N/A] [用户:N/A] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:37]
2025-05-22 21:38:08,618 [INFO] [N/A] [用户:N/A] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:102]
2025-05-22 21:38:08,660 [INFO] [N/A] [用户:N/A] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:37]
2025-05-22 21:38:08,821 [WARNING] [N/A] [用户:N/A]  * Debugger is active! [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:38:08,852 [INFO] [N/A] [用户:N/A]  * Debugger PIN: 131-026-009 [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:38:17,259 [INFO] [N/A] [用户:N/A]  * Detected change in 'D:\\PyProject\\icms2\\app\\routes\\module_menu.py', reloading [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:38:17,496 [INFO] [N/A] [用户:N/A]  * Restarting with stat [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:38:20,070 [INFO] [N/A] [用户:N/A] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:102]
2025-05-22 21:38:20,582 [INFO] [N/A] [用户:N/A] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:37]
2025-05-22 21:38:20,590 [INFO] [N/A] [用户:N/A] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:102]
2025-05-22 21:38:20,654 [INFO] [N/A] [用户:N/A] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:37]
2025-05-22 21:38:20,901 [INFO] [N/A] [用户:N/A] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:102]
2025-05-22 21:38:21,062 [INFO] [N/A] [用户:N/A] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:37]
2025-05-22 21:38:21,267 [WARNING] [N/A] [用户:N/A]  * Debugger is active! [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:38:21,315 [INFO] [N/A] [用户:N/A]  * Debugger PIN: 131-026-009 [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:38:28,513 [INFO] [N/A] [用户:N/A]  * Detected change in 'D:\\PyProject\\icms2\\app\\routes\\module_menu.py', reloading [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:38:28,762 [INFO] [N/A] [用户:N/A]  * Restarting with stat [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:38:30,591 [INFO] [N/A] [用户:N/A] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:102]
2025-05-22 21:38:30,846 [INFO] [N/A] [用户:N/A] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:37]
2025-05-22 21:38:30,850 [INFO] [N/A] [用户:N/A] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:102]
2025-05-22 21:38:30,951 [INFO] [N/A] [用户:N/A] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:37]
2025-05-22 21:38:31,196 [INFO] [N/A] [用户:N/A] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:102]
2025-05-22 21:38:31,254 [INFO] [N/A] [用户:N/A] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:37]
2025-05-22 21:38:31,437 [WARNING] [N/A] [用户:N/A]  * Debugger is active! [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:38:31,491 [INFO] [N/A] [用户:N/A]  * Debugger PIN: 131-026-009 [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:38:45,751 [INFO] [N/A] [用户:N/A]  * Detected change in 'D:\\PyProject\\icms2\\app\\routes\\module_menu.py', reloading [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:38:45,997 [INFO] [N/A] [用户:N/A]  * Restarting with stat [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:38:48,721 [INFO] [N/A] [用户:N/A] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:102]
2025-05-22 21:38:49,357 [INFO] [N/A] [用户:N/A] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:37]
2025-05-22 21:38:49,363 [INFO] [N/A] [用户:N/A] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:102]
2025-05-22 21:38:49,422 [INFO] [N/A] [用户:N/A] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:37]
2025-05-22 21:38:49,632 [INFO] [N/A] [用户:N/A] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:102]
2025-05-22 21:38:49,674 [INFO] [N/A] [用户:N/A] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:37]
2025-05-22 21:38:49,833 [WARNING] [N/A] [用户:N/A]  * Debugger is active! [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:38:49,865 [INFO] [N/A] [用户:N/A]  * Debugger PIN: 131-026-009 [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:39:11,937 [INFO] [N/A] [用户:N/A]  * Detected change in 'D:\\PyProject\\icms2\\app\\routes\\role.py', reloading [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:39:12,404 [INFO] [N/A] [用户:N/A]  * Restarting with stat [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:39:14,865 [INFO] [N/A] [用户:N/A] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:102]
2025-05-22 21:39:15,215 [INFO] [N/A] [用户:N/A] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:37]
2025-05-22 21:39:15,219 [INFO] [N/A] [用户:N/A] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:102]
2025-05-22 21:39:15,340 [INFO] [N/A] [用户:N/A] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:37]
2025-05-22 21:39:15,549 [INFO] [N/A] [用户:N/A] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:102]
2025-05-22 21:39:15,601 [INFO] [N/A] [用户:N/A] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:37]
2025-05-22 21:39:15,763 [WARNING] [N/A] [用户:N/A]  * Debugger is active! [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:39:15,809 [INFO] [N/A] [用户:N/A]  * Debugger PIN: 131-026-009 [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:39:25,286 [INFO] [N/A] [用户:N/A]  * Detected change in 'D:\\PyProject\\icms2\\app\\routes\\role.py', reloading [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:39:25,801 [INFO] [N/A] [用户:N/A]  * Restarting with stat [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:39:28,706 [INFO] [N/A] [用户:N/A] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:102]
2025-05-22 21:39:29,007 [INFO] [N/A] [用户:N/A] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:37]
2025-05-22 21:39:29,012 [INFO] [N/A] [用户:N/A] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:102]
2025-05-22 21:39:29,051 [INFO] [N/A] [用户:N/A] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:37]
2025-05-22 21:39:29,213 [INFO] [N/A] [用户:N/A] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:102]
2025-05-22 21:39:29,250 [INFO] [N/A] [用户:N/A] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:37]
2025-05-22 21:39:29,410 [WARNING] [N/A] [用户:N/A]  * Debugger is active! [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:39:29,449 [INFO] [N/A] [用户:N/A]  * Debugger PIN: 131-026-009 [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:39:37,716 [INFO] [N/A] [用户:N/A]  * Detected change in 'D:\\PyProject\\icms2\\app\\routes\\role.py', reloading [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:39:37,969 [INFO] [N/A] [用户:N/A]  * Restarting with stat [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:39:40,158 [INFO] [N/A] [用户:N/A] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:102]
2025-05-22 21:39:40,665 [INFO] [N/A] [用户:N/A] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:37]
2025-05-22 21:39:40,686 [INFO] [N/A] [用户:N/A] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:102]
2025-05-22 21:39:40,844 [INFO] [N/A] [用户:N/A] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:37]
2025-05-22 21:39:41,132 [INFO] [N/A] [用户:N/A] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:102]
2025-05-22 21:39:41,191 [INFO] [N/A] [用户:N/A] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:37]
2025-05-22 21:39:41,362 [WARNING] [N/A] [用户:N/A]  * Debugger is active! [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:39:41,408 [INFO] [N/A] [用户:N/A]  * Debugger PIN: 131-026-009 [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:39:49,908 [INFO] [N/A] [用户:N/A]  * Detected change in 'D:\\PyProject\\icms2\\app\\routes\\role.py', reloading [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:39:50,091 [INFO] [N/A] [用户:N/A]  * Restarting with stat [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:39:52,033 [INFO] [N/A] [用户:N/A] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:102]
2025-05-22 21:39:52,566 [INFO] [N/A] [用户:N/A] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:37]
2025-05-22 21:39:52,574 [INFO] [N/A] [用户:N/A] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:102]
2025-05-22 21:39:52,652 [INFO] [N/A] [用户:N/A] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:37]
2025-05-22 21:39:52,906 [INFO] [N/A] [用户:N/A] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:102]
2025-05-22 21:39:52,989 [INFO] [N/A] [用户:N/A] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:37]
2025-05-22 21:39:53,205 [WARNING] [N/A] [用户:N/A]  * Debugger is active! [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:39:53,265 [INFO] [N/A] [用户:N/A]  * Debugger PIN: 131-026-009 [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:39:56,212 [INFO] [N/A] [用户:N/A]  * Detected change in 'D:\\PyProject\\icms2\\app\\routes\\role.py', reloading [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:39:56,566 [INFO] [N/A] [用户:N/A]  * Restarting with stat [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:39:58,731 [INFO] [N/A] [用户:N/A] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:102]
2025-05-22 21:39:59,145 [INFO] [N/A] [用户:N/A] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:37]
2025-05-22 21:39:59,152 [INFO] [N/A] [用户:N/A] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:102]
2025-05-22 21:39:59,208 [INFO] [N/A] [用户:N/A] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:37]
2025-05-22 21:39:59,422 [INFO] [N/A] [用户:N/A] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:102]
2025-05-22 21:39:59,489 [INFO] [N/A] [用户:N/A] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:37]
2025-05-22 21:39:59,705 [WARNING] [N/A] [用户:N/A]  * Debugger is active! [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:39:59,805 [INFO] [N/A] [用户:N/A]  * Debugger PIN: 131-026-009 [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:40:09,486 [INFO] [N/A] [用户:N/A]  * Detected change in 'D:\\PyProject\\icms2\\app\\routes\\role.py', reloading [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:40:09,740 [INFO] [N/A] [用户:N/A]  * Restarting with stat [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:40:12,296 [INFO] [N/A] [用户:N/A] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:102]
2025-05-22 21:40:12,925 [INFO] [N/A] [用户:N/A] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:37]
2025-05-22 21:40:12,932 [INFO] [N/A] [用户:N/A] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:102]
2025-05-22 21:40:13,016 [INFO] [N/A] [用户:N/A] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:37]
2025-05-22 21:40:13,284 [INFO] [N/A] [用户:N/A] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:102]
2025-05-22 21:40:13,340 [INFO] [N/A] [用户:N/A] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:37]
2025-05-22 21:40:13,515 [WARNING] [N/A] [用户:N/A]  * Debugger is active! [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:40:13,563 [INFO] [N/A] [用户:N/A]  * Debugger PIN: 131-026-009 [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:40:19,769 [INFO] [N/A] [用户:N/A]  * Detected change in 'D:\\PyProject\\icms2\\app\\routes\\role.py', reloading [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:40:20,098 [INFO] [N/A] [用户:N/A]  * Restarting with stat [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:40:22,411 [INFO] [N/A] [用户:N/A] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:102]
2025-05-22 21:40:22,730 [INFO] [N/A] [用户:N/A] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:37]
2025-05-22 21:40:22,734 [INFO] [N/A] [用户:N/A] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:102]
2025-05-22 21:40:22,786 [INFO] [N/A] [用户:N/A] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:37]
2025-05-22 21:40:22,993 [INFO] [N/A] [用户:N/A] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:102]
2025-05-22 21:40:23,048 [INFO] [N/A] [用户:N/A] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:37]
2025-05-22 21:40:23,218 [WARNING] [N/A] [用户:N/A]  * Debugger is active! [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:40:23,261 [INFO] [N/A] [用户:N/A]  * Debugger PIN: 131-026-009 [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:40:51,361 [INFO] [N/A] [用户:N/A]  * Detected change in 'D:\\PyProject\\icms2\\app\\routes\\server_resource.py', reloading [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:40:51,613 [INFO] [N/A] [用户:N/A]  * Restarting with stat [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:40:54,453 [INFO] [N/A] [用户:N/A] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:102]
2025-05-22 21:40:54,942 [INFO] [N/A] [用户:N/A] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:37]
2025-05-22 21:40:54,951 [INFO] [N/A] [用户:N/A] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:102]
2025-05-22 21:40:55,040 [INFO] [N/A] [用户:N/A] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:37]
2025-05-22 21:40:55,296 [INFO] [N/A] [用户:N/A] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:102]
2025-05-22 21:40:55,366 [INFO] [N/A] [用户:N/A] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:37]
2025-05-22 21:40:55,577 [WARNING] [N/A] [用户:N/A]  * Debugger is active! [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:40:55,657 [INFO] [N/A] [用户:N/A]  * Debugger PIN: 131-026-009 [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:41:03,231 [INFO] [N/A] [用户:N/A]  * Detected change in 'D:\\PyProject\\icms2\\app\\routes\\server_resource.py', reloading [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:41:03,512 [INFO] [N/A] [用户:N/A]  * Restarting with stat [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:41:06,249 [INFO] [N/A] [用户:N/A] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:102]
2025-05-22 21:41:06,777 [INFO] [N/A] [用户:N/A] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:37]
2025-05-22 21:41:06,809 [INFO] [N/A] [用户:N/A] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:102]
2025-05-22 21:41:06,928 [INFO] [N/A] [用户:N/A] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:37]
2025-05-22 21:41:07,199 [INFO] [N/A] [用户:N/A] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:102]
2025-05-22 21:41:07,274 [INFO] [N/A] [用户:N/A] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:37]
2025-05-22 21:41:07,518 [WARNING] [N/A] [用户:N/A]  * Debugger is active! [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:41:07,583 [INFO] [N/A] [用户:N/A]  * Debugger PIN: 131-026-009 [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:41:11,630 [INFO] [N/A] [用户:N/A]  * Detected change in 'D:\\PyProject\\icms2\\app\\routes\\server_resource.py', reloading [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:41:11,951 [INFO] [N/A] [用户:N/A]  * Restarting with stat [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:41:14,820 [INFO] [N/A] [用户:N/A] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:102]
2025-05-22 21:41:15,266 [INFO] [N/A] [用户:N/A] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:37]
2025-05-22 21:41:15,279 [INFO] [N/A] [用户:N/A] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:102]
2025-05-22 21:41:15,450 [INFO] [N/A] [用户:N/A] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:37]
2025-05-22 21:41:15,677 [INFO] [N/A] [用户:N/A] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:102]
2025-05-22 21:41:15,759 [INFO] [N/A] [用户:N/A] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:37]
2025-05-22 21:41:15,961 [WARNING] [N/A] [用户:N/A]  * Debugger is active! [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:41:16,027 [INFO] [N/A] [用户:N/A]  * Debugger PIN: 131-026-009 [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:41:20,161 [INFO] [N/A] [用户:N/A]  * Detected change in 'D:\\PyProject\\icms2\\app\\routes\\server_resource.py', reloading [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:41:20,693 [INFO] [N/A] [用户:N/A]  * Restarting with stat [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:41:24,172 [INFO] [N/A] [用户:N/A] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:102]
2025-05-22 21:41:24,897 [INFO] [N/A] [用户:N/A] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:37]
2025-05-22 21:41:24,906 [INFO] [N/A] [用户:N/A] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:102]
2025-05-22 21:41:24,962 [INFO] [N/A] [用户:N/A] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:37]
2025-05-22 21:41:25,164 [INFO] [N/A] [用户:N/A] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:102]
2025-05-22 21:41:25,207 [INFO] [N/A] [用户:N/A] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:37]
2025-05-22 21:41:25,365 [WARNING] [N/A] [用户:N/A]  * Debugger is active! [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:41:25,399 [INFO] [N/A] [用户:N/A]  * Debugger PIN: 131-026-009 [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:41:33,543 [INFO] [N/A] [用户:N/A]  * Detected change in 'D:\\PyProject\\icms2\\app\\routes\\server_resource.py', reloading [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:41:33,857 [INFO] [N/A] [用户:N/A]  * Restarting with stat [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:41:38,596 [INFO] [N/A] [用户:N/A] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:102]
2025-05-22 21:41:39,123 [INFO] [N/A] [用户:N/A] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:37]
2025-05-22 21:41:39,129 [INFO] [N/A] [用户:N/A] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:102]
2025-05-22 21:41:39,211 [INFO] [N/A] [用户:N/A] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:37]
2025-05-22 21:41:39,444 [INFO] [N/A] [用户:N/A] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:102]
2025-05-22 21:41:39,517 [INFO] [N/A] [用户:N/A] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:37]
2025-05-22 21:41:39,741 [WARNING] [N/A] [用户:N/A]  * Debugger is active! [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:41:39,805 [INFO] [N/A] [用户:N/A]  * Debugger PIN: 131-026-009 [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:41:53,924 [INFO] [N/A] [用户:N/A]  * Detected change in 'D:\\PyProject\\icms2\\app\\routes\\log_download.py', reloading [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:41:54,334 [INFO] [N/A] [用户:N/A]  * Restarting with stat [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:41:57,798 [INFO] [N/A] [用户:N/A] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:102]
2025-05-22 21:41:58,155 [INFO] [N/A] [用户:N/A] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:37]
2025-05-22 21:41:58,161 [INFO] [N/A] [用户:N/A] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:102]
2025-05-22 21:41:58,220 [INFO] [N/A] [用户:N/A] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:37]
2025-05-22 21:41:58,406 [INFO] [N/A] [用户:N/A] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:102]
2025-05-22 21:41:58,460 [INFO] [N/A] [用户:N/A] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:37]
2025-05-22 21:41:58,648 [WARNING] [N/A] [用户:N/A]  * Debugger is active! [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:41:58,718 [INFO] [N/A] [用户:N/A]  * Debugger PIN: 131-026-009 [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:42:05,287 [INFO] [N/A] [用户:N/A]  * Detected change in 'D:\\PyProject\\icms2\\app\\routes\\log_download.py', reloading [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:42:05,513 [INFO] [N/A] [用户:N/A]  * Restarting with stat [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:42:08,473 [INFO] [N/A] [用户:N/A] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:102]
2025-05-22 21:42:09,294 [INFO] [N/A] [用户:N/A] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:37]
2025-05-22 21:42:09,338 [INFO] [N/A] [用户:N/A] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:102]
2025-05-22 21:42:09,494 [INFO] [N/A] [用户:N/A] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:37]
2025-05-22 21:42:09,790 [INFO] [N/A] [用户:N/A] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:102]
2025-05-22 21:42:09,857 [INFO] [N/A] [用户:N/A] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:37]
2025-05-22 21:42:10,068 [WARNING] [N/A] [用户:N/A]  * Debugger is active! [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:42:10,133 [INFO] [N/A] [用户:N/A]  * Debugger PIN: 131-026-009 [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:42:24,329 [INFO] [N/A] [用户:N/A]  * Detected change in 'D:\\PyProject\\icms2\\app\\routes\\log_download.py', reloading [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:42:24,640 [INFO] [N/A] [用户:N/A]  * Restarting with stat [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:42:27,265 [INFO] [N/A] [用户:N/A] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:102]
2025-05-22 21:42:27,770 [INFO] [N/A] [用户:N/A] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:37]
2025-05-22 21:42:27,778 [INFO] [N/A] [用户:N/A] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:102]
2025-05-22 21:42:27,840 [INFO] [N/A] [用户:N/A] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:37]
2025-05-22 21:42:28,057 [INFO] [N/A] [用户:N/A] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:102]
2025-05-22 21:42:28,109 [INFO] [N/A] [用户:N/A] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:37]
2025-05-22 21:42:28,291 [WARNING] [N/A] [用户:N/A]  * Debugger is active! [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:42:28,340 [INFO] [N/A] [用户:N/A]  * Debugger PIN: 131-026-009 [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:42:53,624 [INFO] [N/A] [用户:N/A]  * Detected change in 'D:\\PyProject\\icms2\\app\\routes\\log_download.py', reloading [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:42:53,888 [INFO] [N/A] [用户:N/A]  * Restarting with stat [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:42:56,525 [INFO] [N/A] [用户:N/A] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:102]
2025-05-22 21:42:57,066 [INFO] [N/A] [用户:N/A] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:37]
2025-05-22 21:42:57,073 [INFO] [N/A] [用户:N/A] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:102]
2025-05-22 21:42:57,149 [INFO] [N/A] [用户:N/A] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:37]
2025-05-22 21:42:57,405 [INFO] [N/A] [用户:N/A] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:102]
2025-05-22 21:42:57,468 [INFO] [N/A] [用户:N/A] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:37]
2025-05-22 21:42:57,666 [WARNING] [N/A] [用户:N/A]  * Debugger is active! [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:42:57,715 [INFO] [N/A] [用户:N/A]  * Debugger PIN: 131-026-009 [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:43:19,654 [INFO] [N/A] [用户:N/A]  * Detected change in 'D:\\PyProject\\icms2\\app\\routes\\load_balancer.py', reloading [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:43:19,972 [INFO] [N/A] [用户:N/A]  * Restarting with stat [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:43:23,245 [INFO] [N/A] [用户:N/A] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:102]
2025-05-22 21:43:23,691 [INFO] [N/A] [用户:N/A] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:37]
2025-05-22 21:43:23,697 [INFO] [N/A] [用户:N/A] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:102]
2025-05-22 21:43:23,757 [INFO] [N/A] [用户:N/A] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:37]
2025-05-22 21:43:23,959 [INFO] [N/A] [用户:N/A] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:102]
2025-05-22 21:43:24,022 [INFO] [N/A] [用户:N/A] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:37]
2025-05-22 21:43:24,190 [WARNING] [N/A] [用户:N/A]  * Debugger is active! [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:43:24,239 [INFO] [N/A] [用户:N/A]  * Debugger PIN: 131-026-009 [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:43:37,802 [INFO] [N/A] [用户:N/A]  * Detected change in 'D:\\PyProject\\icms2\\app\\routes\\load_balancer.py', reloading [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:43:38,197 [INFO] [N/A] [用户:N/A]  * Restarting with stat [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:43:41,211 [INFO] [N/A] [用户:N/A] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:102]
2025-05-22 21:43:41,540 [INFO] [N/A] [用户:N/A] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:37]
2025-05-22 21:43:41,544 [INFO] [N/A] [用户:N/A] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:102]
2025-05-22 21:43:41,587 [INFO] [N/A] [用户:N/A] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:37]
2025-05-22 21:43:41,770 [INFO] [N/A] [用户:N/A] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:102]
2025-05-22 21:43:41,821 [INFO] [N/A] [用户:N/A] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:37]
2025-05-22 21:43:41,966 [WARNING] [N/A] [用户:N/A]  * Debugger is active! [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:43:41,999 [INFO] [N/A] [用户:N/A]  * Debugger PIN: 131-026-009 [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:43:45,927 [INFO] [N/A] [用户:N/A]  * Detected change in 'D:\\PyProject\\icms2\\app\\routes\\load_balancer.py', reloading [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:43:46,249 [INFO] [N/A] [用户:N/A]  * Restarting with stat [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:43:49,236 [INFO] [N/A] [用户:N/A] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:102]
2025-05-22 21:43:49,678 [INFO] [N/A] [用户:N/A] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:37]
2025-05-22 21:43:49,683 [INFO] [N/A] [用户:N/A] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:102]
2025-05-22 21:43:49,748 [INFO] [N/A] [用户:N/A] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:37]
2025-05-22 21:43:49,976 [INFO] [N/A] [用户:N/A] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:102]
2025-05-22 21:43:50,070 [INFO] [N/A] [用户:N/A] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:37]
2025-05-22 21:43:50,251 [WARNING] [N/A] [用户:N/A]  * Debugger is active! [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:43:50,300 [INFO] [N/A] [用户:N/A]  * Debugger PIN: 131-026-009 [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:43:55,402 [INFO] [N/A] [用户:N/A]  * Detected change in 'D:\\PyProject\\icms2\\app\\routes\\load_balancer.py', reloading [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:43:55,709 [INFO] [N/A] [用户:N/A]  * Restarting with stat [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:43:57,865 [INFO] [N/A] [用户:N/A] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:102]
2025-05-22 21:43:58,198 [INFO] [N/A] [用户:N/A] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:37]
2025-05-22 21:43:58,204 [INFO] [N/A] [用户:N/A] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:102]
2025-05-22 21:43:58,258 [INFO] [N/A] [用户:N/A] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:37]
2025-05-22 21:43:58,456 [INFO] [N/A] [用户:N/A] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:102]
2025-05-22 21:43:58,512 [INFO] [N/A] [用户:N/A] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:37]
2025-05-22 21:43:58,690 [WARNING] [N/A] [用户:N/A]  * Debugger is active! [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:43:58,741 [INFO] [N/A] [用户:N/A]  * Debugger PIN: 131-026-009 [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:48:16,385 [INFO] [127.0.0.1] [用户:unknown] 请求开始: endpoint=log_download.get_top_folders, method=GET, url=http://localhost:5000/api/logs/folders, client_ip=127.0.0.1, user_id=unknown [in D:\PyProject\icms2\app\utils\decorators.py:39]
2025-05-22 21:48:16,386 [INFO] [127.0.0.1] [用户:unknown] 获取顶级文件夹列表: client_ip=127.0.0.1, user_id=unknown, prefix= [in D:\PyProject\icms2\app\routes\log_download.py:39]
2025-05-22 21:48:17,520 [INFO] [127.0.0.1] [用户:unknown] generate built-in connection pool success. maxsize=10,10 [in D:\PyProject\icms2\.venv\Lib\site-packages\qcloud_cos\cos_client.py:274]
2025-05-22 21:48:17,520 [INFO] [127.0.0.1] [用户:unknown] bound built-in connection pool when new client. maxsize=10,10 [in D:\PyProject\icms2\.venv\Lib\site-packages\qcloud_cos\cos_client.py:264]
2025-05-22 21:48:17,521 [INFO] [127.0.0.1] [用户:unknown] list objects, url=:https://cvmlog-private-prd-sh-v5-1251979869.cos.ap-shanghai-fsi.myqcloud.com/ ,headers=:{} [in D:\PyProject\icms2\.venv\Lib\site-packages\qcloud_cos\cos_client.py:1473]
2025-05-22 21:48:18,258 [INFO] [127.0.0.1] [用户:unknown] 获取顶级文件夹列表成功: client_ip=127.0.0.1, user_id=unknown, count=442 [in D:\PyProject\icms2\app\routes\log_download.py:135]
2025-05-22 21:48:18,260 [INFO] [127.0.0.1] [用户:unknown] 请求结束: endpoint=log_download.get_top_folders, method=GET, status=200, time=1.875s, client_ip=127.0.0.1, user_id=unknown [in D:\PyProject\icms2\app\utils\decorators.py:50]
2025-05-22 21:48:18,270 [INFO] [N/A] [用户:N/A] 127.0.0.1 - - [22/May/2025 21:48:18] "GET /api/logs/folders HTTP/1.1" 200 - [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:48:18,282 [INFO] [127.0.0.1] [用户:unknown] 请求开始: endpoint=log_download.get_top_folders, method=GET, url=http://localhost:5000/api/logs/folders, client_ip=127.0.0.1, user_id=unknown [in D:\PyProject\icms2\app\utils\decorators.py:39]
2025-05-22 21:48:18,283 [INFO] [127.0.0.1] [用户:unknown] 获取顶级文件夹列表: client_ip=127.0.0.1, user_id=unknown, prefix= [in D:\PyProject\icms2\app\routes\log_download.py:39]
2025-05-22 21:48:18,289 [INFO] [127.0.0.1] [用户:unknown] bound built-in connection pool when new client. maxsize=10,10 [in D:\PyProject\icms2\.venv\Lib\site-packages\qcloud_cos\cos_client.py:264]
2025-05-22 21:48:18,290 [INFO] [127.0.0.1] [用户:unknown] list objects, url=:https://cvmlog-private-prd-sh-v5-1251979869.cos.ap-shanghai-fsi.myqcloud.com/ ,headers=:{} [in D:\PyProject\icms2\.venv\Lib\site-packages\qcloud_cos\cos_client.py:1473]
2025-05-22 21:48:18,810 [INFO] [127.0.0.1] [用户:unknown] 获取顶级文件夹列表成功: client_ip=127.0.0.1, user_id=unknown, count=442 [in D:\PyProject\icms2\app\routes\log_download.py:135]
2025-05-22 21:48:18,811 [INFO] [127.0.0.1] [用户:unknown] 请求结束: endpoint=log_download.get_top_folders, method=GET, status=200, time=0.529s, client_ip=127.0.0.1, user_id=unknown [in D:\PyProject\icms2\app\utils\decorators.py:50]
2025-05-22 21:48:18,822 [INFO] [N/A] [用户:N/A] 127.0.0.1 - - [22/May/2025 21:48:18] "GET /api/logs/folders HTTP/1.1" 200 - [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:48:20,497 [INFO] [127.0.0.1] [用户:unknown] 请求开始: endpoint=server_resource.get_servers, method=GET, url=http://localhost:5000/api/servers?page=1&per_page=20&hostname=&ip=&remark=, client_ip=127.0.0.1, user_id=unknown [in D:\PyProject\icms2\app\utils\decorators.py:39]
2025-05-22 21:48:20,533 [INFO] [127.0.0.1] [用户:unknown] 请求结束: endpoint=server_resource.get_servers, method=GET, status=200, time=0.036s, client_ip=127.0.0.1, user_id=unknown [in D:\PyProject\icms2\app\utils\decorators.py:50]
2025-05-22 21:48:20,543 [INFO] [N/A] [用户:N/A] 127.0.0.1 - - [22/May/2025 21:48:20] "GET /api/servers?page=1&per_page=20&hostname=&ip=&remark= HTTP/1.1" 200 - [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:48:20,750 [INFO] [127.0.0.1] [用户:unknown] 请求开始: endpoint=server_resource.get_servers, method=GET, url=http://localhost:5000/api/servers?page=1&per_page=20&hostname=&ip=&remark=, client_ip=127.0.0.1, user_id=unknown [in D:\PyProject\icms2\app\utils\decorators.py:39]
2025-05-22 21:48:20,784 [INFO] [127.0.0.1] [用户:unknown] 请求结束: endpoint=server_resource.get_servers, method=GET, status=200, time=0.034s, client_ip=127.0.0.1, user_id=unknown [in D:\PyProject\icms2\app\utils\decorators.py:50]
2025-05-22 21:48:20,803 [INFO] [N/A] [用户:N/A] 127.0.0.1 - - [22/May/2025 21:48:20] "GET /api/servers?page=1&per_page=20&hostname=&ip=&remark= HTTP/1.1" 200 - [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:50:40,565 [INFO] [N/A] [用户:N/A]  * Detected change in 'D:\\PyProject\\icms2\\app\\__init__.py', reloading [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:50:40,839 [INFO] [N/A] [用户:N/A]  * Restarting with stat [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:50:43,438 [INFO] [N/A] [用户:N/A] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:102]
2025-05-22 21:50:43,833 [INFO] [N/A] [用户:N/A] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:41]
2025-05-22 21:50:43,838 [INFO] [N/A] [用户:N/A] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:102]
2025-05-22 21:50:43,899 [INFO] [N/A] [用户:N/A] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:41]
2025-05-22 21:50:44,115 [INFO] [N/A] [用户:N/A] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:102]
2025-05-22 21:50:44,161 [INFO] [N/A] [用户:N/A] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:41]
2025-05-22 21:50:44,319 [WARNING] [N/A] [用户:N/A]  * Debugger is active! [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:50:44,350 [INFO] [N/A] [用户:N/A]  * Debugger PIN: 131-026-009 [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:50:56,105 [INFO] [N/A] [用户:N/A]  * Detected change in 'D:\\PyProject\\icms2\\app\\utils\\logger.py', reloading [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:50:56,376 [INFO] [N/A] [用户:N/A]  * Restarting with stat [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:50:58,694 [INFO] [N/A] [用户:N/A] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:112]
2025-05-22 21:50:59,314 [INFO] [N/A] [用户:N/A] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:41]
2025-05-22 21:50:59,325 [INFO] [N/A] [用户:N/A] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:112]
2025-05-22 21:50:59,407 [INFO] [N/A] [用户:N/A] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:41]
2025-05-22 21:50:59,655 [INFO] [N/A] [用户:N/A] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:112]
2025-05-22 21:50:59,752 [INFO] [N/A] [用户:N/A] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:41]
2025-05-22 21:51:00,096 [WARNING] [N/A] [用户:N/A]  * Debugger is active! [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:51:00,158 [INFO] [N/A] [用户:N/A]  * Debugger PIN: 131-026-009 [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:51:08,645 [INFO] [N/A] [用户:N/A]  * Detected change in 'D:\\PyProject\\icms2\\config.py', reloading [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:51:09,086 [INFO] [N/A] [用户:N/A]  * Restarting with stat [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:51:11,975 [INFO] [N/A] [用户:N/A(N/A)] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:112]
2025-05-22 21:51:12,559 [INFO] [N/A] [用户:N/A(N/A)] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:41]
2025-05-22 21:51:12,566 [INFO] [N/A] [用户:N/A(N/A)] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:112]
2025-05-22 21:51:12,653 [INFO] [N/A] [用户:N/A(N/A)] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:41]
2025-05-22 21:51:12,933 [INFO] [N/A] [用户:N/A(N/A)] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:112]
2025-05-22 21:51:13,011 [INFO] [N/A] [用户:N/A(N/A)] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:41]
2025-05-22 21:51:13,273 [WARNING] [N/A] [用户:N/A(N/A)]  * Debugger is active! [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:51:13,330 [INFO] [N/A] [用户:N/A(N/A)]  * Debugger PIN: 131-026-009 [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:51:18,502 [INFO] [127.0.0.1] [用户:unknown(unknown)] 请求开始: endpoint=auth.validate_token, method=GET, url=http://localhost:5000/api/validate-token, client_ip=127.0.0.1, user_id=unknown [in D:\PyProject\icms2\app\utils\decorators.py:39]
2025-05-22 21:51:18,732 [INFO] [127.0.0.1] [用户:unknown(unknown)] 请求结束: endpoint=auth.validate_token, method=GET, status=200, time=0.230s, client_ip=127.0.0.1, user_id=unknown [in D:\PyProject\icms2\app\utils\decorators.py:50]
2025-05-22 21:51:18,744 [INFO] [N/A] [用户:N/A(N/A)] 127.0.0.1 - - [22/May/2025 21:51:18] "GET /api/validate-token HTTP/1.1" 200 - [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:51:18,758 [INFO] [127.0.0.1] [用户:unknown(unknown)] 请求开始: endpoint=auth.validate_token, method=GET, url=http://localhost:5000/api/validate-token, client_ip=127.0.0.1, user_id=unknown [in D:\PyProject\icms2\app\utils\decorators.py:39]
2025-05-22 21:51:18,899 [INFO] [127.0.0.1] [用户:unknown(unknown)] 请求结束: endpoint=auth.validate_token, method=GET, status=200, time=0.141s, client_ip=127.0.0.1, user_id=unknown [in D:\PyProject\icms2\app\utils\decorators.py:50]
2025-05-22 21:51:18,911 [INFO] [N/A] [用户:N/A(N/A)] 127.0.0.1 - - [22/May/2025 21:51:18] "GET /api/validate-token HTTP/1.1" 200 - [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:51:22,811 [INFO] [N/A] [用户:N/A(N/A)]  * Detected change in 'D:\\PyProject\\icms2\\app\\utils\\decorators.py', reloading [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:51:23,079 [INFO] [N/A] [用户:N/A]  * Restarting with stat [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:51:35,020 [INFO] [N/A] [用户:N/A(N/A)] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:112]
2025-05-22 21:51:35,258 [INFO] [N/A] [用户:N/A(N/A)] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:41]
2025-05-22 21:51:35,262 [INFO] [N/A] [用户:N/A(N/A)] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:112]
2025-05-22 21:51:35,301 [INFO] [N/A] [用户:N/A(N/A)] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:41]
2025-05-22 21:51:54,774 [INFO] [N/A] [用户:N/A(N/A)] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:112]
2025-05-22 21:51:54,809 [INFO] [N/A] [用户:N/A(N/A)] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:41]
2025-05-22 21:51:54,964 [WARNING] [N/A] [用户:N/A(N/A)]  * Debugger is active! [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:51:55,005 [INFO] [N/A] [用户:N/A(N/A)]  * Debugger PIN: 131-026-009 [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:51:58,924 [INFO] [N/A] [用户:N/A(N/A)]  * Detected change in 'D:\\PyProject\\icms2\\app\\utils\\decorators.py', reloading [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:51:59,162 [INFO] [N/A] [用户:N/A]  * Restarting with stat [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:52:01,890 [INFO] [N/A] [用户:N/A(N/A)] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:112]
2025-05-22 21:52:02,375 [INFO] [N/A] [用户:N/A(N/A)] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:41]
2025-05-22 21:52:02,384 [INFO] [N/A] [用户:N/A(N/A)] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:112]
2025-05-22 21:52:02,465 [INFO] [N/A] [用户:N/A(N/A)] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:41]
2025-05-22 21:52:02,698 [INFO] [N/A] [用户:N/A(N/A)] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:112]
2025-05-22 21:52:02,766 [INFO] [N/A] [用户:N/A(N/A)] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:41]
2025-05-22 21:52:03,063 [WARNING] [N/A] [用户:N/A(N/A)]  * Debugger is active! [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:52:03,131 [INFO] [N/A] [用户:N/A(N/A)]  * Debugger PIN: 131-026-009 [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:52:11,811 [INFO] [N/A] [用户:N/A(N/A)]  * Detected change in 'D:\\PyProject\\icms2\\app\\utils\\decorators.py', reloading [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:52:12,016 [INFO] [N/A] [用户:N/A]  * Restarting with stat [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:52:13,989 [INFO] [N/A] [用户:N/A(N/A)] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:112]
2025-05-22 21:52:14,488 [INFO] [N/A] [用户:N/A(N/A)] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:41]
2025-05-22 21:52:14,497 [INFO] [N/A] [用户:N/A(N/A)] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:112]
2025-05-22 21:52:14,566 [INFO] [N/A] [用户:N/A(N/A)] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:41]
2025-05-22 21:52:14,813 [INFO] [N/A] [用户:N/A(N/A)] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:112]
2025-05-22 21:52:14,970 [INFO] [N/A] [用户:N/A(N/A)] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:41]
2025-05-22 21:52:15,266 [WARNING] [N/A] [用户:N/A(N/A)]  * Debugger is active! [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:52:15,351 [INFO] [N/A] [用户:N/A(N/A)]  * Debugger PIN: 131-026-009 [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:52:25,174 [INFO] [N/A] [用户:N/A(N/A)]  * Detected change in 'D:\\PyProject\\icms2\\app\\utils\\decorators.py', reloading [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:52:25,575 [INFO] [N/A] [用户:N/A]  * Restarting with stat [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:52:28,590 [INFO] [N/A] [用户:N/A(N/A)] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:112]
2025-05-22 21:52:29,071 [INFO] [N/A] [用户:N/A(N/A)] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:41]
2025-05-22 21:52:29,077 [INFO] [N/A] [用户:N/A(N/A)] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:112]
2025-05-22 21:52:29,261 [INFO] [N/A] [用户:N/A(N/A)] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:41]
2025-05-22 21:52:29,462 [INFO] [N/A] [用户:N/A(N/A)] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:112]
2025-05-22 21:52:29,525 [INFO] [N/A] [用户:N/A(N/A)] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:41]
2025-05-22 21:52:29,707 [WARNING] [N/A] [用户:N/A(N/A)]  * Debugger is active! [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:52:29,759 [INFO] [N/A] [用户:N/A(N/A)]  * Debugger PIN: 131-026-009 [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:52:49,690 [INFO] [N/A] [用户:N/A(N/A)]  * Detected change in 'D:\\PyProject\\icms2\\app\\utils\\decorators.py', reloading [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:52:49,963 [INFO] [N/A] [用户:N/A]  * Restarting with stat [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:52:53,090 [INFO] [N/A] [用户:N/A(N/A)] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:112]
2025-05-22 21:52:53,439 [INFO] [N/A] [用户:N/A(N/A)] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:41]
2025-05-22 21:52:53,444 [INFO] [N/A] [用户:N/A(N/A)] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:112]
2025-05-22 21:52:53,516 [INFO] [N/A] [用户:N/A(N/A)] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:41]
2025-05-22 21:52:53,717 [INFO] [N/A] [用户:N/A(N/A)] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:112]
2025-05-22 21:52:53,759 [INFO] [N/A] [用户:N/A(N/A)] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:41]
2025-05-22 21:52:53,920 [WARNING] [N/A] [用户:N/A(N/A)]  * Debugger is active! [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:52:53,953 [INFO] [N/A] [用户:N/A(N/A)]  * Debugger PIN: 131-026-009 [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:52:58,890 [INFO] [N/A] [用户:N/A(N/A)]  * Detected change in 'D:\\PyProject\\icms2\\app\\utils\\decorators.py', reloading [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:52:59,147 [INFO] [N/A] [用户:N/A]  * Restarting with stat [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:53:01,467 [INFO] [N/A] [用户:N/A(N/A)] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:112]
2025-05-22 21:53:01,843 [INFO] [N/A] [用户:N/A(N/A)] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:41]
2025-05-22 21:53:01,850 [INFO] [N/A] [用户:N/A(N/A)] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:112]
2025-05-22 21:53:01,915 [INFO] [N/A] [用户:N/A(N/A)] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:41]
2025-05-22 21:53:02,193 [INFO] [N/A] [用户:N/A(N/A)] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:112]
2025-05-22 21:53:02,254 [INFO] [N/A] [用户:N/A(N/A)] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:41]
2025-05-22 21:53:02,435 [WARNING] [N/A] [用户:N/A(N/A)]  * Debugger is active! [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:53:02,502 [INFO] [N/A] [用户:N/A(N/A)]  * Debugger PIN: 131-026-009 [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:53:09,938 [INFO] [N/A] [用户:N/A(N/A)]  * Detected change in 'D:\\PyProject\\icms2\\app\\utils\\decorators.py', reloading [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:53:10,344 [INFO] [N/A] [用户:N/A]  * Restarting with stat [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:53:13,005 [INFO] [N/A] [用户:N/A(N/A)] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:112]
2025-05-22 21:53:13,375 [INFO] [N/A] [用户:N/A(N/A)] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:41]
2025-05-22 21:53:13,380 [INFO] [N/A] [用户:N/A(N/A)] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:112]
2025-05-22 21:53:13,437 [INFO] [N/A] [用户:N/A(N/A)] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:41]
2025-05-22 21:53:13,646 [INFO] [N/A] [用户:N/A(N/A)] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:112]
2025-05-22 21:53:13,701 [INFO] [N/A] [用户:N/A(N/A)] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:41]
2025-05-22 21:53:13,878 [WARNING] [N/A] [用户:N/A(N/A)]  * Debugger is active! [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:53:13,996 [INFO] [N/A] [用户:N/A(N/A)]  * Debugger PIN: 131-026-009 [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:54:02,525 [INFO] [127.0.0.1] [用户:unknown(unknown)] 请求开始: endpoint=auth.validate_token, method=GET, url=http://localhost:5000/api/validate-token, client_ip=127.0.0.1, username=unknown, user_id=unknown [in D:\PyProject\icms2\app\utils\decorators.py:50]
2025-05-22 21:54:02,853 [INFO] [127.0.0.1] [用户:unknown(unknown)] 请求结束: endpoint=auth.validate_token, method=GET, status=200, time=0.328s, client_ip=127.0.0.1, username=unknown, user_id=unknown [in D:\PyProject\icms2\app\utils\decorators.py:61]
2025-05-22 21:54:02,865 [INFO] [N/A] [用户:N/A(N/A)] 127.0.0.1 - - [22/May/2025 21:54:02] "GET /api/validate-token HTTP/1.1" 200 - [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:54:02,904 [INFO] [127.0.0.1] [用户:unknown(unknown)] 请求开始: endpoint=auth.validate_token, method=GET, url=http://localhost:5000/api/validate-token, client_ip=127.0.0.1, username=unknown, user_id=unknown [in D:\PyProject\icms2\app\utils\decorators.py:50]
2025-05-22 21:54:03,065 [INFO] [127.0.0.1] [用户:unknown(unknown)] 请求结束: endpoint=auth.validate_token, method=GET, status=200, time=0.161s, client_ip=127.0.0.1, username=unknown, user_id=unknown [in D:\PyProject\icms2\app\utils\decorators.py:61]
2025-05-22 21:54:03,076 [INFO] [N/A] [用户:N/A(N/A)] 127.0.0.1 - - [22/May/2025 21:54:03] "GET /api/validate-token HTTP/1.1" 200 - [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:54:03,092 [INFO] [127.0.0.1] [用户:unknown(unknown)] 请求开始: endpoint=auth.validate_token, method=GET, url=http://localhost:5000/api/validate-token, client_ip=127.0.0.1, username=unknown, user_id=unknown [in D:\PyProject\icms2\app\utils\decorators.py:50]
2025-05-22 21:54:03,239 [INFO] [127.0.0.1] [用户:unknown(unknown)] 请求结束: endpoint=auth.validate_token, method=GET, status=200, time=0.148s, client_ip=127.0.0.1, username=unknown, user_id=unknown [in D:\PyProject\icms2\app\utils\decorators.py:61]
2025-05-22 21:54:03,251 [INFO] [N/A] [用户:N/A(N/A)] 127.0.0.1 - - [22/May/2025 21:54:03] "GET /api/validate-token HTTP/1.1" 200 - [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:54:03,263 [INFO] [127.0.0.1] [用户:unknown(unknown)] 请求开始: endpoint=auth.validate_token, method=GET, url=http://localhost:5000/api/validate-token, client_ip=127.0.0.1, username=unknown, user_id=unknown [in D:\PyProject\icms2\app\utils\decorators.py:50]
2025-05-22 21:54:03,436 [INFO] [127.0.0.1] [用户:unknown(unknown)] 请求结束: endpoint=auth.validate_token, method=GET, status=200, time=0.173s, client_ip=127.0.0.1, username=unknown, user_id=unknown [in D:\PyProject\icms2\app\utils\decorators.py:61]
2025-05-22 21:54:03,449 [INFO] [N/A] [用户:N/A(N/A)] 127.0.0.1 - - [22/May/2025 21:54:03] "GET /api/validate-token HTTP/1.1" 200 - [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:54:21,555 [INFO] [127.0.0.1] [用户:unknown(unknown)] 请求开始: endpoint=auth.login, method=POST, url=http://localhost:5000/api/login, client_ip=127.0.0.1, username=unknown, user_id=unknown [in D:\PyProject\icms2\app\utils\decorators.py:50]
2025-05-22 21:54:21,556 [INFO] [127.0.0.1] [用户:unknown(unknown)] 操作开始: 类型=用户登录, endpoint=auth.login, method=POST, client_ip=127.0.0.1, username=unknown, user_id=unknown, 数据={'username': 'gerujun', 'password': 'MCcjchgrj000', 'user_type': 'local'} [in D:\PyProject\icms2\app\utils\decorators.py:128]
2025-05-22 21:54:21,574 [INFO] [127.0.0.1] [用户:unknown(unknown)] Trying LDAP config: id=5, server=*********** [in D:\PyProject\icms2\app\routes\auth.py:220]
2025-05-22 21:54:22,532 [INFO] [127.0.0.1] [用户:unknown(unknown)] LDAP search: config_id=5, base=OU=信息技术部,OU=北京职场,OU=内部全体员工,OU=和泰人寿员工,DC=ht,DC=com, filter=(|(cn=gerujun)(sAMAccountName=gerujun)) [in D:\PyProject\icms2\app\routes\auth.py:235]
2025-05-22 21:54:22,573 [INFO] [127.0.0.1] [用户:unknown(unknown)] Trying LDAP config: id=8, server=*********** [in D:\PyProject\icms2\app\routes\auth.py:220]
2025-05-22 21:54:23,592 [INFO] [127.0.0.1] [用户:unknown(unknown)] LDAP search: config_id=8, base=OU=信息技术部,OU=深圳职场,OU=内部全体员工,OU=和泰人寿员工,DC=ht,DC=com, filter=(|(cn=gerujun)(sAMAccountName=gerujun)) [in D:\PyProject\icms2\app\routes\auth.py:235]
2025-05-22 21:54:24,745 [INFO] [127.0.0.1] [用户:unknown(unknown)] Login successful: user=gerujun, ip=127.0.0.1, type=LDAP, config_id=8 [in D:\PyProject\icms2\app\routes\auth.py:275]
2025-05-22 21:54:24,746 [INFO] [127.0.0.1] [用户:unknown(unknown)] 操作成功: 类型=用户登录, endpoint=auth.login, method=POST, client_ip=127.0.0.1, username=unknown, user_id=unknown [in D:\PyProject\icms2\app\utils\decorators.py:138]
2025-05-22 21:54:24,746 [INFO] [127.0.0.1] [用户:unknown(unknown)] 请求结束: endpoint=auth.login, method=POST, status=200, time=3.191s, client_ip=127.0.0.1, username=unknown, user_id=unknown [in D:\PyProject\icms2\app\utils\decorators.py:61]
2025-05-22 21:54:24,760 [INFO] [N/A] [用户:N/A(N/A)] 127.0.0.1 - - [22/May/2025 21:54:24] "POST /api/login HTTP/1.1" 200 - [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:56:29,068 [INFO] [N/A] [用户:N/A(N/A)]  * Detected change in 'D:\\PyProject\\icms2\\app\\routes\\auth.py', reloading [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:56:29,430 [INFO] [N/A] [用户:N/A]  * Restarting with stat [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:56:32,858 [INFO] [N/A] [用户:N/A(N/A)] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:112]
2025-05-22 21:56:33,386 [INFO] [N/A] [用户:N/A(N/A)] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:41]
2025-05-22 21:56:33,396 [INFO] [N/A] [用户:N/A(N/A)] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:112]
2025-05-22 21:56:33,462 [INFO] [N/A] [用户:N/A(N/A)] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:41]
2025-05-22 21:56:33,684 [INFO] [N/A] [用户:N/A(N/A)] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:112]
2025-05-22 21:56:33,742 [INFO] [N/A] [用户:N/A(N/A)] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:41]
2025-05-22 21:56:33,921 [WARNING] [N/A] [用户:N/A(N/A)]  * Debugger is active! [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:56:33,975 [INFO] [N/A] [用户:N/A(N/A)]  * Debugger PIN: 131-026-009 [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:56:49,173 [INFO] [N/A] [用户:N/A(N/A)]  * Detected change in 'D:\\PyProject\\icms2\\app\\routes\\auth.py', reloading [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:56:49,469 [INFO] [N/A] [用户:N/A]  * Restarting with stat [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:56:52,429 [INFO] [N/A] [用户:N/A(N/A)] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:112]
2025-05-22 21:56:52,905 [INFO] [N/A] [用户:N/A(N/A)] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:41]
2025-05-22 21:56:52,911 [INFO] [N/A] [用户:N/A(N/A)] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:112]
2025-05-22 21:56:52,975 [INFO] [N/A] [用户:N/A(N/A)] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:41]
2025-05-22 21:56:53,192 [INFO] [N/A] [用户:N/A(N/A)] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:112]
2025-05-22 21:56:53,255 [INFO] [N/A] [用户:N/A(N/A)] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:41]
2025-05-22 21:56:53,455 [WARNING] [N/A] [用户:N/A(N/A)]  * Debugger is active! [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:56:53,516 [INFO] [N/A] [用户:N/A(N/A)]  * Debugger PIN: 131-026-009 [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:57:10,949 [INFO] [N/A] [用户:N/A(N/A)]  * Detected change in 'D:\\PyProject\\icms2\\app\\routes\\auth.py', reloading [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:57:11,198 [INFO] [N/A] [用户:N/A]  * Restarting with stat [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:57:14,170 [INFO] [N/A] [用户:N/A(N/A)] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:112]
2025-05-22 21:57:14,647 [INFO] [N/A] [用户:N/A(N/A)] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:41]
2025-05-22 21:57:14,655 [INFO] [N/A] [用户:N/A(N/A)] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:112]
2025-05-22 21:57:14,756 [INFO] [N/A] [用户:N/A(N/A)] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:41]
2025-05-22 21:57:14,967 [INFO] [N/A] [用户:N/A(N/A)] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:112]
2025-05-22 21:57:15,027 [INFO] [N/A] [用户:N/A(N/A)] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:41]
2025-05-22 21:57:15,212 [WARNING] [N/A] [用户:N/A(N/A)]  * Debugger is active! [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:57:15,262 [INFO] [N/A] [用户:N/A(N/A)]  * Debugger PIN: 131-026-009 [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:57:43,039 [INFO] [N/A] [用户:N/A(N/A)]  * Detected change in 'D:\\PyProject\\icms2\\app\\routes\\auth.py', reloading [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:57:43,269 [INFO] [N/A] [用户:N/A]  * Restarting with stat [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:57:45,493 [INFO] [N/A] [用户:N/A(N/A)] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:112]
2025-05-22 21:57:46,028 [INFO] [N/A] [用户:N/A(N/A)] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:41]
2025-05-22 21:57:46,035 [INFO] [N/A] [用户:N/A(N/A)] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:112]
2025-05-22 21:57:46,119 [INFO] [N/A] [用户:N/A(N/A)] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:41]
2025-05-22 21:57:46,382 [INFO] [N/A] [用户:N/A(N/A)] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:112]
2025-05-22 21:57:46,447 [INFO] [N/A] [用户:N/A(N/A)] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:41]
2025-05-22 21:57:46,666 [WARNING] [N/A] [用户:N/A(N/A)]  * Debugger is active! [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:57:46,732 [INFO] [N/A] [用户:N/A(N/A)]  * Debugger PIN: 131-026-009 [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:57:54,204 [INFO] [N/A] [用户:N/A(N/A)]  * Detected change in 'D:\\PyProject\\icms2\\app\\routes\\auth.py', reloading [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:57:54,978 [INFO] [N/A] [用户:N/A]  * Restarting with stat [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:57:57,351 [INFO] [N/A] [用户:N/A(N/A)] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:112]
2025-05-22 21:57:57,794 [INFO] [N/A] [用户:N/A(N/A)] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:41]
2025-05-22 21:57:57,801 [INFO] [N/A] [用户:N/A(N/A)] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:112]
2025-05-22 21:57:57,883 [INFO] [N/A] [用户:N/A(N/A)] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:41]
2025-05-22 21:57:58,142 [INFO] [N/A] [用户:N/A(N/A)] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:112]
2025-05-22 21:57:58,214 [INFO] [N/A] [用户:N/A(N/A)] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:41]
2025-05-22 21:57:58,415 [WARNING] [N/A] [用户:N/A(N/A)]  * Debugger is active! [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:57:58,491 [INFO] [N/A] [用户:N/A(N/A)]  * Debugger PIN: 131-026-009 [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:58:05,917 [INFO] [N/A] [用户:N/A(N/A)]  * Detected change in 'D:\\PyProject\\icms2\\app\\routes\\auth.py', reloading [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:58:06,189 [INFO] [N/A] [用户:N/A]  * Restarting with stat [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:58:08,798 [INFO] [N/A] [用户:N/A(N/A)] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:112]
2025-05-22 21:58:09,258 [INFO] [N/A] [用户:N/A(N/A)] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:41]
2025-05-22 21:58:09,266 [INFO] [N/A] [用户:N/A(N/A)] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:112]
2025-05-22 21:58:09,342 [INFO] [N/A] [用户:N/A(N/A)] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:41]
2025-05-22 21:58:09,579 [INFO] [N/A] [用户:N/A(N/A)] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:112]
2025-05-22 21:58:09,660 [INFO] [N/A] [用户:N/A(N/A)] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:41]
2025-05-22 21:58:10,006 [WARNING] [N/A] [用户:N/A(N/A)]  * Debugger is active! [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:58:10,087 [INFO] [N/A] [用户:N/A(N/A)]  * Debugger PIN: 131-026-009 [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:58:32,930 [INFO] [127.0.0.1] [用户:unknown(unknown)] 请求开始: endpoint=auth.validate_token, method=GET, url=http://localhost:5000/api/validate-token, client_ip=127.0.0.1, username=unknown, user_id=unknown [in D:\PyProject\icms2\app\utils\decorators.py:50]
2025-05-22 21:58:33,250 [INFO] [127.0.0.1] [用户:unknown(unknown)] 请求结束: endpoint=auth.validate_token, method=GET, status=200, time=0.320s, client_ip=127.0.0.1, username=unknown, user_id=unknown [in D:\PyProject\icms2\app\utils\decorators.py:61]
2025-05-22 21:58:33,265 [INFO] [N/A] [用户:N/A(N/A)] 127.0.0.1 - - [22/May/2025 21:58:33] "GET /api/validate-token HTTP/1.1" 200 - [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:58:33,349 [INFO] [127.0.0.1] [用户:unknown(unknown)] 请求开始: endpoint=auth.validate_token, method=GET, url=http://localhost:5000/api/validate-token, client_ip=127.0.0.1, username=unknown, user_id=unknown [in D:\PyProject\icms2\app\utils\decorators.py:50]
2025-05-22 21:58:33,527 [INFO] [127.0.0.1] [用户:unknown(unknown)] 请求结束: endpoint=auth.validate_token, method=GET, status=200, time=0.178s, client_ip=127.0.0.1, username=unknown, user_id=unknown [in D:\PyProject\icms2\app\utils\decorators.py:61]
2025-05-22 21:58:33,539 [INFO] [N/A] [用户:N/A(N/A)] 127.0.0.1 - - [22/May/2025 21:58:33] "GET /api/validate-token HTTP/1.1" 200 - [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:58:33,555 [INFO] [127.0.0.1] [用户:unknown(unknown)] 请求开始: endpoint=auth.validate_token, method=GET, url=http://localhost:5000/api/validate-token, client_ip=127.0.0.1, username=unknown, user_id=unknown [in D:\PyProject\icms2\app\utils\decorators.py:50]
2025-05-22 21:58:33,713 [INFO] [127.0.0.1] [用户:unknown(unknown)] 请求结束: endpoint=auth.validate_token, method=GET, status=200, time=0.157s, client_ip=127.0.0.1, username=unknown, user_id=unknown [in D:\PyProject\icms2\app\utils\decorators.py:61]
2025-05-22 21:58:33,724 [INFO] [N/A] [用户:N/A(N/A)] 127.0.0.1 - - [22/May/2025 21:58:33] "GET /api/validate-token HTTP/1.1" 200 - [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:58:33,858 [INFO] [127.0.0.1] [用户:unknown(unknown)] 请求开始: endpoint=auth.validate_token, method=GET, url=http://localhost:5000/api/validate-token, client_ip=127.0.0.1, username=unknown, user_id=unknown [in D:\PyProject\icms2\app\utils\decorators.py:50]
2025-05-22 21:58:34,038 [INFO] [127.0.0.1] [用户:unknown(unknown)] 请求结束: endpoint=auth.validate_token, method=GET, status=200, time=0.180s, client_ip=127.0.0.1, username=unknown, user_id=unknown [in D:\PyProject\icms2\app\utils\decorators.py:61]
2025-05-22 21:58:34,048 [INFO] [N/A] [用户:N/A(N/A)] 127.0.0.1 - - [22/May/2025 21:58:34] "GET /api/validate-token HTTP/1.1" 200 - [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:58:42,339 [INFO] [127.0.0.1] [用户:unknown(unknown)] 请求开始: endpoint=auth.login, method=POST, url=http://localhost:5000/api/login, client_ip=127.0.0.1, username=unknown, user_id=unknown [in D:\PyProject\icms2\app\utils\decorators.py:50]
2025-05-22 21:58:42,358 [INFO] [127.0.0.1] [用户:unknown(unknown)] Trying LDAP config: id=5, server=*********** [in D:\PyProject\icms2\app\routes\auth.py:227]
2025-05-22 21:58:43,351 [INFO] [127.0.0.1] [用户:unknown(unknown)] LDAP search: config_id=5, base=OU=信息技术部,OU=北京职场,OU=内部全体员工,OU=和泰人寿员工,DC=ht,DC=com, filter=(|(cn=gerujun)(sAMAccountName=gerujun)) [in D:\PyProject\icms2\app\routes\auth.py:242]
2025-05-22 21:58:43,398 [INFO] [127.0.0.1] [用户:unknown(unknown)] Trying LDAP config: id=8, server=*********** [in D:\PyProject\icms2\app\routes\auth.py:227]
2025-05-22 21:58:44,362 [INFO] [127.0.0.1] [用户:unknown(unknown)] LDAP search: config_id=8, base=OU=信息技术部,OU=深圳职场,OU=内部全体员工,OU=和泰人寿员工,DC=ht,DC=com, filter=(|(cn=gerujun)(sAMAccountName=gerujun)) [in D:\PyProject\icms2\app\routes\auth.py:242]
2025-05-22 21:58:45,511 [INFO] [127.0.0.1] [用户:gerujun(11)] Login successful: user=gerujun, ip=127.0.0.1, type=LDAP, config_id=8 [in D:\PyProject\icms2\app\routes\auth.py:287]
2025-05-22 21:58:45,512 [INFO] [127.0.0.1] [用户:gerujun(11)] 操作成功: 类型=用户登录, endpoint=auth.login, method=POST, client_ip=127.0.0.1, username=gerujun, user_id=11 [in D:\PyProject\icms2\app\routes\auth.py:290]
2025-05-22 21:58:45,513 [INFO] [127.0.0.1] [用户:gerujun(11)] 请求结束: endpoint=auth.login, method=POST, status=200, time=3.174s, client_ip=127.0.0.1, username=unknown, user_id=unknown [in D:\PyProject\icms2\app\utils\decorators.py:61]
2025-05-22 21:58:45,526 [INFO] [N/A] [用户:N/A(N/A)] 127.0.0.1 - - [22/May/2025 21:58:45] "POST /api/login HTTP/1.1" 200 - [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:58:58,663 [INFO] [127.0.0.1] [用户:gerujun(11)] 请求开始: endpoint=application.get_applications, method=GET, url=http://localhost:5000/api/applications?page=1&per_page=30&app_en_name=&app_cn_name=&app_admin=, client_ip=127.0.0.1, username=gerujun, user_id=11 [in D:\PyProject\icms2\app\utils\decorators.py:50]
2025-05-22 21:58:58,691 [INFO] [127.0.0.1] [用户:gerujun(11)] 请求结束: endpoint=application.get_applications, method=GET, status=200, time=0.028s, client_ip=127.0.0.1, username=gerujun, user_id=11 [in D:\PyProject\icms2\app\utils\decorators.py:61]
2025-05-22 21:58:58,705 [INFO] [N/A] [用户:N/A(N/A)] 127.0.0.1 - - [22/May/2025 21:58:58] "GET /api/applications?page=1&per_page=30&app_en_name=&app_cn_name=&app_admin= HTTP/1.1" 200 - [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:58:59,035 [INFO] [127.0.0.1] [用户:gerujun(11)] 请求开始: endpoint=application.get_applications, method=GET, url=http://localhost:5000/api/applications?page=1&per_page=30&app_en_name=&app_cn_name=&app_admin=, client_ip=127.0.0.1, username=gerujun, user_id=11 [in D:\PyProject\icms2\app\utils\decorators.py:50]
2025-05-22 21:58:59,058 [INFO] [127.0.0.1] [用户:gerujun(11)] 请求结束: endpoint=application.get_applications, method=GET, status=200, time=0.022s, client_ip=127.0.0.1, username=gerujun, user_id=11 [in D:\PyProject\icms2\app\utils\decorators.py:61]
2025-05-22 21:58:59,076 [INFO] [N/A] [用户:N/A(N/A)] 127.0.0.1 - - [22/May/2025 21:58:59] "GET /api/applications?page=1&per_page=30&app_en_name=&app_cn_name=&app_admin= HTTP/1.1" 200 - [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:59:25,805 [INFO] [127.0.0.1] [用户:gerujun(11)] 请求开始: endpoint=application.update_application, method=PUT, url=http://localhost:5000/api/applications/4, client_ip=127.0.0.1, username=gerujun, user_id=11 [in D:\PyProject\icms2\app\utils\decorators.py:50]
2025-05-22 21:59:25,806 [INFO] [127.0.0.1] [用户:gerujun(11)] 操作开始: 类型=更新应用系统, endpoint=application.update_application, method=PUT, client_ip=127.0.0.1, username=gerujun, user_id=11, 数据={'app_admin': '678', 'remark': ''} [in D:\PyProject\icms2\app\utils\decorators.py:128]
2025-05-22 21:59:25,855 [INFO] [127.0.0.1] [用户:gerujun(11)] 操作成功: 类型=更新应用系统, endpoint=application.update_application, method=PUT, client_ip=127.0.0.1, username=gerujun, user_id=11 [in D:\PyProject\icms2\app\utils\decorators.py:138]
2025-05-22 21:59:25,862 [INFO] [127.0.0.1] [用户:gerujun(11)] 请求结束: endpoint=application.update_application, method=PUT, status=200, time=0.057s, client_ip=127.0.0.1, username=gerujun, user_id=11 [in D:\PyProject\icms2\app\utils\decorators.py:61]
2025-05-22 21:59:25,873 [INFO] [N/A] [用户:N/A(N/A)] 127.0.0.1 - - [22/May/2025 21:59:25] "PUT /api/applications/4 HTTP/1.1" 200 - [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 21:59:26,191 [INFO] [127.0.0.1] [用户:gerujun(11)] 请求开始: endpoint=application.get_applications, method=GET, url=http://localhost:5000/api/applications?page=1&per_page=30&app_en_name=&app_cn_name=&app_admin=, client_ip=127.0.0.1, username=gerujun, user_id=11 [in D:\PyProject\icms2\app\utils\decorators.py:50]
2025-05-22 21:59:26,218 [INFO] [127.0.0.1] [用户:gerujun(11)] 请求结束: endpoint=application.get_applications, method=GET, status=200, time=0.028s, client_ip=127.0.0.1, username=gerujun, user_id=11 [in D:\PyProject\icms2\app\utils\decorators.py:61]
2025-05-22 21:59:26,230 [INFO] [N/A] [用户:N/A(N/A)] 127.0.0.1 - - [22/May/2025 21:59:26] "GET /api/applications?page=1&per_page=30&app_en_name=&app_cn_name=&app_admin= HTTP/1.1" 200 - [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 22:00:32,054 [INFO] [N/A] [用户:N/A(N/A)] 127.0.0.1 - - [22/May/2025 22:00:32] "GET /api/application-environments?page=1&per_page=20&app_en_name=&app_cn_name= HTTP/1.1" 200 - [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 22:00:32,308 [INFO] [N/A] [用户:N/A(N/A)] 127.0.0.1 - - [22/May/2025 22:00:32] "GET /api/application-environments?page=1&per_page=20&app_en_name=&app_cn_name= HTTP/1.1" 200 - [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 22:00:54,381 [INFO] [127.0.0.1] [用户:gerujun(11)] 请求开始: endpoint=server_resource.get_servers, method=GET, url=http://localhost:5000/api/servers?page=1&per_page=20&hostname=&ip=&remark=, client_ip=127.0.0.1, username=gerujun, user_id=11 [in D:\PyProject\icms2\app\utils\decorators.py:50]
2025-05-22 22:00:54,417 [INFO] [127.0.0.1] [用户:gerujun(11)] 请求结束: endpoint=server_resource.get_servers, method=GET, status=200, time=0.035s, client_ip=127.0.0.1, username=gerujun, user_id=11 [in D:\PyProject\icms2\app\utils\decorators.py:61]
2025-05-22 22:00:54,429 [INFO] [N/A] [用户:N/A(N/A)] 127.0.0.1 - - [22/May/2025 22:00:54] "GET /api/servers?page=1&per_page=20&hostname=&ip=&remark= HTTP/1.1" 200 - [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 22:00:54,807 [INFO] [127.0.0.1] [用户:gerujun(11)] 请求开始: endpoint=server_resource.get_servers, method=GET, url=http://localhost:5000/api/servers?page=1&per_page=20&hostname=&ip=&remark=, client_ip=127.0.0.1, username=gerujun, user_id=11 [in D:\PyProject\icms2\app\utils\decorators.py:50]
2025-05-22 22:00:54,838 [INFO] [127.0.0.1] [用户:gerujun(11)] 请求结束: endpoint=server_resource.get_servers, method=GET, status=200, time=0.031s, client_ip=127.0.0.1, username=gerujun, user_id=11 [in D:\PyProject\icms2\app\utils\decorators.py:61]
2025-05-22 22:00:54,850 [INFO] [N/A] [用户:N/A(N/A)] 127.0.0.1 - - [22/May/2025 22:00:54] "GET /api/servers?page=1&per_page=20&hostname=&ip=&remark= HTTP/1.1" 200 - [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 22:03:24,424 [INFO] [N/A] [用户:N/A(N/A)]  * Detected change in 'D:\\PyProject\\icms2\\app\\routes\\auth.py', reloading [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 22:03:24,816 [INFO] [N/A] [用户:N/A]  * Restarting with stat [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 22:03:27,921 [INFO] [N/A] [用户:N/A(N/A)] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:112]
2025-05-22 22:03:28,363 [INFO] [N/A] [用户:N/A(N/A)] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:41]
2025-05-22 22:03:28,369 [INFO] [N/A] [用户:N/A(N/A)] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:112]
2025-05-22 22:03:28,447 [INFO] [N/A] [用户:N/A(N/A)] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:41]
2025-05-22 22:03:28,659 [INFO] [N/A] [用户:N/A(N/A)] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:112]
2025-05-22 22:03:28,712 [INFO] [N/A] [用户:N/A(N/A)] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:41]
2025-05-22 22:03:28,908 [WARNING] [N/A] [用户:N/A(N/A)]  * Debugger is active! [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 22:03:28,961 [INFO] [N/A] [用户:N/A(N/A)]  * Debugger PIN: 131-026-009 [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 22:03:43,037 [INFO] [N/A] [用户:N/A(N/A)]  * Detected change in 'D:\\PyProject\\icms2\\app\\utils\\auth_middleware.py', reloading [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 22:03:43,291 [INFO] [N/A] [用户:N/A]  * Restarting with stat [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 22:03:46,026 [INFO] [N/A] [用户:N/A(N/A)] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:112]
2025-05-22 22:03:46,631 [INFO] [N/A] [用户:N/A(N/A)] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:41]
2025-05-22 22:03:46,638 [INFO] [N/A] [用户:N/A(N/A)] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:112]
2025-05-22 22:03:46,725 [INFO] [N/A] [用户:N/A(N/A)] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:41]
2025-05-22 22:03:46,945 [INFO] [N/A] [用户:N/A(N/A)] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:112]
2025-05-22 22:03:47,021 [INFO] [N/A] [用户:N/A(N/A)] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:41]
2025-05-22 22:03:47,217 [WARNING] [N/A] [用户:N/A(N/A)]  * Debugger is active! [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 22:03:47,267 [INFO] [N/A] [用户:N/A(N/A)]  * Debugger PIN: 131-026-009 [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 22:04:07,811 [INFO] [N/A] [用户:N/A(N/A)]  * Detected change in 'D:\\PyProject\\icms2\\app\\routes\\server_resource.py', reloading [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 22:04:08,125 [INFO] [N/A] [用户:N/A]  * Restarting with stat [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 22:04:10,579 [INFO] [N/A] [用户:N/A(N/A)] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:112]
2025-05-22 22:04:11,115 [INFO] [N/A] [用户:N/A(N/A)] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:41]
2025-05-22 22:04:11,127 [INFO] [N/A] [用户:N/A(N/A)] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:112]
2025-05-22 22:04:11,224 [INFO] [N/A] [用户:N/A(N/A)] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:41]
2025-05-22 22:04:11,471 [INFO] [N/A] [用户:N/A(N/A)] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:112]
2025-05-22 22:04:11,532 [INFO] [N/A] [用户:N/A(N/A)] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:41]
2025-05-22 22:04:11,784 [WARNING] [N/A] [用户:N/A(N/A)]  * Debugger is active! [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 22:04:11,889 [INFO] [N/A] [用户:N/A(N/A)]  * Debugger PIN: 131-026-009 [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 22:04:30,473 [INFO] [N/A] [用户:N/A(N/A)]  * Detected change in 'D:\\PyProject\\icms2\\app\\routes\\server_resource.py', reloading [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 22:04:30,815 [INFO] [N/A] [用户:N/A]  * Restarting with stat [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 22:04:33,614 [INFO] [N/A] [用户:N/A(N/A)] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:112]
2025-05-22 22:04:34,135 [INFO] [N/A] [用户:N/A(N/A)] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:41]
2025-05-22 22:04:34,140 [INFO] [N/A] [用户:N/A(N/A)] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:112]
2025-05-22 22:04:34,218 [INFO] [N/A] [用户:N/A(N/A)] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:41]
2025-05-22 22:04:34,457 [INFO] [N/A] [用户:N/A(N/A)] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:112]
2025-05-22 22:04:34,520 [INFO] [N/A] [用户:N/A(N/A)] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:41]
2025-05-22 22:04:34,721 [WARNING] [N/A] [用户:N/A(N/A)]  * Debugger is active! [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 22:04:34,790 [INFO] [N/A] [用户:N/A(N/A)]  * Debugger PIN: 131-026-009 [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 22:04:54,559 [INFO] [N/A] [用户:N/A(N/A)]  * Detected change in 'D:\\PyProject\\icms2\\app\\routes\\application.py', reloading [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 22:04:54,833 [INFO] [N/A] [用户:N/A]  * Restarting with stat [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 22:04:57,828 [INFO] [N/A] [用户:N/A(N/A)] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:112]
2025-05-22 22:04:58,337 [INFO] [N/A] [用户:N/A(N/A)] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:41]
2025-05-22 22:04:58,344 [INFO] [N/A] [用户:N/A(N/A)] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:112]
2025-05-22 22:04:58,420 [INFO] [N/A] [用户:N/A(N/A)] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:41]
2025-05-22 22:04:58,646 [INFO] [N/A] [用户:N/A(N/A)] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:112]
2025-05-22 22:04:58,711 [INFO] [N/A] [用户:N/A(N/A)] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:41]
2025-05-22 22:04:58,906 [WARNING] [N/A] [用户:N/A(N/A)]  * Debugger is active! [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 22:04:58,963 [INFO] [N/A] [用户:N/A(N/A)]  * Debugger PIN: 131-026-009 [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 22:05:20,942 [INFO] [N/A] [用户:N/A(N/A)]  * Detected change in 'D:\\PyProject\\icms2\\app\\routes\\application.py', reloading [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 22:05:21,218 [INFO] [N/A] [用户:N/A]  * Restarting with stat [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 22:05:24,714 [INFO] [N/A] [用户:N/A(N/A)] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:112]
2025-05-22 22:05:25,060 [INFO] [N/A] [用户:N/A(N/A)] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:41]
2025-05-22 22:05:25,067 [INFO] [N/A] [用户:N/A(N/A)] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:112]
2025-05-22 22:05:25,107 [INFO] [N/A] [用户:N/A(N/A)] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:41]
2025-05-22 22:05:25,292 [INFO] [N/A] [用户:N/A(N/A)] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:112]
2025-05-22 22:05:25,327 [INFO] [N/A] [用户:N/A(N/A)] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:41]
2025-05-22 22:05:25,473 [WARNING] [N/A] [用户:N/A(N/A)]  * Debugger is active! [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 22:05:25,502 [INFO] [N/A] [用户:N/A(N/A)]  * Debugger PIN: 131-026-009 [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 22:05:25,750 [INFO] [127.0.0.1] [用户:gerujun(11)] 请求开始: endpoint=log_download.get_top_folders, method=GET, url=http://localhost:5000/api/logs/folders, client_ip=127.0.0.1, username=gerujun, user_id=11 [in D:\PyProject\icms2\app\utils\decorators.py:50]
2025-05-22 22:05:25,751 [INFO] [127.0.0.1] [用户:gerujun(11)] 获取顶级文件夹列表: client_ip=127.0.0.1, user_id=11, prefix= [in D:\PyProject\icms2\app\routes\log_download.py:39]
2025-05-22 22:05:26,776 [INFO] [127.0.0.1] [用户:gerujun(11)] generate built-in connection pool success. maxsize=10,10 [in D:\PyProject\icms2\.venv\Lib\site-packages\qcloud_cos\cos_client.py:274]
2025-05-22 22:05:26,776 [INFO] [127.0.0.1] [用户:gerujun(11)] bound built-in connection pool when new client. maxsize=10,10 [in D:\PyProject\icms2\.venv\Lib\site-packages\qcloud_cos\cos_client.py:264]
2025-05-22 22:05:26,777 [INFO] [127.0.0.1] [用户:gerujun(11)] list objects, url=:https://cvmlog-private-prd-sh-v5-1251979869.cos.ap-shanghai-fsi.myqcloud.com/ ,headers=:{} [in D:\PyProject\icms2\.venv\Lib\site-packages\qcloud_cos\cos_client.py:1473]
2025-05-22 22:05:27,526 [INFO] [127.0.0.1] [用户:gerujun(11)] 获取顶级文件夹列表成功: client_ip=127.0.0.1, user_id=11, count=442 [in D:\PyProject\icms2\app\routes\log_download.py:135]
2025-05-22 22:05:27,527 [INFO] [127.0.0.1] [用户:gerujun(11)] 请求结束: endpoint=log_download.get_top_folders, method=GET, status=200, time=1.777s, client_ip=127.0.0.1, username=gerujun, user_id=11 [in D:\PyProject\icms2\app\utils\decorators.py:61]
2025-05-22 22:05:27,541 [INFO] [N/A] [用户:N/A(N/A)] 127.0.0.1 - - [22/May/2025 22:05:27] "GET /api/logs/folders HTTP/1.1" 200 - [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 22:05:27,562 [INFO] [127.0.0.1] [用户:gerujun(11)] 请求开始: endpoint=log_download.get_top_folders, method=GET, url=http://localhost:5000/api/logs/folders, client_ip=127.0.0.1, username=gerujun, user_id=11 [in D:\PyProject\icms2\app\utils\decorators.py:50]
2025-05-22 22:05:27,563 [INFO] [127.0.0.1] [用户:gerujun(11)] 获取顶级文件夹列表: client_ip=127.0.0.1, user_id=11, prefix= [in D:\PyProject\icms2\app\routes\log_download.py:39]
2025-05-22 22:05:27,569 [INFO] [127.0.0.1] [用户:gerujun(11)] bound built-in connection pool when new client. maxsize=10,10 [in D:\PyProject\icms2\.venv\Lib\site-packages\qcloud_cos\cos_client.py:264]
2025-05-22 22:05:27,570 [INFO] [127.0.0.1] [用户:gerujun(11)] list objects, url=:https://cvmlog-private-prd-sh-v5-1251979869.cos.ap-shanghai-fsi.myqcloud.com/ ,headers=:{} [in D:\PyProject\icms2\.venv\Lib\site-packages\qcloud_cos\cos_client.py:1473]
2025-05-22 22:05:28,068 [INFO] [127.0.0.1] [用户:gerujun(11)] 获取顶级文件夹列表成功: client_ip=127.0.0.1, user_id=11, count=442 [in D:\PyProject\icms2\app\routes\log_download.py:135]
2025-05-22 22:05:28,069 [INFO] [127.0.0.1] [用户:gerujun(11)] 请求结束: endpoint=log_download.get_top_folders, method=GET, status=200, time=0.507s, client_ip=127.0.0.1, username=gerujun, user_id=11 [in D:\PyProject\icms2\app\utils\decorators.py:61]
2025-05-22 22:05:28,084 [INFO] [N/A] [用户:N/A(N/A)] 127.0.0.1 - - [22/May/2025 22:05:28] "GET /api/logs/folders HTTP/1.1" 200 - [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 22:05:29,315 [INFO] [127.0.0.1] [用户:gerujun(11)] 请求开始: endpoint=application.get_applications, method=GET, url=http://localhost:5000/api/applications?page=1&per_page=30&app_en_name=&app_cn_name=&app_admin=, client_ip=127.0.0.1, username=gerujun, user_id=11 [in D:\PyProject\icms2\app\utils\decorators.py:50]
2025-05-22 22:05:29,343 [INFO] [127.0.0.1] [用户:gerujun(11)] 请求结束: endpoint=application.get_applications, method=GET, status=200, time=0.028s, client_ip=127.0.0.1, username=gerujun, user_id=11 [in D:\PyProject\icms2\app\utils\decorators.py:61]
2025-05-22 22:05:29,353 [INFO] [N/A] [用户:N/A(N/A)] 127.0.0.1 - - [22/May/2025 22:05:29] "GET /api/applications?page=1&per_page=30&app_en_name=&app_cn_name=&app_admin= HTTP/1.1" 200 - [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 22:05:29,565 [INFO] [127.0.0.1] [用户:gerujun(11)] 请求开始: endpoint=application.get_applications, method=GET, url=http://localhost:5000/api/applications?page=1&per_page=30&app_en_name=&app_cn_name=&app_admin=, client_ip=127.0.0.1, username=gerujun, user_id=11 [in D:\PyProject\icms2\app\utils\decorators.py:50]
2025-05-22 22:05:29,589 [INFO] [127.0.0.1] [用户:gerujun(11)] 请求结束: endpoint=application.get_applications, method=GET, status=200, time=0.024s, client_ip=127.0.0.1, username=gerujun, user_id=11 [in D:\PyProject\icms2\app\utils\decorators.py:61]
2025-05-22 22:05:29,601 [INFO] [N/A] [用户:N/A(N/A)] 127.0.0.1 - - [22/May/2025 22:05:29] "GET /api/applications?page=1&per_page=30&app_en_name=&app_cn_name=&app_admin= HTTP/1.1" 200 - [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 22:06:04,945 [INFO] [127.0.0.1] [用户:gerujun(11)] 请求开始: endpoint=server_resource.get_servers, method=GET, url=http://localhost:5000/api/servers?page=1&per_page=20&hostname=&ip=&remark=, client_ip=127.0.0.1, username=gerujun, user_id=11 [in D:\PyProject\icms2\app\utils\decorators.py:50]
2025-05-22 22:06:04,981 [INFO] [127.0.0.1] [用户:gerujun(11)] 请求结束: endpoint=server_resource.get_servers, method=GET, status=200, time=0.036s, client_ip=127.0.0.1, username=gerujun, user_id=11 [in D:\PyProject\icms2\app\utils\decorators.py:61]
2025-05-22 22:06:04,994 [INFO] [N/A] [用户:N/A(N/A)] 127.0.0.1 - - [22/May/2025 22:06:04] "GET /api/servers?page=1&per_page=20&hostname=&ip=&remark= HTTP/1.1" 200 - [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 22:06:05,318 [INFO] [127.0.0.1] [用户:gerujun(11)] 请求开始: endpoint=server_resource.get_servers, method=GET, url=http://localhost:5000/api/servers?page=1&per_page=20&hostname=&ip=&remark=, client_ip=127.0.0.1, username=gerujun, user_id=11 [in D:\PyProject\icms2\app\utils\decorators.py:50]
2025-05-22 22:06:05,350 [INFO] [127.0.0.1] [用户:gerujun(11)] 请求结束: endpoint=server_resource.get_servers, method=GET, status=200, time=0.032s, client_ip=127.0.0.1, username=gerujun, user_id=11 [in D:\PyProject\icms2\app\utils\decorators.py:61]
2025-05-22 22:06:05,364 [INFO] [N/A] [用户:N/A(N/A)] 127.0.0.1 - - [22/May/2025 22:06:05] "GET /api/servers?page=1&per_page=20&hostname=&ip=&remark= HTTP/1.1" 200 - [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 22:06:23,262 [INFO] [N/A] [用户:N/A(N/A)]  * Detected change in 'D:\\PyProject\\icms2\\app\\routes\\application_environment.py', reloading [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 22:06:23,640 [INFO] [N/A] [用户:N/A]  * Restarting with stat [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 22:06:26,925 [INFO] [N/A] [用户:N/A(N/A)] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:112]
2025-05-22 22:06:27,360 [INFO] [N/A] [用户:N/A(N/A)] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:41]
2025-05-22 22:06:27,366 [INFO] [N/A] [用户:N/A(N/A)] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:112]
2025-05-22 22:06:27,425 [INFO] [N/A] [用户:N/A(N/A)] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:41]
2025-05-22 22:06:27,638 [INFO] [N/A] [用户:N/A(N/A)] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:112]
2025-05-22 22:06:27,698 [INFO] [N/A] [用户:N/A(N/A)] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:41]
2025-05-22 22:06:27,895 [WARNING] [N/A] [用户:N/A(N/A)]  * Debugger is active! [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 22:06:27,966 [INFO] [N/A] [用户:N/A(N/A)]  * Debugger PIN: 131-026-009 [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 22:06:42,543 [INFO] [N/A] [用户:N/A(N/A)]  * Detected change in 'D:\\PyProject\\icms2\\app\\routes\\application_environment.py', reloading [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 22:06:42,885 [INFO] [N/A] [用户:N/A]  * Restarting with stat [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 22:06:45,873 [INFO] [N/A] [用户:N/A(N/A)] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:112]
2025-05-22 22:06:46,221 [INFO] [N/A] [用户:N/A(N/A)] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:41]
2025-05-22 22:06:46,227 [INFO] [N/A] [用户:N/A(N/A)] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:112]
2025-05-22 22:06:46,279 [INFO] [N/A] [用户:N/A(N/A)] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:41]
2025-05-22 22:06:46,575 [INFO] [N/A] [用户:N/A(N/A)] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:112]
2025-05-22 22:06:46,627 [INFO] [N/A] [用户:N/A(N/A)] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:41]
2025-05-22 22:06:46,914 [WARNING] [N/A] [用户:N/A(N/A)]  * Debugger is active! [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 22:06:46,967 [INFO] [N/A] [用户:N/A(N/A)]  * Debugger PIN: 131-026-009 [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 22:06:59,339 [INFO] [N/A] [用户:N/A(N/A)]  * Detected change in 'D:\\PyProject\\icms2\\app\\routes\\application_environment.py', reloading [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 22:06:59,548 [INFO] [N/A] [用户:N/A]  * Restarting with stat [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 22:07:01,692 [INFO] [N/A] [用户:N/A(N/A)] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:112]
2025-05-22 22:07:02,112 [INFO] [N/A] [用户:N/A(N/A)] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:41]
2025-05-22 22:07:02,118 [INFO] [N/A] [用户:N/A(N/A)] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:112]
2025-05-22 22:07:02,188 [INFO] [N/A] [用户:N/A(N/A)] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:41]
2025-05-22 22:07:02,415 [INFO] [N/A] [用户:N/A(N/A)] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:112]
2025-05-22 22:07:02,480 [INFO] [N/A] [用户:N/A(N/A)] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:41]
2025-05-22 22:07:02,665 [WARNING] [N/A] [用户:N/A(N/A)]  * Debugger is active! [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 22:07:02,708 [INFO] [N/A] [用户:N/A(N/A)]  * Debugger PIN: 131-026-009 [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 22:07:12,329 [INFO] [N/A] [用户:N/A(N/A)]  * Detected change in 'D:\\PyProject\\icms2\\app\\routes\\application_environment.py', reloading [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 22:07:12,664 [INFO] [N/A] [用户:N/A]  * Restarting with stat [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 22:07:15,376 [INFO] [N/A] [用户:N/A(N/A)] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:112]
2025-05-22 22:07:15,802 [INFO] [N/A] [用户:N/A(N/A)] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:41]
2025-05-22 22:07:15,808 [INFO] [N/A] [用户:N/A(N/A)] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:112]
2025-05-22 22:07:15,871 [INFO] [N/A] [用户:N/A(N/A)] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:41]
2025-05-22 22:07:16,058 [INFO] [N/A] [用户:N/A(N/A)] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:112]
2025-05-22 22:07:16,114 [INFO] [N/A] [用户:N/A(N/A)] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:41]
2025-05-22 22:07:16,305 [WARNING] [N/A] [用户:N/A(N/A)]  * Debugger is active! [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 22:07:16,368 [INFO] [N/A] [用户:N/A(N/A)]  * Debugger PIN: 131-026-009 [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 22:07:31,884 [INFO] [N/A] [用户:N/A(N/A)]  * Detected change in 'D:\\PyProject\\icms2\\app\\routes\\application_environment.py', reloading [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 22:07:32,349 [INFO] [N/A] [用户:N/A]  * Restarting with stat [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 22:07:35,354 [INFO] [N/A] [用户:N/A(N/A)] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:112]
2025-05-22 22:07:35,761 [INFO] [N/A] [用户:N/A(N/A)] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:41]
2025-05-22 22:07:35,768 [INFO] [N/A] [用户:N/A(N/A)] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:112]
2025-05-22 22:07:35,827 [INFO] [N/A] [用户:N/A(N/A)] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:41]
2025-05-22 22:07:36,369 [INFO] [N/A] [用户:N/A(N/A)] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:112]
2025-05-22 22:07:36,422 [INFO] [N/A] [用户:N/A(N/A)] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:41]
2025-05-22 22:07:36,635 [WARNING] [N/A] [用户:N/A(N/A)]  * Debugger is active! [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 22:07:36,693 [INFO] [N/A] [用户:N/A(N/A)]  * Debugger PIN: 131-026-009 [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 22:07:47,471 [INFO] [N/A] [用户:N/A(N/A)]  * Detected change in 'D:\\PyProject\\icms2\\app\\routes\\application_environment.py', reloading [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 22:07:47,744 [INFO] [N/A] [用户:N/A]  * Restarting with stat [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 22:07:50,577 [INFO] [N/A] [用户:N/A(N/A)] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:112]
2025-05-22 22:07:51,156 [INFO] [N/A] [用户:N/A(N/A)] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:41]
2025-05-22 22:07:51,166 [INFO] [N/A] [用户:N/A(N/A)] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:112]
2025-05-22 22:07:51,233 [INFO] [N/A] [用户:N/A(N/A)] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:41]
2025-05-22 22:07:51,471 [INFO] [N/A] [用户:N/A(N/A)] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:112]
2025-05-22 22:07:51,539 [INFO] [N/A] [用户:N/A(N/A)] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:41]
2025-05-22 22:07:51,717 [WARNING] [N/A] [用户:N/A(N/A)]  * Debugger is active! [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 22:07:51,765 [INFO] [N/A] [用户:N/A(N/A)]  * Debugger PIN: 131-026-009 [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 22:08:45,798 [INFO] [127.0.0.1] [用户:gerujun(11)] 请求开始: endpoint=application_environment.get_application_environments, method=GET, url=http://localhost:5000/api/application-environments?page=1&per_page=20&app_en_name=&app_cn_name=, client_ip=127.0.0.1, username=gerujun, user_id=11 [in D:\PyProject\icms2\app\utils\decorators.py:50]
2025-05-22 22:08:45,858 [INFO] [127.0.0.1] [用户:gerujun(11)] 请求结束: endpoint=application_environment.get_application_environments, method=GET, status=200, time=0.060s, client_ip=127.0.0.1, username=gerujun, user_id=11 [in D:\PyProject\icms2\app\utils\decorators.py:61]
2025-05-22 22:08:45,875 [INFO] [N/A] [用户:N/A(N/A)] 127.0.0.1 - - [22/May/2025 22:08:45] "GET /api/application-environments?page=1&per_page=20&app_en_name=&app_cn_name= HTTP/1.1" 200 - [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 22:08:45,972 [INFO] [127.0.0.1] [用户:gerujun(11)] 请求开始: endpoint=application_environment.get_application_environments, method=GET, url=http://localhost:5000/api/application-environments?page=1&per_page=20&app_en_name=&app_cn_name=, client_ip=127.0.0.1, username=gerujun, user_id=11 [in D:\PyProject\icms2\app\utils\decorators.py:50]
2025-05-22 22:08:46,019 [INFO] [127.0.0.1] [用户:gerujun(11)] 请求结束: endpoint=application_environment.get_application_environments, method=GET, status=200, time=0.047s, client_ip=127.0.0.1, username=gerujun, user_id=11 [in D:\PyProject\icms2\app\utils\decorators.py:61]
2025-05-22 22:08:46,030 [INFO] [N/A] [用户:N/A(N/A)] 127.0.0.1 - - [22/May/2025 22:08:46] "GET /api/application-environments?page=1&per_page=20&app_en_name=&app_cn_name= HTTP/1.1" 200 - [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 22:08:58,634 [INFO] [127.0.0.1] [用户:unknown(unknown)] 请求开始: endpoint=auth.validate_token, method=GET, url=http://localhost:5000/api/validate-token, client_ip=127.0.0.1, username=unknown, user_id=unknown [in D:\PyProject\icms2\app\utils\decorators.py:50]
2025-05-22 22:08:58,973 [INFO] [127.0.0.1] [用户:unknown(unknown)] 请求结束: endpoint=auth.validate_token, method=GET, status=200, time=0.339s, client_ip=127.0.0.1, username=unknown, user_id=unknown [in D:\PyProject\icms2\app\utils\decorators.py:61]
2025-05-22 22:08:58,990 [INFO] [N/A] [用户:N/A(N/A)] 127.0.0.1 - - [22/May/2025 22:08:58] "GET /api/validate-token HTTP/1.1" 200 - [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 22:08:59,033 [INFO] [127.0.0.1] [用户:unknown(unknown)] 请求开始: endpoint=auth.validate_token, method=GET, url=http://localhost:5000/api/validate-token, client_ip=127.0.0.1, username=unknown, user_id=unknown [in D:\PyProject\icms2\app\utils\decorators.py:50]
2025-05-22 22:08:59,193 [INFO] [127.0.0.1] [用户:unknown(unknown)] 请求结束: endpoint=auth.validate_token, method=GET, status=200, time=0.161s, client_ip=127.0.0.1, username=unknown, user_id=unknown [in D:\PyProject\icms2\app\utils\decorators.py:61]
2025-05-22 22:08:59,206 [INFO] [N/A] [用户:N/A(N/A)] 127.0.0.1 - - [22/May/2025 22:08:59] "GET /api/validate-token HTTP/1.1" 200 - [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 22:08:59,222 [INFO] [127.0.0.1] [用户:unknown(unknown)] 请求开始: endpoint=auth.validate_token, method=GET, url=http://localhost:5000/api/validate-token, client_ip=127.0.0.1, username=unknown, user_id=unknown [in D:\PyProject\icms2\app\utils\decorators.py:50]
2025-05-22 22:08:59,366 [INFO] [127.0.0.1] [用户:unknown(unknown)] 请求结束: endpoint=auth.validate_token, method=GET, status=200, time=0.144s, client_ip=127.0.0.1, username=unknown, user_id=unknown [in D:\PyProject\icms2\app\utils\decorators.py:61]
2025-05-22 22:08:59,379 [INFO] [N/A] [用户:N/A(N/A)] 127.0.0.1 - - [22/May/2025 22:08:59] "GET /api/validate-token HTTP/1.1" 200 - [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 22:08:59,394 [INFO] [127.0.0.1] [用户:unknown(unknown)] 请求开始: endpoint=auth.validate_token, method=GET, url=http://localhost:5000/api/validate-token, client_ip=127.0.0.1, username=unknown, user_id=unknown [in D:\PyProject\icms2\app\utils\decorators.py:50]
2025-05-22 22:08:59,535 [INFO] [127.0.0.1] [用户:unknown(unknown)] 请求结束: endpoint=auth.validate_token, method=GET, status=200, time=0.141s, client_ip=127.0.0.1, username=unknown, user_id=unknown [in D:\PyProject\icms2\app\utils\decorators.py:61]
2025-05-22 22:08:59,546 [INFO] [N/A] [用户:N/A(N/A)] 127.0.0.1 - - [22/May/2025 22:08:59] "GET /api/validate-token HTTP/1.1" 200 - [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 22:09:05,856 [INFO] [127.0.0.1] [用户:unknown(unknown)] 请求开始: endpoint=auth.login, method=POST, url=http://localhost:5000/api/login, client_ip=127.0.0.1, username=unknown, user_id=unknown [in D:\PyProject\icms2\app\utils\decorators.py:50]
2025-05-22 22:09:06,474 [INFO] [127.0.0.1] [用户:admin(1)] Login successful: user=admin, ip=127.0.0.1, type=Local [in D:\PyProject\icms2\app\routes\auth.py:188]
2025-05-22 22:09:06,475 [INFO] [127.0.0.1] [用户:admin(1)] 操作成功: 类型=用户登录, endpoint=auth.login, method=POST, client_ip=127.0.0.1, username=admin, user_id=1 [in D:\PyProject\icms2\app\routes\auth.py:191]
2025-05-22 22:09:06,475 [INFO] [127.0.0.1] [用户:admin(1)] 请求结束: endpoint=auth.login, method=POST, status=200, time=0.619s, client_ip=127.0.0.1, username=unknown, user_id=unknown [in D:\PyProject\icms2\app\utils\decorators.py:61]
2025-05-22 22:09:06,487 [INFO] [N/A] [用户:N/A(N/A)] 127.0.0.1 - - [22/May/2025 22:09:06] "POST /api/login HTTP/1.1" 200 - [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 22:09:21,943 [INFO] [127.0.0.1] [用户:admin(1)] 请求开始: endpoint=application_environment.get_application_environments, method=GET, url=http://localhost:5000/api/application-environments?page=1&per_page=20&app_en_name=&app_cn_name=, client_ip=127.0.0.1, username=admin, user_id=1 [in D:\PyProject\icms2\app\utils\decorators.py:50]
2025-05-22 22:09:21,989 [INFO] [127.0.0.1] [用户:admin(1)] 请求结束: endpoint=application_environment.get_application_environments, method=GET, status=200, time=0.047s, client_ip=127.0.0.1, username=admin, user_id=1 [in D:\PyProject\icms2\app\utils\decorators.py:61]
2025-05-22 22:09:22,014 [INFO] [N/A] [用户:N/A(N/A)] 127.0.0.1 - - [22/May/2025 22:09:22] "GET /api/application-environments?page=1&per_page=20&app_en_name=&app_cn_name= HTTP/1.1" 200 - [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 22:09:22,339 [INFO] [127.0.0.1] [用户:admin(1)] 请求开始: endpoint=application_environment.get_application_environments, method=GET, url=http://localhost:5000/api/application-environments?page=1&per_page=20&app_en_name=&app_cn_name=, client_ip=127.0.0.1, username=admin, user_id=1 [in D:\PyProject\icms2\app\utils\decorators.py:50]
2025-05-22 22:09:22,411 [INFO] [127.0.0.1] [用户:admin(1)] 请求结束: endpoint=application_environment.get_application_environments, method=GET, status=200, time=0.072s, client_ip=127.0.0.1, username=admin, user_id=1 [in D:\PyProject\icms2\app\utils\decorators.py:61]
2025-05-22 22:09:22,423 [INFO] [N/A] [用户:N/A(N/A)] 127.0.0.1 - - [22/May/2025 22:09:22] "GET /api/application-environments?page=1&per_page=20&app_en_name=&app_cn_name= HTTP/1.1" 200 - [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 22:12:44,271 [INFO] [127.0.0.1] [用户:admin(1)] 请求开始: endpoint=server_resource.get_servers, method=GET, url=http://localhost:5000/api/servers?page=1&per_page=20&hostname=&ip=&remark=, client_ip=127.0.0.1, username=admin, user_id=1 [in D:\PyProject\icms2\app\utils\decorators.py:50]
2025-05-22 22:12:44,309 [INFO] [127.0.0.1] [用户:admin(1)] 请求结束: endpoint=server_resource.get_servers, method=GET, status=200, time=0.038s, client_ip=127.0.0.1, username=admin, user_id=1 [in D:\PyProject\icms2\app\utils\decorators.py:61]
2025-05-22 22:12:44,320 [INFO] [N/A] [用户:N/A(N/A)] 127.0.0.1 - - [22/May/2025 22:12:44] "GET /api/servers?page=1&per_page=20&hostname=&ip=&remark= HTTP/1.1" 200 - [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 22:12:44,522 [INFO] [127.0.0.1] [用户:admin(1)] 请求开始: endpoint=server_resource.get_servers, method=GET, url=http://localhost:5000/api/servers?page=1&per_page=20&hostname=&ip=&remark=, client_ip=127.0.0.1, username=admin, user_id=1 [in D:\PyProject\icms2\app\utils\decorators.py:50]
2025-05-22 22:12:44,550 [INFO] [127.0.0.1] [用户:admin(1)] 请求结束: endpoint=server_resource.get_servers, method=GET, status=200, time=0.029s, client_ip=127.0.0.1, username=admin, user_id=1 [in D:\PyProject\icms2\app\utils\decorators.py:61]
2025-05-22 22:12:44,566 [INFO] [N/A] [用户:N/A(N/A)] 127.0.0.1 - - [22/May/2025 22:12:44] "GET /api/servers?page=1&per_page=20&hostname=&ip=&remark= HTTP/1.1" 200 - [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 22:12:56,140 [INFO] [N/A] [用户:N/A(N/A)]  * Detected change in 'D:\\PyProject\\icms2\\app\\__init__.py', reloading [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 22:12:56,666 [INFO] [N/A] [用户:N/A]  * Restarting with stat [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 22:12:59,640 [INFO] [N/A] [用户:N/A(N/A)] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:112]
2025-05-22 22:12:59,900 [INFO] [N/A] [用户:N/A(N/A)] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:41]
2025-05-22 22:12:59,904 [INFO] [N/A] [用户:N/A(N/A)] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:112]
2025-05-22 22:12:59,940 [INFO] [N/A] [用户:N/A(N/A)] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:41]
2025-05-22 22:13:00,104 [INFO] [N/A] [用户:N/A(N/A)] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:112]
2025-05-22 22:13:00,136 [INFO] [N/A] [用户:N/A(N/A)] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:41]
2025-05-22 22:13:00,281 [WARNING] [N/A] [用户:N/A(N/A)]  * Debugger is active! [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 22:13:00,312 [INFO] [N/A] [用户:N/A(N/A)]  * Debugger PIN: 131-026-009 [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 22:13:08,758 [INFO] [N/A] [用户:N/A(N/A)]  * Detected change in 'D:\\PyProject\\icms2\\app\\routes\\auth.py', reloading [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 22:13:09,022 [INFO] [N/A] [用户:N/A]  * Restarting with stat [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 22:13:12,331 [INFO] [N/A] [用户:N/A(N/A)] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:112]
2025-05-22 22:13:12,809 [INFO] [N/A] [用户:N/A(N/A)] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:41]
2025-05-22 22:13:12,818 [INFO] [N/A] [用户:N/A(N/A)] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:112]
2025-05-22 22:13:12,889 [INFO] [N/A] [用户:N/A(N/A)] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:41]
2025-05-22 22:13:13,125 [INFO] [N/A] [用户:N/A(N/A)] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:112]
2025-05-22 22:13:13,176 [INFO] [N/A] [用户:N/A(N/A)] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:41]
2025-05-22 22:13:13,372 [WARNING] [N/A] [用户:N/A(N/A)]  * Debugger is active! [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 22:13:13,437 [INFO] [N/A] [用户:N/A(N/A)]  * Debugger PIN: 131-026-009 [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 22:13:20,746 [INFO] [N/A] [用户:N/A(N/A)]  * Detected change in 'D:\\PyProject\\icms2\\app\\routes\\auth.py', reloading [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 22:13:21,005 [INFO] [N/A] [用户:N/A]  * Restarting with stat [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 22:13:23,348 [INFO] [N/A] [用户:N/A(N/A)] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:112]
2025-05-22 22:13:23,690 [INFO] [N/A] [用户:N/A(N/A)] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:41]
2025-05-22 22:13:23,695 [INFO] [N/A] [用户:N/A(N/A)] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:112]
2025-05-22 22:13:23,734 [INFO] [N/A] [用户:N/A(N/A)] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:41]
2025-05-22 22:13:24,032 [INFO] [N/A] [用户:N/A(N/A)] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:112]
2025-05-22 22:13:24,091 [INFO] [N/A] [用户:N/A(N/A)] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:41]
2025-05-22 22:13:24,298 [WARNING] [N/A] [用户:N/A(N/A)]  * Debugger is active! [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 22:13:24,358 [INFO] [N/A] [用户:N/A(N/A)]  * Debugger PIN: 131-026-009 [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 22:13:38,765 [INFO] [N/A] [用户:N/A(N/A)]  * Detected change in 'D:\\PyProject\\icms2\\app\\routes\\auth.py', reloading [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 22:13:39,471 [INFO] [N/A] [用户:N/A]  * Restarting with stat [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 22:13:42,313 [INFO] [N/A] [用户:N/A(N/A)] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:112]
2025-05-22 22:13:42,740 [INFO] [N/A] [用户:N/A(N/A)] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:41]
2025-05-22 22:13:42,746 [INFO] [N/A] [用户:N/A(N/A)] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:112]
2025-05-22 22:13:42,803 [INFO] [N/A] [用户:N/A(N/A)] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:41]
2025-05-22 22:13:42,992 [INFO] [N/A] [用户:N/A(N/A)] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:112]
2025-05-22 22:13:43,048 [INFO] [N/A] [用户:N/A(N/A)] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:41]
2025-05-22 22:13:43,204 [WARNING] [N/A] [用户:N/A(N/A)]  * Debugger is active! [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 22:13:43,242 [INFO] [N/A] [用户:N/A(N/A)]  * Debugger PIN: 131-026-009 [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 22:13:48,538 [INFO] [N/A] [用户:N/A(N/A)]  * Detected change in 'D:\\PyProject\\icms2\\app\\routes\\auth.py', reloading [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 22:13:48,832 [INFO] [N/A] [用户:N/A]  * Restarting with stat [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 22:13:51,279 [INFO] [N/A] [用户:N/A(N/A)] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:112]
2025-05-22 22:13:51,678 [INFO] [N/A] [用户:N/A(N/A)] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:41]
2025-05-22 22:13:51,683 [INFO] [N/A] [用户:N/A(N/A)] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:112]
2025-05-22 22:13:51,734 [INFO] [N/A] [用户:N/A(N/A)] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:41]
2025-05-22 22:13:51,941 [INFO] [N/A] [用户:N/A(N/A)] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:112]
2025-05-22 22:13:52,000 [INFO] [N/A] [用户:N/A(N/A)] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:41]
2025-05-22 22:13:52,172 [WARNING] [N/A] [用户:N/A(N/A)]  * Debugger is active! [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 22:13:52,218 [INFO] [N/A] [用户:N/A(N/A)]  * Debugger PIN: 131-026-009 [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 22:14:05,475 [INFO] [N/A] [用户:N/A(N/A)]  * Detected change in 'D:\\PyProject\\icms2\\app\\routes\\auth.py', reloading [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 22:14:05,753 [INFO] [N/A] [用户:N/A]  * Restarting with stat [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 22:14:09,084 [INFO] [N/A] [用户:N/A(N/A)] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:112]
2025-05-22 22:14:09,708 [INFO] [N/A] [用户:N/A(N/A)] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:41]
2025-05-22 22:14:09,744 [INFO] [N/A] [用户:N/A(N/A)] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:112]
2025-05-22 22:14:09,833 [INFO] [N/A] [用户:N/A(N/A)] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:41]
2025-05-22 22:14:10,083 [INFO] [N/A] [用户:N/A(N/A)] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:112]
2025-05-22 22:14:10,139 [INFO] [N/A] [用户:N/A(N/A)] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:41]
2025-05-22 22:14:10,376 [WARNING] [N/A] [用户:N/A(N/A)]  * Debugger is active! [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 22:14:10,440 [INFO] [N/A] [用户:N/A(N/A)]  * Debugger PIN: 131-026-009 [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 22:14:14,628 [INFO] [127.0.0.1] [用户:admin(1)] 请求开始: endpoint=load_balancer.get_load_balancers, method=GET, url=http://localhost:5000/api/load-balancers?page=1&per_page=20&name=&vip=, client_ip=127.0.0.1, username=admin, user_id=1 [in D:\PyProject\icms2\app\utils\decorators.py:50]
2025-05-22 22:14:14,630 [INFO] [127.0.0.1] [用户:admin(1)] 查询负载均衡列表: client_ip=127.0.0.1, name=, vip=, page=1, per_page=20 [in D:\PyProject\icms2\app\routes\load_balancer.py:34]
2025-05-22 22:14:14,658 [INFO] [127.0.0.1] [用户:admin(1)] 查询负载均衡列表成功: client_ip=127.0.0.1, total=422 [in D:\PyProject\icms2\app\routes\load_balancer.py:64]
2025-05-22 22:14:14,660 [INFO] [127.0.0.1] [用户:admin(1)] 请求结束: endpoint=load_balancer.get_load_balancers, method=GET, status=200, time=0.032s, client_ip=127.0.0.1, username=admin, user_id=1 [in D:\PyProject\icms2\app\utils\decorators.py:61]
2025-05-22 22:14:14,676 [INFO] [N/A] [用户:N/A(N/A)] 127.0.0.1 - - [22/May/2025 22:14:14] "GET /api/load-balancers?page=1&per_page=20&name=&vip= HTTP/1.1" 200 - [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 22:14:14,762 [INFO] [127.0.0.1] [用户:admin(1)] 请求开始: endpoint=load_balancer.get_load_balancers, method=GET, url=http://localhost:5000/api/load-balancers?page=1&per_page=20&name=&vip=, client_ip=127.0.0.1, username=admin, user_id=1 [in D:\PyProject\icms2\app\utils\decorators.py:50]
2025-05-22 22:14:14,764 [INFO] [127.0.0.1] [用户:admin(1)] 查询负载均衡列表: client_ip=127.0.0.1, name=, vip=, page=1, per_page=20 [in D:\PyProject\icms2\app\routes\load_balancer.py:34]
2025-05-22 22:14:14,781 [INFO] [127.0.0.1] [用户:admin(1)] 查询负载均衡列表成功: client_ip=127.0.0.1, total=422 [in D:\PyProject\icms2\app\routes\load_balancer.py:64]
2025-05-22 22:14:14,783 [INFO] [127.0.0.1] [用户:admin(1)] 请求结束: endpoint=load_balancer.get_load_balancers, method=GET, status=200, time=0.021s, client_ip=127.0.0.1, username=admin, user_id=1 [in D:\PyProject\icms2\app\utils\decorators.py:61]
2025-05-22 22:14:14,796 [INFO] [N/A] [用户:N/A(N/A)] 127.0.0.1 - - [22/May/2025 22:14:14] "GET /api/load-balancers?page=1&per_page=20&name=&vip= HTTP/1.1" 200 - [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 22:14:15,434 [INFO] [N/A] [用户:N/A(N/A)]  * Detected change in 'D:\\PyProject\\icms2\\app\\routes\\auth.py', reloading [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 22:14:15,826 [INFO] [N/A] [用户:N/A]  * Restarting with stat [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 22:14:18,654 [INFO] [N/A] [用户:N/A(N/A)] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:112]
2025-05-22 22:14:19,217 [INFO] [N/A] [用户:N/A(N/A)] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:41]
2025-05-22 22:14:19,313 [INFO] [N/A] [用户:N/A(N/A)] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:112]
2025-05-22 22:14:19,501 [INFO] [N/A] [用户:N/A(N/A)] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:41]
2025-05-22 22:14:19,746 [INFO] [N/A] [用户:N/A(N/A)] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:112]
2025-05-22 22:14:19,880 [INFO] [N/A] [用户:N/A(N/A)] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:41]
2025-05-22 22:14:20,151 [WARNING] [N/A] [用户:N/A(N/A)]  * Debugger is active! [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 22:14:20,227 [INFO] [N/A] [用户:N/A(N/A)]  * Debugger PIN: 131-026-009 [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 22:14:29,075 [INFO] [N/A] [用户:N/A(N/A)]  * Detected change in 'D:\\PyProject\\icms2\\app\\utils\\auth_middleware.py', reloading [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 22:14:29,361 [INFO] [N/A] [用户:N/A]  * Restarting with stat [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 22:14:32,254 [INFO] [N/A] [用户:N/A(N/A)] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:112]
2025-05-22 22:14:32,736 [INFO] [N/A] [用户:N/A(N/A)] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:41]
2025-05-22 22:14:32,740 [INFO] [N/A] [用户:N/A(N/A)] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:112]
2025-05-22 22:14:32,794 [INFO] [N/A] [用户:N/A(N/A)] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:41]
2025-05-22 22:14:33,029 [INFO] [N/A] [用户:N/A(N/A)] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:112]
2025-05-22 22:14:33,106 [INFO] [N/A] [用户:N/A(N/A)] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:41]
2025-05-22 22:14:33,289 [WARNING] [N/A] [用户:N/A(N/A)]  * Debugger is active! [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 22:14:33,346 [INFO] [N/A] [用户:N/A(N/A)]  * Debugger PIN: 131-026-009 [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 22:14:42,272 [INFO] [N/A] [用户:N/A(N/A)]  * Detected change in 'D:\\PyProject\\icms2\\config.py', reloading [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 22:14:42,604 [INFO] [N/A] [用户:N/A]  * Restarting with stat [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 22:14:46,104 [INFO] [N/A] [用户:N/A(N/A)] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:112]
2025-05-22 22:14:46,590 [INFO] [N/A] [用户:N/A(N/A)] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:41]
2025-05-22 22:14:46,597 [INFO] [N/A] [用户:N/A(N/A)] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:112]
2025-05-22 22:14:46,785 [INFO] [N/A] [用户:N/A(N/A)] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:41]
2025-05-22 22:14:47,100 [INFO] [N/A] [用户:N/A(N/A)] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:112]
2025-05-22 22:14:47,167 [INFO] [N/A] [用户:N/A(N/A)] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:41]
2025-05-22 22:14:47,363 [WARNING] [N/A] [用户:N/A(N/A)]  * Debugger is active! [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 22:14:47,416 [INFO] [N/A] [用户:N/A(N/A)]  * Debugger PIN: 131-026-009 [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 22:14:53,889 [INFO] [N/A] [用户:N/A(N/A)]  * Detected change in 'D:\\PyProject\\icms2\\app\\__init__.py', reloading [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 22:14:54,314 [INFO] [N/A] [用户:N/A]  * Restarting with stat [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 22:14:57,334 [INFO] [N/A] [用户:N/A(N/A)] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:112]
2025-05-22 22:14:57,930 [INFO] [N/A] [用户:N/A(N/A)] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:41]
2025-05-22 22:14:57,934 [INFO] [N/A] [用户:N/A(N/A)] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:112]
2025-05-22 22:14:58,044 [INFO] [N/A] [用户:N/A(N/A)] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:41]
2025-05-22 22:14:58,420 [INFO] [N/A] [用户:N/A(N/A)] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:112]
2025-05-22 22:14:58,488 [INFO] [N/A] [用户:N/A(N/A)] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:41]
2025-05-22 22:14:58,670 [WARNING] [N/A] [用户:N/A(N/A)]  * Debugger is active! [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 22:14:58,712 [INFO] [N/A] [用户:N/A(N/A)]  * Debugger PIN: 131-026-009 [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 22:15:30,844 [INFO] [127.0.0.1] [用户:unknown(unknown)] 请求开始: endpoint=auth.validate_token, method=GET, url=http://localhost:5000/api/validate-token, client_ip=127.0.0.1, username=unknown, user_id=unknown [in D:\PyProject\icms2\app\utils\decorators.py:50]
2025-05-22 22:15:31,496 [INFO] [127.0.0.1] [用户:unknown(unknown)] 请求结束: endpoint=auth.validate_token, method=GET, status=200, time=0.651s, client_ip=127.0.0.1, username=unknown, user_id=unknown [in D:\PyProject\icms2\app\utils\decorators.py:61]
2025-05-22 22:15:31,511 [INFO] [N/A] [用户:N/A(N/A)] 127.0.0.1 - - [22/May/2025 22:15:31] "GET /api/validate-token HTTP/1.1" 200 - [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 22:15:31,527 [INFO] [127.0.0.1] [用户:unknown(unknown)] 请求开始: endpoint=auth.validate_token, method=GET, url=http://localhost:5000/api/validate-token, client_ip=127.0.0.1, username=unknown, user_id=unknown [in D:\PyProject\icms2\app\utils\decorators.py:50]
2025-05-22 22:15:32,021 [INFO] [127.0.0.1] [用户:unknown(unknown)] 请求结束: endpoint=auth.validate_token, method=GET, status=200, time=0.494s, client_ip=127.0.0.1, username=unknown, user_id=unknown [in D:\PyProject\icms2\app\utils\decorators.py:61]
2025-05-22 22:15:32,032 [INFO] [N/A] [用户:N/A(N/A)] 127.0.0.1 - - [22/May/2025 22:15:32] "GET /api/validate-token HTTP/1.1" 200 - [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 22:15:32,045 [INFO] [127.0.0.1] [用户:unknown(unknown)] 请求开始: endpoint=auth.validate_token, method=GET, url=http://localhost:5000/api/validate-token, client_ip=127.0.0.1, username=unknown, user_id=unknown [in D:\PyProject\icms2\app\utils\decorators.py:50]
2025-05-22 22:15:32,504 [INFO] [127.0.0.1] [用户:unknown(unknown)] 请求结束: endpoint=auth.validate_token, method=GET, status=200, time=0.458s, client_ip=127.0.0.1, username=unknown, user_id=unknown [in D:\PyProject\icms2\app\utils\decorators.py:61]
2025-05-22 22:15:32,515 [INFO] [N/A] [用户:N/A(N/A)] 127.0.0.1 - - [22/May/2025 22:15:32] "GET /api/validate-token HTTP/1.1" 200 - [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 22:15:32,676 [INFO] [127.0.0.1] [用户:unknown(unknown)] 请求开始: endpoint=auth.validate_token, method=GET, url=http://localhost:5000/api/validate-token, client_ip=127.0.0.1, username=unknown, user_id=unknown [in D:\PyProject\icms2\app\utils\decorators.py:50]
2025-05-22 22:15:33,151 [INFO] [127.0.0.1] [用户:unknown(unknown)] 请求结束: endpoint=auth.validate_token, method=GET, status=200, time=0.474s, client_ip=127.0.0.1, username=unknown, user_id=unknown [in D:\PyProject\icms2\app\utils\decorators.py:61]
2025-05-22 22:15:33,160 [INFO] [N/A] [用户:N/A(N/A)] 127.0.0.1 - - [22/May/2025 22:15:33] "GET /api/validate-token HTTP/1.1" 200 - [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 22:15:39,789 [INFO] [127.0.0.1] [用户:unknown(unknown)] 请求开始: endpoint=auth.login, method=POST, url=http://localhost:5000/api/login, client_ip=127.0.0.1, username=unknown, user_id=unknown [in D:\PyProject\icms2\app\utils\decorators.py:50]
2025-05-22 22:15:39,811 [INFO] [127.0.0.1] [用户:unknown(unknown)] Trying LDAP config: id=5, server=*********** [in D:\PyProject\icms2\app\routes\auth.py:247]
2025-05-22 22:15:40,782 [INFO] [127.0.0.1] [用户:unknown(unknown)] LDAP search: config_id=5, base=OU=信息技术部,OU=北京职场,OU=内部全体员工,OU=和泰人寿员工,DC=ht,DC=com, filter=(|(cn=gerujun)(sAMAccountName=gerujun)) [in D:\PyProject\icms2\app\routes\auth.py:262]
2025-05-22 22:15:40,825 [INFO] [127.0.0.1] [用户:unknown(unknown)] Trying LDAP config: id=8, server=*********** [in D:\PyProject\icms2\app\routes\auth.py:247]
2025-05-22 22:15:41,737 [INFO] [127.0.0.1] [用户:unknown(unknown)] LDAP search: config_id=8, base=OU=信息技术部,OU=深圳职场,OU=内部全体员工,OU=和泰人寿员工,DC=ht,DC=com, filter=(|(cn=gerujun)(sAMAccountName=gerujun)) [in D:\PyProject\icms2\app\routes\auth.py:262]
2025-05-22 22:15:42,997 [INFO] [127.0.0.1] [用户:gerujun(11)] Login successful: user=gerujun, ip=127.0.0.1, type=LDAP, config_id=8 [in D:\PyProject\icms2\app\routes\auth.py:307]
2025-05-22 22:15:42,998 [INFO] [127.0.0.1] [用户:gerujun(11)] 操作成功: 类型=用户登录, endpoint=auth.login, method=POST, client_ip=127.0.0.1, username=gerujun, user_id=11 [in D:\PyProject\icms2\app\routes\auth.py:310]
2025-05-22 22:15:42,999 [INFO] [127.0.0.1] [用户:gerujun(11)] 请求结束: endpoint=auth.login, method=POST, status=200, time=3.209s, client_ip=127.0.0.1, username=unknown, user_id=unknown [in D:\PyProject\icms2\app\utils\decorators.py:61]
2025-05-22 22:15:43,011 [INFO] [N/A] [用户:N/A(N/A)] 127.0.0.1 - - [22/May/2025 22:15:43] "POST /api/login HTTP/1.1" 200 - [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 22:15:47,130 [INFO] [127.0.0.1] [用户:gerujun(11)] 请求开始: endpoint=application.get_applications, method=GET, url=http://localhost:5000/api/applications?page=1&per_page=30&app_en_name=&app_cn_name=&app_admin=, client_ip=127.0.0.1, username=gerujun, user_id=11 [in D:\PyProject\icms2\app\utils\decorators.py:50]
2025-05-22 22:15:47,159 [INFO] [127.0.0.1] [用户:gerujun(11)] 请求结束: endpoint=application.get_applications, method=GET, status=200, time=0.029s, client_ip=127.0.0.1, username=gerujun, user_id=11 [in D:\PyProject\icms2\app\utils\decorators.py:61]
2025-05-22 22:15:47,173 [INFO] [N/A] [用户:N/A(N/A)] 127.0.0.1 - - [22/May/2025 22:15:47] "GET /api/applications?page=1&per_page=30&app_en_name=&app_cn_name=&app_admin= HTTP/1.1" 200 - [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 22:15:47,518 [INFO] [127.0.0.1] [用户:gerujun(11)] 请求开始: endpoint=application.get_applications, method=GET, url=http://localhost:5000/api/applications?page=1&per_page=30&app_en_name=&app_cn_name=&app_admin=, client_ip=127.0.0.1, username=gerujun, user_id=11 [in D:\PyProject\icms2\app\utils\decorators.py:50]
2025-05-22 22:15:47,577 [INFO] [127.0.0.1] [用户:gerujun(11)] 请求结束: endpoint=application.get_applications, method=GET, status=200, time=0.059s, client_ip=127.0.0.1, username=gerujun, user_id=11 [in D:\PyProject\icms2\app\utils\decorators.py:61]
2025-05-22 22:15:47,671 [INFO] [N/A] [用户:N/A(N/A)] 127.0.0.1 - - [22/May/2025 22:15:47] "GET /api/applications?page=1&per_page=30&app_en_name=&app_cn_name=&app_admin= HTTP/1.1" 200 - [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 22:16:04,934 [INFO] [127.0.0.1] [用户:unknown(unknown)] 请求开始: endpoint=auth.validate_token, method=GET, url=http://localhost:5000/api/validate-token, client_ip=127.0.0.1, username=unknown, user_id=unknown [in D:\PyProject\icms2\app\utils\decorators.py:50]
2025-05-22 22:16:05,158 [INFO] [127.0.0.1] [用户:unknown(unknown)] 请求结束: endpoint=auth.validate_token, method=GET, status=200, time=0.225s, client_ip=127.0.0.1, username=unknown, user_id=unknown [in D:\PyProject\icms2\app\utils\decorators.py:61]
2025-05-22 22:16:05,171 [INFO] [N/A] [用户:N/A(N/A)] 127.0.0.1 - - [22/May/2025 22:16:05] "GET /api/validate-token HTTP/1.1" 200 - [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 22:16:05,204 [INFO] [127.0.0.1] [用户:unknown(unknown)] 请求开始: endpoint=auth.validate_token, method=GET, url=http://localhost:5000/api/validate-token, client_ip=127.0.0.1, username=unknown, user_id=unknown [in D:\PyProject\icms2\app\utils\decorators.py:50]
2025-05-22 22:16:05,384 [INFO] [127.0.0.1] [用户:unknown(unknown)] 请求结束: endpoint=auth.validate_token, method=GET, status=200, time=0.180s, client_ip=127.0.0.1, username=unknown, user_id=unknown [in D:\PyProject\icms2\app\utils\decorators.py:61]
2025-05-22 22:16:05,398 [INFO] [N/A] [用户:N/A(N/A)] 127.0.0.1 - - [22/May/2025 22:16:05] "GET /api/validate-token HTTP/1.1" 200 - [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 22:16:05,418 [INFO] [127.0.0.1] [用户:unknown(unknown)] 请求开始: endpoint=auth.validate_token, method=GET, url=http://localhost:5000/api/validate-token, client_ip=127.0.0.1, username=unknown, user_id=unknown [in D:\PyProject\icms2\app\utils\decorators.py:50]
2025-05-22 22:16:05,572 [INFO] [127.0.0.1] [用户:unknown(unknown)] 请求结束: endpoint=auth.validate_token, method=GET, status=200, time=0.153s, client_ip=127.0.0.1, username=unknown, user_id=unknown [in D:\PyProject\icms2\app\utils\decorators.py:61]
2025-05-22 22:16:05,588 [INFO] [N/A] [用户:N/A(N/A)] 127.0.0.1 - - [22/May/2025 22:16:05] "GET /api/validate-token HTTP/1.1" 200 - [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 22:16:05,606 [INFO] [127.0.0.1] [用户:unknown(unknown)] 请求开始: endpoint=auth.validate_token, method=GET, url=http://localhost:5000/api/validate-token, client_ip=127.0.0.1, username=unknown, user_id=unknown [in D:\PyProject\icms2\app\utils\decorators.py:50]
2025-05-22 22:16:05,749 [INFO] [127.0.0.1] [用户:unknown(unknown)] 请求结束: endpoint=auth.validate_token, method=GET, status=200, time=0.143s, client_ip=127.0.0.1, username=unknown, user_id=unknown [in D:\PyProject\icms2\app\utils\decorators.py:61]
2025-05-22 22:16:05,761 [INFO] [N/A] [用户:N/A(N/A)] 127.0.0.1 - - [22/May/2025 22:16:05] "GET /api/validate-token HTTP/1.1" 200 - [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 22:25:42,802 [INFO] [N/A] [用户:N/A(N/A)]  * Detected change in 'D:\\PyProject\\icms2\\app\\routes\\auth.py', reloading [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 22:25:43,086 [INFO] [N/A] [用户:N/A]  * Restarting with stat [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 22:25:45,167 [INFO] [N/A] [用户:N/A(N/A)] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:112]
2025-05-22 22:25:45,609 [INFO] [N/A] [用户:N/A(N/A)] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:41]
2025-05-22 22:25:45,718 [INFO] [N/A] [用户:N/A(N/A)] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:112]
2025-05-22 22:25:45,889 [INFO] [N/A] [用户:N/A(N/A)] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:41]
2025-05-22 22:25:46,198 [INFO] [N/A] [用户:N/A(N/A)] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:112]
2025-05-22 22:25:46,255 [INFO] [N/A] [用户:N/A(N/A)] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:41]
2025-05-22 22:25:46,442 [WARNING] [N/A] [用户:N/A(N/A)]  * Debugger is active! [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 22:25:46,496 [INFO] [N/A] [用户:N/A(N/A)]  * Debugger PIN: 131-026-009 [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 22:26:00,048 [INFO] [N/A] [用户:N/A(N/A)]  * Detected change in 'D:\\PyProject\\icms2\\app\\routes\\auth.py', reloading [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 22:26:00,404 [INFO] [N/A] [用户:N/A]  * Restarting with stat [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 22:26:03,604 [INFO] [N/A] [用户:N/A(N/A)] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:112]
2025-05-22 22:26:04,278 [INFO] [N/A] [用户:N/A(N/A)] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:41]
2025-05-22 22:26:04,286 [INFO] [N/A] [用户:N/A(N/A)] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:112]
2025-05-22 22:26:04,373 [INFO] [N/A] [用户:N/A(N/A)] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:41]
2025-05-22 22:26:04,690 [INFO] [N/A] [用户:N/A(N/A)] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:112]
2025-05-22 22:26:04,769 [INFO] [N/A] [用户:N/A(N/A)] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:41]
2025-05-22 22:26:04,985 [WARNING] [N/A] [用户:N/A(N/A)]  * Debugger is active! [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 22:26:05,040 [INFO] [N/A] [用户:N/A(N/A)]  * Debugger PIN: 131-026-009 [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 22:26:19,287 [INFO] [N/A] [用户:N/A(N/A)]  * Detected change in 'D:\\PyProject\\icms2\\app\\utils\\auth_middleware.py', reloading [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 22:26:19,568 [INFO] [N/A] [用户:N/A]  * Restarting with stat [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 22:26:22,206 [INFO] [N/A] [用户:N/A(N/A)] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:112]
2025-05-22 22:26:22,764 [INFO] [N/A] [用户:N/A(N/A)] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:41]
2025-05-22 22:26:22,769 [INFO] [N/A] [用户:N/A(N/A)] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:112]
2025-05-22 22:26:22,928 [INFO] [N/A] [用户:N/A(N/A)] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:41]
2025-05-22 22:26:23,138 [INFO] [N/A] [用户:N/A(N/A)] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:112]
2025-05-22 22:26:23,187 [INFO] [N/A] [用户:N/A(N/A)] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:41]
2025-05-22 22:26:23,336 [WARNING] [N/A] [用户:N/A(N/A)]  * Debugger is active! [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 22:26:23,373 [INFO] [N/A] [用户:N/A(N/A)]  * Debugger PIN: 131-026-009 [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 22:28:23,282 [INFO] [N/A] [用户:N/A(N/A)]  * Detected change in 'D:\\PyProject\\icms2\\app\\routes\\auth.py', reloading [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 22:28:23,584 [INFO] [N/A] [用户:N/A]  * Restarting with stat [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 22:28:26,640 [INFO] [N/A] [用户:N/A(N/A)] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:112]
2025-05-22 22:28:27,038 [INFO] [N/A] [用户:N/A(N/A)] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:41]
2025-05-22 22:28:27,046 [INFO] [N/A] [用户:N/A(N/A)] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:112]
2025-05-22 22:28:27,129 [INFO] [N/A] [用户:N/A(N/A)] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:41]
2025-05-22 22:28:27,644 [INFO] [N/A] [用户:N/A(N/A)] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:112]
2025-05-22 22:28:27,761 [INFO] [N/A] [用户:N/A(N/A)] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:41]
2025-05-22 22:28:27,955 [WARNING] [N/A] [用户:N/A(N/A)]  * Debugger is active! [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 22:28:28,020 [INFO] [N/A] [用户:N/A(N/A)]  * Debugger PIN: 131-026-009 [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 22:28:36,718 [INFO] [N/A] [用户:N/A(N/A)]  * Detected change in 'D:\\PyProject\\icms2\\app\\routes\\auth.py', reloading [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 22:28:36,952 [INFO] [N/A] [用户:N/A]  * Restarting with stat [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 22:28:39,369 [INFO] [N/A] [用户:N/A(N/A)] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:112]
2025-05-22 22:28:39,828 [INFO] [N/A] [用户:N/A(N/A)] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:41]
2025-05-22 22:28:39,834 [INFO] [N/A] [用户:N/A(N/A)] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:112]
2025-05-22 22:28:39,902 [INFO] [N/A] [用户:N/A(N/A)] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:41]
2025-05-22 22:28:40,109 [INFO] [N/A] [用户:N/A(N/A)] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:112]
2025-05-22 22:28:40,164 [INFO] [N/A] [用户:N/A(N/A)] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:41]
2025-05-22 22:28:40,340 [WARNING] [N/A] [用户:N/A(N/A)]  * Debugger is active! [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 22:28:40,393 [INFO] [N/A] [用户:N/A(N/A)]  * Debugger PIN: 131-026-009 [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 22:28:53,653 [INFO] [N/A] [用户:N/A(N/A)]  * Detected change in 'D:\\PyProject\\icms2\\app\\routes\\auth.py', reloading [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 22:28:53,953 [INFO] [N/A] [用户:N/A]  * Restarting with stat [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 22:28:57,535 [INFO] [N/A] [用户:N/A(N/A)] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:112]
2025-05-22 22:28:58,050 [INFO] [N/A] [用户:N/A(N/A)] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:41]
2025-05-22 22:28:58,056 [INFO] [N/A] [用户:N/A(N/A)] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:112]
2025-05-22 22:28:58,127 [INFO] [N/A] [用户:N/A(N/A)] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:41]
2025-05-22 22:28:58,358 [INFO] [N/A] [用户:N/A(N/A)] 应用启动成功，环境: development, 日志级别: INFO [in D:\PyProject\icms2\app\utils\logger.py:112]
2025-05-22 22:28:58,422 [INFO] [N/A] [用户:N/A(N/A)] ICMS2应用已启动，环境：development [in D:\PyProject\icms2\app\__init__.py:41]
2025-05-22 22:28:58,608 [WARNING] [N/A] [用户:N/A(N/A)]  * Debugger is active! [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 22:28:58,657 [INFO] [N/A] [用户:N/A(N/A)]  * Debugger PIN: 131-026-009 [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 22:31:16,470 [INFO] [127.0.0.1] [用户:unknown(unknown)] 请求开始: endpoint=auth.login, method=POST, url=http://localhost:5000/api/login, client_ip=127.0.0.1, username=unknown, user_id=unknown [in D:\PyProject\icms2\app\utils\decorators.py:50]
2025-05-22 22:31:16,561 [INFO] [127.0.0.1] [用户:unknown(unknown)] Trying LDAP config: id=5, server=*********** [in D:\PyProject\icms2\app\routes\auth.py:267]
2025-05-22 22:31:17,612 [INFO] [127.0.0.1] [用户:unknown(unknown)] LDAP search: config_id=5, base=OU=信息技术部,OU=北京职场,OU=内部全体员工,OU=和泰人寿员工,DC=ht,DC=com, filter=(|(cn=gerujun)(sAMAccountName=gerujun)) [in D:\PyProject\icms2\app\routes\auth.py:282]
2025-05-22 22:31:17,655 [INFO] [127.0.0.1] [用户:unknown(unknown)] Trying LDAP config: id=8, server=*********** [in D:\PyProject\icms2\app\routes\auth.py:267]
2025-05-22 22:31:18,649 [INFO] [127.0.0.1] [用户:unknown(unknown)] LDAP search: config_id=8, base=OU=信息技术部,OU=深圳职场,OU=内部全体员工,OU=和泰人寿员工,DC=ht,DC=com, filter=(|(cn=gerujun)(sAMAccountName=gerujun)) [in D:\PyProject\icms2\app\routes\auth.py:282]
2025-05-22 22:31:19,845 [INFO] [127.0.0.1] [用户:gerujun(11)] Login successful: user=gerujun, ip=127.0.0.1, type=LDAP, config_id=8 [in D:\PyProject\icms2\app\routes\auth.py:327]
2025-05-22 22:31:19,845 [INFO] [127.0.0.1] [用户:gerujun(11)] 操作成功: 类型=用户登录, endpoint=auth.login, method=POST, client_ip=127.0.0.1, username=gerujun, user_id=11 [in D:\PyProject\icms2\app\routes\auth.py:330]
2025-05-22 22:31:19,846 [INFO] [127.0.0.1] [用户:gerujun(11)] 请求结束: endpoint=auth.login, method=POST, status=200, time=3.376s, client_ip=127.0.0.1, username=unknown, user_id=unknown [in D:\PyProject\icms2\app\utils\decorators.py:61]
2025-05-22 22:31:19,858 [INFO] [N/A] [用户:N/A(N/A)] 127.0.0.1 - - [22/May/2025 22:31:19] "POST /api/login HTTP/1.1" 200 - [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 22:31:36,212 [INFO] [127.0.0.1] [用户:gerujun(11)] 请求开始: endpoint=application.get_applications, method=GET, url=http://localhost:5000/api/applications?page=1&per_page=30&app_en_name=&app_cn_name=&app_admin=, client_ip=127.0.0.1, username=gerujun, user_id=11 [in D:\PyProject\icms2\app\utils\decorators.py:50]
2025-05-22 22:31:36,243 [INFO] [127.0.0.1] [用户:gerujun(11)] 请求结束: endpoint=application.get_applications, method=GET, status=200, time=0.031s, client_ip=127.0.0.1, username=gerujun, user_id=11 [in D:\PyProject\icms2\app\utils\decorators.py:61]
2025-05-22 22:31:36,256 [INFO] [N/A] [用户:N/A(N/A)] 127.0.0.1 - - [22/May/2025 22:31:36] "GET /api/applications?page=1&per_page=30&app_en_name=&app_cn_name=&app_admin= HTTP/1.1" 200 - [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 22:31:36,593 [INFO] [127.0.0.1] [用户:gerujun(11)] 请求开始: endpoint=application.get_applications, method=GET, url=http://localhost:5000/api/applications?page=1&per_page=30&app_en_name=&app_cn_name=&app_admin=, client_ip=127.0.0.1, username=gerujun, user_id=11 [in D:\PyProject\icms2\app\utils\decorators.py:50]
2025-05-22 22:31:36,620 [INFO] [127.0.0.1] [用户:gerujun(11)] 请求结束: endpoint=application.get_applications, method=GET, status=200, time=0.027s, client_ip=127.0.0.1, username=gerujun, user_id=11 [in D:\PyProject\icms2\app\utils\decorators.py:61]
2025-05-22 22:31:36,631 [INFO] [N/A] [用户:N/A(N/A)] 127.0.0.1 - - [22/May/2025 22:31:36] "GET /api/applications?page=1&per_page=30&app_en_name=&app_cn_name=&app_admin= HTTP/1.1" 200 - [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 22:31:39,040 [INFO] [127.0.0.1] [用户:gerujun(11)] 请求开始: endpoint=application_environment.get_application_environments, method=GET, url=http://localhost:5000/api/application-environments?page=1&per_page=20&app_en_name=&app_cn_name=, client_ip=127.0.0.1, username=gerujun, user_id=11 [in D:\PyProject\icms2\app\utils\decorators.py:50]
2025-05-22 22:31:39,098 [INFO] [127.0.0.1] [用户:gerujun(11)] 请求结束: endpoint=application_environment.get_application_environments, method=GET, status=200, time=0.058s, client_ip=127.0.0.1, username=gerujun, user_id=11 [in D:\PyProject\icms2\app\utils\decorators.py:61]
2025-05-22 22:31:39,111 [INFO] [N/A] [用户:N/A(N/A)] 127.0.0.1 - - [22/May/2025 22:31:39] "GET /api/application-environments?page=1&per_page=20&app_en_name=&app_cn_name= HTTP/1.1" 200 - [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 22:31:39,425 [INFO] [127.0.0.1] [用户:gerujun(11)] 请求开始: endpoint=application_environment.get_application_environments, method=GET, url=http://localhost:5000/api/application-environments?page=1&per_page=20&app_en_name=&app_cn_name=, client_ip=127.0.0.1, username=gerujun, user_id=11 [in D:\PyProject\icms2\app\utils\decorators.py:50]
2025-05-22 22:31:39,471 [INFO] [127.0.0.1] [用户:gerujun(11)] 请求结束: endpoint=application_environment.get_application_environments, method=GET, status=200, time=0.045s, client_ip=127.0.0.1, username=gerujun, user_id=11 [in D:\PyProject\icms2\app\utils\decorators.py:61]
2025-05-22 22:31:39,486 [INFO] [N/A] [用户:N/A(N/A)] 127.0.0.1 - - [22/May/2025 22:31:39] "GET /api/application-environments?page=1&per_page=20&app_en_name=&app_cn_name= HTTP/1.1" 200 - [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 22:31:57,632 [INFO] [127.0.0.1] [用户:gerujun(11)] 请求开始: endpoint=auth.validate_token, method=GET, url=http://localhost:5000/api/validate-token, client_ip=127.0.0.1, username=gerujun, user_id=11 [in D:\PyProject\icms2\app\utils\decorators.py:50]
2025-05-22 22:31:57,799 [INFO] [127.0.0.1] [用户:gerujun(11)] 请求结束: endpoint=auth.validate_token, method=GET, status=200, time=0.166s, client_ip=127.0.0.1, username=gerujun, user_id=11 [in D:\PyProject\icms2\app\utils\decorators.py:61]
2025-05-22 22:31:57,811 [INFO] [N/A] [用户:N/A(N/A)] 127.0.0.1 - - [22/May/2025 22:31:57] "GET /api/validate-token HTTP/1.1" 200 - [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 22:31:57,830 [INFO] [127.0.0.1] [用户:gerujun(11)] 请求开始: endpoint=auth.validate_token, method=GET, url=http://localhost:5000/api/validate-token, client_ip=127.0.0.1, username=gerujun, user_id=11 [in D:\PyProject\icms2\app\utils\decorators.py:50]
2025-05-22 22:31:57,973 [INFO] [127.0.0.1] [用户:gerujun(11)] 请求结束: endpoint=auth.validate_token, method=GET, status=200, time=0.143s, client_ip=127.0.0.1, username=gerujun, user_id=11 [in D:\PyProject\icms2\app\utils\decorators.py:61]
2025-05-22 22:31:57,986 [INFO] [N/A] [用户:N/A(N/A)] 127.0.0.1 - - [22/May/2025 22:31:57] "GET /api/validate-token HTTP/1.1" 200 - [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 22:31:58,040 [INFO] [127.0.0.1] [用户:gerujun(11)] 请求开始: endpoint=auth.validate_token, method=GET, url=http://localhost:5000/api/validate-token, client_ip=127.0.0.1, username=gerujun, user_id=11 [in D:\PyProject\icms2\app\utils\decorators.py:50]
2025-05-22 22:31:58,176 [INFO] [127.0.0.1] [用户:gerujun(11)] 请求结束: endpoint=auth.validate_token, method=GET, status=200, time=0.136s, client_ip=127.0.0.1, username=gerujun, user_id=11 [in D:\PyProject\icms2\app\utils\decorators.py:61]
2025-05-22 22:31:58,195 [INFO] [N/A] [用户:N/A(N/A)] 127.0.0.1 - - [22/May/2025 22:31:58] "GET /api/validate-token HTTP/1.1" 200 - [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 22:31:58,221 [INFO] [127.0.0.1] [用户:gerujun(11)] 请求开始: endpoint=auth.validate_token, method=GET, url=http://localhost:5000/api/validate-token, client_ip=127.0.0.1, username=gerujun, user_id=11 [in D:\PyProject\icms2\app\utils\decorators.py:50]
2025-05-22 22:31:58,356 [INFO] [127.0.0.1] [用户:gerujun(11)] 请求结束: endpoint=auth.validate_token, method=GET, status=200, time=0.135s, client_ip=127.0.0.1, username=gerujun, user_id=11 [in D:\PyProject\icms2\app\utils\decorators.py:61]
2025-05-22 22:31:58,367 [INFO] [N/A] [用户:N/A(N/A)] 127.0.0.1 - - [22/May/2025 22:31:58] "GET /api/validate-token HTTP/1.1" 200 - [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 22:51:57,900 [INFO] [127.0.0.1] [用户:gerujun(11)] 请求开始: endpoint=auth.validate_token, method=GET, url=http://localhost:5000/api/validate-token, client_ip=127.0.0.1, username=gerujun, user_id=11 [in D:\PyProject\icms2\app\utils\decorators.py:50]
2025-05-22 22:51:58,038 [INFO] [127.0.0.1] [用户:gerujun(11)] 请求结束: endpoint=auth.validate_token, method=GET, status=200, time=0.138s, client_ip=127.0.0.1, username=gerujun, user_id=11 [in D:\PyProject\icms2\app\utils\decorators.py:61]
2025-05-22 22:51:58,049 [INFO] [N/A] [用户:N/A(N/A)] 127.0.0.1 - - [22/May/2025 22:51:58] "GET /api/validate-token HTTP/1.1" 200 - [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 22:51:58,145 [INFO] [127.0.0.1] [用户:gerujun(11)] 请求开始: endpoint=auth.validate_token, method=GET, url=http://localhost:5000/api/validate-token, client_ip=127.0.0.1, username=gerujun, user_id=11 [in D:\PyProject\icms2\app\utils\decorators.py:50]
2025-05-22 22:51:58,256 [INFO] [127.0.0.1] [用户:gerujun(11)] 请求结束: endpoint=auth.validate_token, method=GET, status=200, time=0.111s, client_ip=127.0.0.1, username=gerujun, user_id=11 [in D:\PyProject\icms2\app\utils\decorators.py:61]
2025-05-22 22:51:58,266 [INFO] [N/A] [用户:N/A(N/A)] 127.0.0.1 - - [22/May/2025 22:51:58] "GET /api/validate-token HTTP/1.1" 200 - [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 23:11:57,879 [INFO] [127.0.0.1] [用户:gerujun(11)] 请求开始: endpoint=auth.validate_token, method=GET, url=http://localhost:5000/api/validate-token, client_ip=127.0.0.1, username=gerujun, user_id=11 [in D:\PyProject\icms2\app\utils\decorators.py:50]
2025-05-22 23:11:58,009 [INFO] [127.0.0.1] [用户:gerujun(11)] 请求结束: endpoint=auth.validate_token, method=GET, status=200, time=0.130s, client_ip=127.0.0.1, username=gerujun, user_id=11 [in D:\PyProject\icms2\app\utils\decorators.py:61]
2025-05-22 23:11:58,022 [INFO] [N/A] [用户:N/A(N/A)] 127.0.0.1 - - [22/May/2025 23:11:58] "GET /api/validate-token HTTP/1.1" 200 - [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 23:11:58,132 [INFO] [127.0.0.1] [用户:gerujun(11)] 请求开始: endpoint=auth.validate_token, method=GET, url=http://localhost:5000/api/validate-token, client_ip=127.0.0.1, username=gerujun, user_id=11 [in D:\PyProject\icms2\app\utils\decorators.py:50]
2025-05-22 23:11:58,246 [INFO] [127.0.0.1] [用户:gerujun(11)] 请求结束: endpoint=auth.validate_token, method=GET, status=200, time=0.114s, client_ip=127.0.0.1, username=gerujun, user_id=11 [in D:\PyProject\icms2\app\utils\decorators.py:61]
2025-05-22 23:11:58,257 [INFO] [N/A] [用户:N/A(N/A)] 127.0.0.1 - - [22/May/2025 23:11:58] "GET /api/validate-token HTTP/1.1" 200 - [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 23:31:57,867 [INFO] [127.0.0.1] [用户:gerujun(11)] 请求开始: endpoint=auth.validate_token, method=GET, url=http://localhost:5000/api/validate-token, client_ip=127.0.0.1, username=gerujun, user_id=11 [in D:\PyProject\icms2\app\utils\decorators.py:50]
2025-05-22 23:31:57,988 [INFO] [127.0.0.1] [用户:gerujun(11)] 请求结束: endpoint=auth.validate_token, method=GET, status=200, time=0.121s, client_ip=127.0.0.1, username=gerujun, user_id=11 [in D:\PyProject\icms2\app\utils\decorators.py:61]
2025-05-22 23:31:57,997 [INFO] [N/A] [用户:N/A(N/A)] 127.0.0.1 - - [22/May/2025 23:31:57] "GET /api/validate-token HTTP/1.1" 200 - [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 23:31:58,120 [INFO] [127.0.0.1] [用户:gerujun(11)] 请求开始: endpoint=auth.validate_token, method=GET, url=http://localhost:5000/api/validate-token, client_ip=127.0.0.1, username=gerujun, user_id=11 [in D:\PyProject\icms2\app\utils\decorators.py:50]
2025-05-22 23:31:58,228 [INFO] [127.0.0.1] [用户:gerujun(11)] 请求结束: endpoint=auth.validate_token, method=GET, status=200, time=0.107s, client_ip=127.0.0.1, username=gerujun, user_id=11 [in D:\PyProject\icms2\app\utils\decorators.py:61]
2025-05-22 23:31:58,239 [INFO] [N/A] [用户:N/A(N/A)] 127.0.0.1 - - [22/May/2025 23:31:58] "GET /api/validate-token HTTP/1.1" 200 - [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 23:51:57,856 [INFO] [127.0.0.1] [用户:gerujun(11)] 请求开始: endpoint=auth.validate_token, method=GET, url=http://localhost:5000/api/validate-token, client_ip=127.0.0.1, username=gerujun, user_id=11 [in D:\PyProject\icms2\app\utils\decorators.py:50]
2025-05-22 23:51:57,986 [INFO] [127.0.0.1] [用户:gerujun(11)] 请求结束: endpoint=auth.validate_token, method=GET, status=200, time=0.130s, client_ip=127.0.0.1, username=gerujun, user_id=11 [in D:\PyProject\icms2\app\utils\decorators.py:61]
2025-05-22 23:51:58,000 [INFO] [N/A] [用户:N/A(N/A)] 127.0.0.1 - - [22/May/2025 23:51:58] "GET /api/validate-token HTTP/1.1" 200 - [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
2025-05-22 23:51:58,106 [INFO] [127.0.0.1] [用户:gerujun(11)] 请求开始: endpoint=auth.validate_token, method=GET, url=http://localhost:5000/api/validate-token, client_ip=127.0.0.1, username=gerujun, user_id=11 [in D:\PyProject\icms2\app\utils\decorators.py:50]
2025-05-22 23:51:58,216 [INFO] [127.0.0.1] [用户:gerujun(11)] 请求结束: endpoint=auth.validate_token, method=GET, status=200, time=0.110s, client_ip=127.0.0.1, username=gerujun, user_id=11 [in D:\PyProject\icms2\app\utils\decorators.py:61]
2025-05-22 23:51:58,230 [INFO] [N/A] [用户:N/A(N/A)] 127.0.0.1 - - [22/May/2025 23:51:58] "GET /api/validate-token HTTP/1.1" 200 - [in D:\PyProject\icms2\.venv\Lib\site-packages\werkzeug\_internal.py:97]
