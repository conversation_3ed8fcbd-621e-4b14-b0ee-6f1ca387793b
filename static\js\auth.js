/**
 * 认证相关的前端脚本
 */

// 在页面加载时验证令牌
document.addEventListener('DOMContentLoaded', function() {
    // 检查是否在登录页面，如果是则不需要验证令牌
    if (window.location.pathname.includes('/login')) {
        return;
    }
    
    // 验证令牌
    validateToken()
        .then(response => {
            if (response.valid) {
                console.log('Token is valid');
                
                // 如果返回了新令牌，更新本地存储
                if (response.new_token) {
                    console.log('Received new token');
                    localStorage.setItem('token', response.new_token);
                }
                
                // 更新用户信息
                updateUserInfo(response.user);
                
                // 更新权限信息
                updatePermissions(response.permissions);
            } else {
                console.log('Token is invalid');
                // 如果令牌无效，重定向到登录页面
                redirectToLogin();
            }
        })
        .catch(error => {
            console.error('Error validating token:', error);
            // 如果发生错误，重定向到登录页面
            redirectToLogin();
        });
});

/**
 * 验证令牌
 * @returns {Promise} 验证结果
 */
function validateToken() {
    return new Promise((resolve, reject) => {
        // 从 localStorage 获取令牌
        const token = localStorage.getItem('token');
        
        // 设置请求头
        const headers = {};
        if (token) {
            headers['Authorization'] = `Bearer ${token}`;
        }
        
        // 发送请求
        fetch('/api/validate-token', {
            method: 'GET',
            headers: headers,
            credentials: 'include' // 包含 Cookie
        })
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            resolve(data);
        })
        .catch(error => {
            reject(error);
        });
    });
}

/**
 * 更新用户信息
 * @param {Object} user 用户信息
 */
function updateUserInfo(user) {
    // 更新页面上的用户信息
    const userNameElements = document.querySelectorAll('.user-name');
    userNameElements.forEach(element => {
        element.textContent = user.display_name || user.username;
    });
    
    // 存储用户信息到 localStorage
    localStorage.setItem('user', JSON.stringify(user));
}

/**
 * 更新权限信息
 * @param {Object} permissions 权限信息
 */
function updatePermissions(permissions) {
    // 存储权限信息到 localStorage
    localStorage.setItem('permissions', JSON.stringify(permissions));
    
    // 根据权限更新菜单显示
    updateMenuVisibility(permissions);
}

/**
 * 根据权限更新菜单显示
 * @param {Object} permissions 权限信息
 */
function updateMenuVisibility(permissions) {
    // 获取所有菜单项
    const menuItems = document.querySelectorAll('.menu-item');
    
    // 如果没有权限信息，隐藏所有菜单
    if (!permissions || !permissions.modules) {
        menuItems.forEach(item => {
            item.style.display = 'none';
        });
        return;
    }
    
    // 获取用户有权限的模块和菜单
    const modules = permissions.modules || [];
    const allowedMenus = [];
    
    // 收集所有允许的菜单
    modules.forEach(module => {
        if (module.menus && Array.isArray(module.menus)) {
            module.menus.forEach(menu => {
                allowedMenus.push(menu.name);
            });
        }
    });
    
    // 更新菜单显示
    menuItems.forEach(item => {
        const menuName = item.getAttribute('data-menu');
        if (menuName && allowedMenus.includes(menuName)) {
            item.style.display = '';
        } else {
            item.style.display = 'none';
        }
    });
}

/**
 * 重定向到登录页面
 */
function redirectToLogin() {
    // 清除本地存储的令牌和用户信息
    localStorage.removeItem('token');
    localStorage.removeItem('user');
    localStorage.removeItem('permissions');
    
    // 重定向到登录页面
    window.location.href = '/login';
}

/**
 * 登出
 */
function logout() {
    // 清除本地存储的令牌和用户信息
    localStorage.removeItem('token');
    localStorage.removeItem('user');
    localStorage.removeItem('permissions');
    
    // 重定向到登录页面
    window.location.href = '/login';
}

// 导出函数
window.auth = {
    validateToken,
    updateUserInfo,
    updatePermissions,
    redirectToLogin,
    logout
};
