# Docker部署指南

本文档提供了使用Docker部署ICMS2应用的详细说明。

## 前提条件

在开始部署之前，请确保您的服务器上已安装以下软件：

- Docker (20.10.x或更高版本)
- Docker Compose (2.x或更高版本)
- Git (可选，用于克隆代码仓库)

## 部署步骤

### 1. 准备代码

将ICMS2代码复制到服务器上，或使用Git克隆仓库：

```bash
git clone [仓库URL]
cd icms2
```

### 2. 配置环境变量

复制示例环境变量文件并根据需要修改：

```bash
cp .env.example .env
```

编辑`.env`文件，设置生产环境所需的参数：

```
# 数据库配置
MYSQL_ROOT_PASSWORD=安全的根密码
MYSQL_DATABASE=icms2
MYSQL_USER=icmsopr
MYSQL_PASSWORD=安全的数据库密码

# 应用配置
SECRET_KEY=生成的安全密钥
FLASK_CONFIG=production
LOG_LEVEL=INFO
```

> **注意**：请确保在生产环境中使用强密码和安全的密钥。

### 3. 构建和启动容器

使用Docker Compose构建和启动应用：

```bash
# 构建镜像
docker-compose build

# 启动服务
docker-compose up -d
```

首次启动时，系统将：
1. 创建MySQL数据库容器
2. 初始化数据库结构
3. 启动ICMS2应用

### 4. 验证部署

检查容器是否正常运行：

```bash
docker-compose ps
```

查看应用日志：

```bash
docker-compose logs -f app
```

应用将在服务器的5000端口上运行，可以通过`http://服务器IP:5000`访问。

## 生产环境配置

### 数据库配置

在生产环境中，数据库配置通过环境变量设置：

1. 在`.env`文件中设置数据库连接参数
2. Docker Compose会将这些参数传递给应用和数据库容器

如果需要使用外部数据库而不是容器化的MySQL：

1. 修改`docker-compose.yml`文件，移除`db`服务
2. 更新`app`服务的`DATABASE_URL`环境变量，指向外部数据库

```yaml
app:
  environment:
    - DATABASE_URL=mysql+pymysql://用户名:密码@外部数据库主机/数据库名
```

### 日志配置

日志配置通过环境变量和卷挂载实现：

1. 日志文件存储在容器的`/app/logs`目录
2. 该目录通过卷挂载到主机的`./logs`目录
3. 可以通过`LOG_LEVEL`环境变量调整日志级别

### 安全配置

在生产环境中，请确保：

1. 使用强密码和安全的密钥
2. 限制数据库访问权限
3. 使用HTTPS保护API通信
4. 定期备份数据库

## 数据库初始化

系统启动时会自动初始化数据库表结构和基础数据。如果需要手动初始化：

```bash
# 进入应用容器
docker-compose exec app bash

# 初始化数据库表
python -c 'from app import create_app, db; from config import config; app = create_app(config["production"]); app.app_context().push(); db.create_all()'

# 初始化模块和菜单数据
python -c 'from scripts.init_modules_menus import init_modules_menus; init_modules_menus()'
```

## 常见问题

### 数据库连接失败

如果应用无法连接到数据库：

1. 检查`.env`文件中的数据库配置
2. 确保数据库容器正在运行：`docker-compose ps`
3. 检查数据库日志：`docker-compose logs db`

### 应用启动失败

如果应用容器无法启动：

1. 检查应用日志：`docker-compose logs app`
2. 确保数据库已初始化
3. 验证环境变量配置

## 更新应用

要更新应用到新版本：

```bash
# 拉取最新代码
git pull

# 重新构建并启动容器
docker-compose down
docker-compose build
docker-compose up -d
```

## 备份数据

定期备份数据库是很重要的：

```bash
# 备份数据库
docker-compose exec db sh -c 'mysqldump -u root -p"$MYSQL_ROOT_PASSWORD" icms2 > /tmp/backup.sql'

# 将备份文件复制到主机
docker cp $(docker-compose ps -q db):/tmp/backup.sql ./backup_$(date +%Y%m%d).sql
```

## 监控

建议设置监控以确保应用正常运行：

1. 使用Docker的健康检查
2. 设置容器资源限制
3. 监控日志文件
4. 设置告警机制
