# 日志使用示例

本文档提供了ICMS2系统日志功能的使用示例，帮助开发人员更好地使用日志系统。

## 目录

- [路由函数中使用日志](#路由函数中使用日志)
- [使用日志装饰器](#使用日志装饰器)
- [记录异常信息](#记录异常信息)
- [记录业务操作](#记录业务操作)
- [常见日志模式](#常见日志模式)

## 路由函数中使用日志

在路由函数中，可以直接使用`current_app.logger`记录日志：

```python
from flask import current_app, request

@app.route('/example')
def example():
    # 记录信息日志
    client_ip = request.remote_addr
    current_app.logger.info(f"访问示例接口: client_ip={client_ip}")
    
    try:
        # 业务逻辑
        result = process_data()
        
        # 记录成功日志
        current_app.logger.info(f"处理数据成功: client_ip={client_ip}, result_count={len(result)}")
        
        return jsonify(result)
    except Exception as e:
        # 记录错误日志
        current_app.logger.error(f"处理数据失败: client_ip={client_ip}, error={str(e)}")
        return jsonify({'error': str(e)}), 500
```

## 使用日志装饰器

系统提供了两个日志装饰器，可以简化日志记录：

### 路由日志装饰器

`@log_route`装饰器会自动记录请求的开始、结束和异常信息：

```python
from app.utils.decorators import log_route

@app.route('/users')
@log_route
def get_users():
    # 业务逻辑
    users = User.query.all()
    return jsonify([user.to_dict() for user in users])
```

装饰器会自动记录以下日志：

```
2023-07-01 12:34:56 [INFO] [*************] [用户:123] 请求开始: endpoint=get_users, method=GET, url=http://example.com/users, client_ip=*************, user_id=123
2023-07-01 12:34:57 [INFO] [*************] [用户:123] 请求结束: endpoint=get_users, method=GET, status=200, time=0.123s, client_ip=*************, user_id=123
```

如果发生异常，会自动记录错误日志：

```
2023-07-01 12:34:56 [ERROR] [*************] [用户:123] 请求异常: endpoint=get_users, method=GET, time=0.056s, client_ip=*************, user_id=123, error=数据库连接失败
Traceback (most recent call last):
  File "app/utils/decorators.py", line 25, in wrapper
    response = func(*args, **kwargs)
  ...
```

### 操作日志装饰器

`@log_operation`装饰器用于记录业务操作，需要指定操作类型：

```python
from app.utils.decorators import log_route, log_operation

@app.route('/users', methods=['POST'])
@log_route
@log_operation('创建用户')
def create_user():
    # 业务逻辑
    user = User(username=request.json.get('username'))
    db.session.add(user)
    db.session.commit()
    return jsonify(user.to_dict())
```

装饰器会自动记录以下日志：

```
2023-07-01 12:34:56 [INFO] [*************] [用户:123] 操作开始: 类型=创建用户, endpoint=create_user, method=POST, client_ip=*************, user_id=123, 数据={'username': 'test_user'}
2023-07-01 12:34:57 [INFO] [*************] [用户:123] 操作成功: 类型=创建用户, endpoint=create_user, method=POST, client_ip=*************, user_id=123
```

如果发生异常，会自动记录错误日志：

```
2023-07-01 12:34:56 [ERROR] [*************] [用户:123] 操作失败: 类型=创建用户, endpoint=create_user, method=POST, client_ip=*************, user_id=123, error=用户名已存在
Traceback (most recent call last):
  File "app/utils/decorators.py", line 78, in wrapper
    response = func(*args, **kwargs)
  ...
```

## 记录异常信息

在异常处理中，应该记录详细的错误信息：

```python
try:
    # 可能抛出异常的代码
    result = some_function()
except Exception as e:
    # 获取请求信息
    client_ip = request.remote_addr
    user_id = g.user.id if hasattr(g, 'user') else 'unknown'
    
    # 记录详细的错误信息
    current_app.logger.error(
        f"函数调用失败: function=some_function, client_ip={client_ip}, "
        f"user_id={user_id}, error={str(e)}", 
        exc_info=True  # 这会记录完整的堆栈跟踪
    )
    
    # 返回友好的错误信息
    return jsonify({'error': '操作失败，请稍后重试'}), 500
```

## 记录业务操作

对于重要的业务操作，应该记录详细的操作信息：

```python
@app.route('/servers/<int:server_id>', methods=['DELETE'])
@log_route
def delete_server(server_id):
    try:
        # 获取服务器信息
        server = ServerResource.query.get_or_404(server_id)
        
        # 记录操作开始
        client_ip = request.remote_addr
        user_id = g.user.id if hasattr(g, 'user') else 'unknown'
        current_app.logger.info(
            f"开始删除服务器: server_id={server_id}, server_name={server.server_name}, "
            f"client_ip={client_ip}, user_id={user_id}"
        )
        
        # 执行删除操作
        db.session.delete(server)
        
        # 记录变更历史
        history = ResourceChangeHistory(
            user_id=user_id,
            resource_type='server',
            resource_id=server_id,
            operation_type=OperationType.DELETE.value,
            before_data=json.dumps(server.to_dict())
        )
        db.session.add(history)
        db.session.commit()
        
        # 记录操作成功
        current_app.logger.info(
            f"删除服务器成功: server_id={server_id}, server_name={server.server_name}, "
            f"client_ip={client_ip}, user_id={user_id}"
        )
        
        return jsonify({'message': '删除成功'})
    except Exception as e:
        db.session.rollback()
        
        # 记录操作失败
        client_ip = request.remote_addr
        user_id = g.user.id if hasattr(g, 'user') else 'unknown'
        current_app.logger.error(
            f"删除服务器失败: server_id={server_id}, client_ip={client_ip}, "
            f"user_id={user_id}, error={str(e)}", 
            exc_info=True
        )
        
        return jsonify({'error': f'删除服务器失败: {str(e)}'}), 500
```

## 常见日志模式

### 1. 请求-响应模式

```python
# 记录请求
current_app.logger.info(f"请求参数: {request.json}")

# 处理请求
result = process_request(request.json)

# 记录响应
current_app.logger.info(f"响应结果: {result}")
```

### 2. 开始-结束模式

```python
# 记录开始
current_app.logger.info("开始同步数据")

# 处理逻辑
sync_data()

# 记录结束
current_app.logger.info("数据同步完成")
```

### 3. 统计信息模式

```python
# 初始化统计信息
stats = {'total': 0, 'success': 0, 'failed': 0}

# 处理数据
for item in items:
    stats['total'] += 1
    try:
        process_item(item)
        stats['success'] += 1
    except Exception as e:
        stats['failed'] += 1
        current_app.logger.error(f"处理项目失败: item_id={item.id}, error={str(e)}")

# 记录统计信息
current_app.logger.info(f"处理完成: total={stats['total']}, success={stats['success']}, failed={stats['failed']}")
```

### 4. 性能监控模式

```python
import time

# 记录开始时间
start_time = time.time()

# 处理逻辑
result = process_data()

# 计算执行时间并记录
execution_time = time.time() - start_time
current_app.logger.info(f"处理数据完成: time={execution_time:.3f}s, result_count={len(result)}")
```
