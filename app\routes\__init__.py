from .auth import auth
from .role import role
from .module_menu import module_menu
from .server_resource import server_resource
from .load_balancer import load_balancer
from .log_download import log_download
from .application import application
from .application_environment import application_environment
from .database_resource import database_resource
from .cvm_management import cvm_management
# 导入其他路由...

def init_routes(app):
    app.register_blueprint(auth, url_prefix='/api')
    app.register_blueprint(role, url_prefix='/api')
    app.register_blueprint(module_menu, url_prefix='/api')
    app.register_blueprint(server_resource, url_prefix='/api')
    app.register_blueprint(load_balancer, url_prefix='/api')
    app.register_blueprint(log_download, url_prefix='/api')
    app.register_blueprint(application, url_prefix='/api')
    app.register_blueprint(application_environment, url_prefix='/api')
    app.register_blueprint(database_resource, url_prefix='/api')
    app.register_blueprint(cvm_management, url_prefix='/api/cvm')
    # 注册其他路由...
