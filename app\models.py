from . import db, beijing_tz
from datetime import datetime
from enum import Enum
import json

def get_beijing_time():
    """获取北京时间"""
    return datetime.now(beijing_tz).replace(tzinfo=None)

# 定义用户类型枚举
class UserType(Enum):
    LDAP = 'ldap'
    LOCAL = 'local'

# 定义操作类型枚举
class OperationType(Enum):
    CREATE = 1
    UPDATE = 2
    DELETE = 3

class User(db.Model):
    __tablename__ = 'user'
    id = db.Column(db.BigInteger, primary_key=True, autoincrement=True)
    username = db.Column(db.String(64), unique=True, nullable=False)
    display_name = db.Column(db.String(128), nullable=False)
    email = db.Column(db.String(128), nullable=False)
    is_active = db.Column(db.Boolean, default=True)
    create_time = db.Column(db.DateTime, default=get_beijing_time)

    # 修改关系定义，使用 primaryjoin 明确指定连接条件，包括用户类型
    roles = db.relationship('Role',
                           secondary='user_role',
                           primaryjoin="and_(User.id==UserRole.user_id, UserRole.user_type=='ldap')",
                           secondaryjoin="UserRole.role_id==Role.id",
                           backref=db.backref('ldap_users', lazy='dynamic'))

class Role(db.Model):
    __tablename__ = 'role'
    id = db.Column(db.BigInteger, primary_key=True, autoincrement=True)
    role_name = db.Column(db.String(64), unique=True, nullable=False)
    description = db.Column(db.String(255))

    role_modules = db.relationship('RoleModule', backref='role', cascade='all, delete-orphan')

class Module(db.Model):
    __tablename__ = 'module'
    id = db.Column(db.BigInteger, primary_key=True, autoincrement=True)
    module_name = db.Column(db.String(64), unique=True, nullable=False)
    display_name = db.Column(db.String(64), nullable=False)
    description = db.Column(db.String(255))

class Menu(db.Model):
    __tablename__ = 'menu'
    id = db.Column(db.BigInteger, primary_key=True, autoincrement=True)
    menu_name = db.Column(db.String(100))
    menu_display_name = db.Column(db.String(100))
    module_id = db.Column(db.BigInteger, db.ForeignKey('module.id'))

class UserRole(db.Model):
    __tablename__ = 'user_role'
    user_id = db.Column(db.BigInteger, primary_key=True)
    user_type = db.Column(db.Enum('ldap', 'local', name='user_type_enum'), primary_key=True, nullable=False)
    role_id = db.Column(db.BigInteger, db.ForeignKey('role.id'), primary_key=True)

class RoleModule(db.Model):
    __tablename__ = 'role_module'
    role_id = db.Column(db.BigInteger, db.ForeignKey('role.id'), primary_key=True)
    module_id = db.Column(db.String(256), nullable=False)  # 存储逗号分隔的模块ID
    menu_id = db.Column(db.String(256), nullable=False)    # 存储逗号分隔的菜单ID

    # 辅助方法，将模块ID列表转换为逗号分隔的字符串
    @staticmethod
    def list_to_str(id_list):
        if not id_list:
            return ""
        return ",".join(str(id) for id in id_list)

    # 辅助方法，将逗号分隔的字符串转换为ID列表
    @staticmethod
    def str_to_list(id_str):
        if not id_str:
            return []
        return [int(id) for id in id_str.split(",") if id]

class Ldap(db.Model):
    __tablename__ = 'ldap'
    id = db.Column(db.BigInteger, primary_key=True, autoincrement=True)
    server = db.Column(db.String(255), nullable=False)
    port = db.Column(db.Integer, nullable=False)
    bind_dn = db.Column(db.String(255), nullable=False)
    password = db.Column(db.String(255), nullable=False)
    ou_path = db.Column(db.String(255))
    is_active = db.Column(db.Boolean, default=True)
    create_time = db.Column(db.DateTime, default=get_beijing_time)
    update_time = db.Column(db.DateTime, default=get_beijing_time, onupdate=get_beijing_time)

class LocalUser(db.Model):
    __tablename__ = 'local_user'
    id = db.Column(db.BigInteger, primary_key=True, autoincrement=True)
    username = db.Column(db.String(64), unique=True, nullable=False)
    password_hash = db.Column(db.String(256), nullable=False)
    display_name = db.Column(db.String(128), nullable=False)
    email = db.Column(db.String(128), nullable=False)
    is_active = db.Column(db.Boolean, default=True)
    create_time = db.Column(db.DateTime, default=get_beijing_time)
    update_time = db.Column(db.DateTime, default=get_beijing_time, onupdate=get_beijing_time)

    # 添加与角色的关系，明确指定用户类型为 'local'
    roles = db.relationship('Role',
                           secondary='user_role',
                           primaryjoin="and_(LocalUser.id==UserRole.user_id, UserRole.user_type=='local')",
                           secondaryjoin="UserRole.role_id==Role.id",
                           backref=db.backref('local_users', lazy='dynamic'),
                           viewonly=True)  # viewonly=True 避免级联更新

class CloudApiKey(db.Model):
    """云API密钥表"""
    __tablename__ = 'cloud_api_key'
    # 根据错误信息，实际表中可能没有id字段，或者字段名不同
    # 修改为与实际数据库表结构匹配的主键字段
    id = db.Column(db.BigInteger, primary_key=True, autoincrement=True)  # 如果表中没有此字段，请删除或修改
    secret_id = db.Column(db.String(100))
    secret_key = db.Column(db.String(100))
    cloudname = db.Column(db.String(100), primary_key=True)  # 如果cloudname是主键，请添加primary_key=True

class TencentRegion(db.Model):
    """腾讯云区域表"""
    __tablename__ = 'tencent_region'
    id = db.Column(db.BigInteger, primary_key=True, autoincrement=True)
    region_en = db.Column(db.String(100))  # 区域英文标识
    region_name = db.Column(db.String(100))  # 区域名称

class ServerResource(db.Model):
    """服务器资源表"""
    __tablename__ = 'server_resource'
    id = db.Column(db.BigInteger, primary_key=True, autoincrement=True)
    instance_id = db.Column(db.String(128), unique=True)  # 实例ID
    hostname = db.Column(db.String(128), nullable=False)  # 主机名
    ipv4 = db.Column(db.String(15), nullable=False)  # IPv4地址
    public_ip = db.Column(db.String(15))  # 公网IP
    os = db.Column(db.String(64), nullable=False)  # 操作系统
    cpu = db.Column(db.String(128), nullable=False)  # CPU信息
    memory = db.Column(db.String(64), nullable=False)  # 内存信息
    region = db.Column(db.String(64))  # 地域
    availability_zone = db.Column(db.String(64))  # 可用区
    vpc_id = db.Column(db.String(128))  # VPC ID
    subnet_id = db.Column(db.String(128))  # 子网ID
    create_time = db.Column(db.DateTime, default=get_beijing_time)  # 记录创建时间
    original_source = db.Column(db.String(255), nullable=False)  # 来源（手动录入manual/腾讯云tencent/华为云huawei）
    remark = db.Column(db.Text)  # 备注
    vm_creat_time = db.Column(db.DateTime, nullable=False)  # 云主机创建时间
    physical_host_id = db.Column(db.String(100))  # CDH宿主机ID
    server_status = db.Column(db.Enum('online', 'offline', name='server_status_enum'), default='online', comment='服务器状态')
    offline_time = db.Column(db.DateTime, comment='下线时间')

    def __repr__(self):
        return f'<ServerResource {self.hostname}>'

    def to_dict(self):
        """将对象转换为字典"""
        return {
            'id': self.id,
            'instance_id': self.instance_id,
            'hostname': self.hostname,
            'ipv4': self.ipv4,
            'public_ip': self.public_ip,
            'os': self.os,
            'cpu': self.cpu,
            'memory': self.memory,
            'region': self.region,
            'availability_zone': self.availability_zone,
            'vpc_id': self.vpc_id,
            'subnet_id': self.subnet_id,
            'create_time': self.create_time.strftime('%Y-%m-%d %H:%M:%S') if self.create_time else None,
            'original_source': self.original_source,
            'remark': self.remark,
            'vm_creat_time': self.vm_creat_time.strftime('%Y-%m-%d %H:%M:%S') if self.vm_creat_time else None,
            'physical_host_id': self.physical_host_id,
            'server_status': self.server_status,
            'offline_time': self.offline_time.strftime('%Y-%m-%d %H:%M:%S') if self.offline_time else None
        }

class ResourceChangeHistory(db.Model):
    """资源变更历史表"""
    __tablename__ = 'resource_change_history'
    id = db.Column(db.BigInteger, primary_key=True, autoincrement=True)
    change_time = db.Column(db.DateTime, default=get_beijing_time)  # 变更时间
    user_id = db.Column(db.BigInteger, db.ForeignKey('user.id'), nullable=False)  # 操作用户
    resource_type = db.Column(db.String(64), nullable=False)  # 资源类型
    resource_id = db.Column(db.Integer, nullable=False)  # 资源ID
    operation_type = db.Column(db.BigInteger, nullable=False)  # 操作类型
    before_data = db.Column(db.JSON)  # 变更前数据
    after_data = db.Column(db.JSON)  # 变更后数据

    # 关联用户
    user = db.relationship('User', backref=db.backref('resource_changes', lazy='dynamic'))

class RecycledHost(db.Model):
    """回收的服务器记录表"""
    __tablename__ = 'recycled_host'
    id = db.Column(db.BigInteger, primary_key=True, autoincrement=True)
    instance_id = db.Column(db.String(100), comment='实例ID')
    hostname = db.Column(db.String(100), comment='主机名')
    ipv4 = db.Column(db.String(100), comment='ipv4地址')
    recycled_time = db.Column(db.DateTime, default=get_beijing_time, comment='回收时间')

    def __repr__(self):
        return f'<RecycledHost {self.hostname}>'

class Application(db.Model):
    """应用系统表"""
    __tablename__ = 'application'
    id = db.Column(db.BigInteger, primary_key=True, autoincrement=True, comment='id')
    app_en_name = db.Column(db.String(64), unique=True, nullable=False, comment='应用英文名')
    app_cn_name = db.Column(db.String(128), nullable=False, comment='应用中文名')
    app_admin = db.Column(db.String(128), nullable=False, comment='应用管理员')
    app_status = db.Column(db.Enum('online', 'offline', name='app_status_enum'), default='online', comment='系统状态')
    create_time = db.Column(db.DateTime, default=get_beijing_time, comment='create_time')
    update_time = db.Column(db.DateTime, default=get_beijing_time, onupdate=get_beijing_time, comment='更新时间')
    offline_time = db.Column(db.DateTime, comment='下线时间')
    remark = db.Column(db.Text, comment='备注')

    def __repr__(self):
        return f'<Application {self.app_en_name}>'

    def to_dict(self):
        """将对象转换为字典"""
        return {
            'id': self.id,
            'app_en_name': self.app_en_name,
            'app_cn_name': self.app_cn_name,
            'app_admin': self.app_admin,
            'app_status': self.app_status,
            'create_time': self.create_time.strftime('%Y-%m-%d %H:%M:%S') if self.create_time else None,
            'update_time': self.update_time.strftime('%Y-%m-%d %H:%M:%S') if self.update_time else None,
            'offline_time': self.offline_time.strftime('%Y-%m-%d %H:%M:%S') if self.offline_time else None,
            'remark': self.remark
        }

class LoadBalancerResource(db.Model):
    """负载均衡资源表"""
    __tablename__ = 'load_balancer_resource'
    id = db.Column(db.BigInteger, primary_key=True, autoincrement=True)
    loadbalancer_id = db.Column(db.String(128), unique=True, nullable=False, comment='实例ID')
    lb_name = db.Column(db.String(128), nullable=False, comment='名称')
    vip = db.Column(db.String(64), nullable=False, comment='VIP')
    ip_version = db.Column(db.Enum('ipv4', 'ipv6', name='ip_version_enum'), comment='IP协议版本')
    belong_network = db.Column(db.String(128), nullable=False, comment='所属网络')
    lb_type = db.Column(db.Integer, comment='对应腾讯云返回值Forward，1为通用负载均衡，0为传统负载均衡')
    zone_region = db.Column(db.String(100), comment='所属区域')
    create_time = db.Column(db.DateTime, default=get_beijing_time, comment='创建时间')
    original_source = db.Column(db.String(128), comment='来源')
    lb_status = db.Column(db.Enum('online', 'offline', name='lb_status_enum'), default='online', comment='负载均衡状态')
    offline_time = db.Column(db.DateTime, comment='下线时间')

    def __repr__(self):
        return f'<LoadBalancerResource {self.lb_name}>'

    def to_dict(self):
        """将对象转换为字典"""
        return {
            'id': self.id,
            'loadbalancer_id': self.loadbalancer_id,
            'lb_name': self.lb_name,
            'vip': self.vip,
            'ip_version': self.ip_version,
            'belong_network': self.belong_network,
            'lb_type': self.lb_type,
            'zone_region': self.zone_region,
            'create_time': self.create_time.strftime('%Y-%m-%d %H:%M:%S') if self.create_time else None,
            'original_source': self.original_source,
            'lb_status': self.lb_status,
            'offline_time': self.offline_time.strftime('%Y-%m-%d %H:%M:%S') if self.offline_time else None
        }

class DatabaseResource(db.Model):
    """数据库资源表"""
    __tablename__ = 'database_resource'
    id = db.Column(db.BigInteger, primary_key=True, autoincrement=True, comment='id')
    db_name = db.Column(db.String(128), nullable=False, comment='数据库名')
    db_ip = db.Column(db.String(15), nullable=False, comment='数据库IP')
    db_type = db.Column(db.String(64), nullable=False, comment='数据库类型')
    purpose = db.Column(db.String(128), nullable=False, comment='数据库用途')
    remark = db.Column(db.Text, comment='备注')
    db_status = db.Column(db.Enum('online', 'offline', name='db_status_enum'), default='online', comment='数据库状态')
    creat_time = db.Column(db.DateTime, default=get_beijing_time, comment='创建时间')
    offline_time = db.Column(db.DateTime, comment='下线时间')

    def __repr__(self):
        return f'<DatabaseResource {self.db_name}>'

    def to_dict(self):
        """将对象转换为字典"""
        return {
            'id': self.id,
            'db_name': self.db_name,
            'db_ip': self.db_ip,
            'db_type': self.db_type,
            'purpose': self.purpose,
            'remark': self.remark,
            'creat_time': self.creat_time.strftime('%Y-%m-%d %H:%M:%S') if self.creat_time else None,
            'db_status': self.db_status,
            'offline_time': self.offline_time.strftime('%Y-%m-%d %H:%M:%S') if self.offline_time else None
        }

class EnvironmentBase(db.Model):
    """应用环境基表"""
    __tablename__ = 'environment_base'
    id = db.Column(db.BigInteger, primary_key=True, autoincrement=True)
    env_en_name = db.Column(db.String(100), comment='应用环境英文名')
    env_cn_name = db.Column(db.String(100), comment='应用环境中文名')

    def __repr__(self):
        return f'<EnvironmentBase {self.env_en_name}>'

    def to_dict(self):
        """将对象转换为字典"""
        return {
            'id': self.id,
            'env_en_name': self.env_en_name,
            'env_cn_name': self.env_cn_name
        }

class DeployMethod(db.Model):
    """部署方式表"""
    __tablename__ = 'deploy_method'
    id = db.Column(db.BigInteger, primary_key=True, autoincrement=True)
    deploy_code = db.Column(db.String(100), comment='部署方式英文')
    deploy_name = db.Column(db.String(100), comment='部署方式名称')

    def __repr__(self):
        return f'<DeployMethod {self.deploy_name}>'

    def to_dict(self):
        """将对象转换为字典"""
        return {
            'id': self.id,
            'deploy_code': self.deploy_code,
            'deploy_name': self.deploy_name
        }

class ApplicationEnvironment(db.Model):
    """应用环境表"""
    __tablename__ = 'application_environment'
    id = db.Column(db.BigInteger, primary_key=True, autoincrement=True, comment='id')
    application_id = db.Column(db.BigInteger, db.ForeignKey('application.id'), nullable=False, comment='application_id')
    env_id = db.Column(db.BigInteger, db.ForeignKey('environment_base.id'), nullable=False, comment='env_id')
    app_deploy_method = db.Column(db.String(256), nullable=False, comment='本套环境应用部署方式')
    app_env_url = db.Column(db.String(256), comment='本套环境应用URL')
    app_env_port = db.Column(db.BigInteger, comment='本套环境应用端口')
    app_env_status = db.Column(db.Enum('online', 'offline', name='app_env_status_enum'), default='online', comment='本套环境的状态')
    create_time = db.Column(db.DateTime, default=get_beijing_time, nullable=False, comment='创建时间')
    update_time = db.Column(db.DateTime, default=get_beijing_time, onupdate=get_beijing_time, comment='更新时间')
    offline_time = db.Column(db.DateTime, comment='下线时间')
    app_env_remark = db.Column(db.String(256), comment='对本套环境的描述')

    # 关联关系
    application = db.relationship('Application', backref=db.backref('environments', lazy='dynamic'))
    environment = db.relationship('EnvironmentBase', backref=db.backref('applications', lazy='dynamic'))

    def __repr__(self):
        return f'<ApplicationEnvironment {self.id}>'

    def to_dict(self):
        """将对象转换为字典"""
        return {
            'id': self.id,
            'application_id': self.application_id,
            'env_id': self.env_id,
            'app_deploy_method': self.app_deploy_method,
            'app_env_url': self.app_env_url,
            'app_env_port': self.app_env_port,
            'app_env_status': self.app_env_status,
            'create_time': self.create_time.strftime('%Y-%m-%d %H:%M:%S') if self.create_time else None,
            'update_time': self.update_time.strftime('%Y-%m-%d %H:%M:%S') if self.update_time else None,
            'offline_time': self.offline_time.strftime('%Y-%m-%d %H:%M:%S') if self.offline_time else None,
            'app_env_remark': self.app_env_remark
        }

class ApplicationEnvLb(db.Model):
    """应用环境负载均衡关联表"""
    __tablename__ = 'application_env_lb'
    application_environment_id = db.Column(db.BigInteger, db.ForeignKey('application_environment.id'), primary_key=True, comment='env_id')
    lb_id = db.Column(db.BigInteger, db.ForeignKey('load_balancer_resource.id'), primary_key=True, comment='lb_id')

    # 关联关系
    application_environment = db.relationship('ApplicationEnvironment', backref=db.backref('load_balancers', lazy='dynamic'))
    load_balancer = db.relationship('LoadBalancerResource', backref=db.backref('application_environments', lazy='dynamic'))

    def __repr__(self):
        return f'<ApplicationEnvLb {self.application_environment_id}_{self.lb_id}>'

class ApplicationEnvServer(db.Model):
    """应用环境服务器关联表"""
    __tablename__ = 'application_env_server'
    application_environment_id = db.Column(db.BigInteger, db.ForeignKey('application_environment.id'), primary_key=True, comment='application_environment_id')
    server_id = db.Column(db.BigInteger, db.ForeignKey('server_resource.id'), primary_key=True, comment='server_id')
    server_port = db.Column(db.String(100), comment='应用端口')

    # 关联关系
    application_environment = db.relationship('ApplicationEnvironment', backref=db.backref('servers', lazy='dynamic'))
    server = db.relationship('ServerResource', backref=db.backref('application_environments', lazy='dynamic'))

    def __repr__(self):
        return f'<ApplicationEnvServer {self.application_environment_id}_{self.server_id}>'

class ApplicationEnvDatabase(db.Model):
    """应用环境数据库关联表"""
    __tablename__ = 'application_env_database'
    application_environment_id = db.Column(db.BigInteger, db.ForeignKey('application_environment.id'), primary_key=True, comment='env_id')
    db_id = db.Column(db.BigInteger, db.ForeignKey('database_resource.id'), primary_key=True, comment='db_id')

    # 关联关系
    application_environment = db.relationship('ApplicationEnvironment', backref=db.backref('databases', lazy='dynamic'))
    database = db.relationship('DatabaseResource', backref=db.backref('application_environments', lazy='dynamic'))

    def __repr__(self):
        return f'<ApplicationEnvDatabase {self.application_environment_id}_{self.db_id}>'

class NetAreaBase(db.Model):
    """网络安全区基表"""
    __tablename__ = 'net_area_base'
    id = db.Column(db.BigInteger, primary_key=True, autoincrement=True, comment='id')
    net_area_name = db.Column(db.String(128), comment='区域名称')
    net_area_des = db.Column(db.String(128), comment='区域描述')

    def __repr__(self):
        return f'<NetAreaBase {self.net_area_name}>'

    def to_dict(self):
        """将对象转换为字典"""
        return {
            'id': self.id,
            'net_area_name': self.net_area_name,
            'net_area_des': self.net_area_des
        }

class EnvNetConf(db.Model):
    """新建云主机配置表"""
    __tablename__ = 'env_net_conf'
    id = db.Column(db.BigInteger, primary_key=True, autoincrement=True, comment='id')
    env_id = db.Column(db.BigInteger, db.ForeignKey('environment_base.id'), nullable=False, comment='环境ID')
    net_area_id = db.Column(db.BigInteger, db.ForeignKey('net_area_base.id'), nullable=False, comment='网络安全区ID')
    subnetid = db.Column(db.String(128), comment='子网ID')
    placement = db.Column(db.String(128), comment='可用区')
    vpcid = db.Column(db.String(128), comment='VPC ID')
    imageid = db.Column(db.String(128), comment='镜像ID')
    instance_type = db.Column(db.String(128), comment='实例机型')
    securitygroupid = db.Column(db.String(128), comment='安全组ID')

    # 关联关系
    environment = db.relationship('EnvironmentBase', backref=db.backref('net_configs', lazy='dynamic'))
    net_area = db.relationship('NetAreaBase', backref=db.backref('env_configs', lazy='dynamic'))

    def __repr__(self):
        return f'<EnvNetConf {self.env_id}_{self.net_area_id}>'

    def to_dict(self):
        """将对象转换为字典"""
        return {
            'id': self.id,
            'env_id': self.env_id,
            'net_area_id': self.net_area_id,
            'subnetid': self.subnetid,
            'placement': self.placement,
            'vpcid': self.vpcid,
            'imageid': self.imageid,
            'instance_type': self.instance_type,
            'securitygroupid': self.securitygroupid
        }

class CreatCvmHostname(db.Model):
    """创建CVM的最新主机名"""
    __tablename__ = 'creat_cvm_hostname'
    nscode = db.Column(db.String(100), primary_key=True, comment='主机名识别码')
    current_hostname = db.Column(db.String(100), comment='当前的主机名，这次创建完成后位数自动加1')

    def __repr__(self):
        return f'<CreatCvmHostname {self.nscode}:{self.current_hostname}>'

    def to_dict(self):
        """将对象转换为字典"""
        return {
            'nscode': self.nscode,
            'current_hostname': self.current_hostname
        }

class CvmCreationRecord(db.Model):
    """CVM创建记录表"""
    __tablename__ = 'cvm_creation_record'
    id = db.Column(db.BigInteger, primary_key=True, autoincrement=True, comment='id')
    application_id = db.Column(db.BigInteger, db.ForeignKey('application.id'), nullable=False, comment='应用ID')
    environment_id = db.Column(db.BigInteger, db.ForeignKey('environment_base.id'), nullable=False, comment='环境ID')
    net_area_id = db.Column(db.BigInteger, db.ForeignKey('net_area_base.id'), nullable=False, comment='网络安全区ID')
    instance_id = db.Column(db.String(128), comment='腾讯云实例ID')
    hostname = db.Column(db.String(128), nullable=False, comment='主机名')
    instance_type = db.Column(db.String(128), comment='实例机型')
    placement = db.Column(db.String(128), comment='可用区')
    vpc_id = db.Column(db.String(128), comment='VPC ID')
    subnet_id = db.Column(db.String(128), comment='子网ID')
    security_group_id = db.Column(db.String(128), comment='安全组ID')
    image_id = db.Column(db.String(128), comment='镜像ID')
    server_port = db.Column(db.String(100), comment='应用端口')
    creation_status = db.Column(db.Enum('creating', 'success', 'failed', name='creation_status_enum'),
                               default='creating', comment='创建状态')
    error_message = db.Column(db.Text, comment='错误信息')
    create_time = db.Column(db.DateTime, default=get_beijing_time, nullable=False, comment='创建时间')
    complete_time = db.Column(db.DateTime, comment='完成时间')
    created_by = db.Column(db.BigInteger, db.ForeignKey('user.id'), nullable=False, comment='创建用户')
    remark = db.Column(db.Text, comment='备注')

    # 关联关系
    application = db.relationship('Application', backref=db.backref('cvm_records', lazy='dynamic'))
    environment = db.relationship('EnvironmentBase', backref=db.backref('cvm_records', lazy='dynamic'))
    net_area = db.relationship('NetAreaBase', backref=db.backref('cvm_records', lazy='dynamic'))
    creator = db.relationship('User', backref=db.backref('created_cvms', lazy='dynamic'))

    def __repr__(self):
        return f'<CvmCreationRecord {self.hostname}>'

    def to_dict(self):
        """将对象转换为字典"""
        return {
            'id': self.id,
            'application_id': self.application_id,
            'environment_id': self.environment_id,
            'net_area_id': self.net_area_id,
            'instance_id': self.instance_id,
            'hostname': self.hostname,
            'instance_type': self.instance_type,
            'placement': self.placement,
            'vpc_id': self.vpc_id,
            'subnet_id': self.subnet_id,
            'security_group_id': self.security_group_id,
            'image_id': self.image_id,
            'server_port': self.server_port,
            'creation_status': self.creation_status,
            'error_message': self.error_message,
            'create_time': self.create_time.isoformat() if self.create_time else None,
            'complete_time': self.complete_time.isoformat() if self.complete_time else None,
            'created_by': self.created_by,
            'remark': self.remark
        }
