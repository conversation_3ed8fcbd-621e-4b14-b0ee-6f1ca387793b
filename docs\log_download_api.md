# 日志下载 API 文档

本文档描述了日志下载模块（`log_download.py`）中的所有 API 接口。

## 目录

- [获取顶级文件夹列表](#获取顶级文件夹列表)
- [获取指定路径下的文件夹和文件列表](#获取指定路径下的文件夹和文件列表)
- [下载文件](#下载文件)

## 获取顶级文件夹列表

获取腾讯云对象存储桶中的顶级文件夹列表，支持按前缀过滤。

**请求方式**：GET

**URL**：`/api/logs/folders`

**查询参数**：

| 参数名  | 类型   | 必填 | 描述                     |
|---------|--------|------|--------------------------|
| prefix  | string | 否   | 文件夹名称前缀，用于过滤和搜索 |

**响应示例**：

```json
{
  "folders": [
    {
      "name": "folder1",
      "path": "folder1/"
    },
    {
      "name": "folder2",
      "path": "folder2/"
    }
  ]
}
```

**错误响应**：

```json
{
  "error": "未配置腾讯云API密钥"
}
```

**说明**：
- 返回腾讯云对象存储桶中的顶级文件夹列表
- 支持按文件夹名称前缀进行过滤和搜索
- 当prefix参数不以斜杠结尾时，将启用搜索模式，可以搜索任意位置的文件夹
- 当prefix参数以斜杠结尾或为空时，将使用标准模式，只列出顶级文件夹
- 需要预先配置腾讯云API密钥
- 需要安装腾讯云对象存储SDK（cos-python-sdk-v5）

## 获取指定路径下的文件夹和文件列表

获取腾讯云对象存储桶中指定路径下的文件夹和文件列表。

**请求方式**：GET

**URL**：`/api/logs/objects`

**查询参数**：

| 参数名 | 类型   | 必填 | 描述                                |
|--------|--------|------|-------------------------------------|
| path   | string | 是   | 路径，例如 'folder1/' 或 'folder1/folder2/' |
| search | string | 否   | 搜索关键字，用于在当前路径下搜索文件夹和文件 |

**响应示例**：

```json
{
  "folders": [
    {
      "name": "subfolder1",
      "path": "folder1/subfolder1/",
      "type": "folder"
    }
  ],
  "files": [
    {
      "name": "file1.log",
      "path": "folder1/file1.log",
      "size": 1024,
      "last_modified": "2023-05-20T12:34:56.000Z",
      "type": "file"
    }
  ]
}
```

**错误响应**：

```json
{
  "error": "未配置腾讯云API密钥"
}
```

**说明**：
- 返回指定路径下的文件夹和文件列表
- 支持通过search参数在当前路径下搜索文件夹和文件
- 当search参数不为空时，将启用搜索模式，可以搜索当前路径下任意位置的文件夹和文件
- 文件夹列表包含名称和路径信息
- 文件列表包含名称、路径、大小和最后修改时间信息
- 需要预先配置腾讯云API密钥
- 需要安装腾讯云对象存储SDK（cos-python-sdk-v5）

## 下载文件

下载腾讯云对象存储桶中的文件。

**请求方式**：GET

**URL**：`/api/logs/download`

**查询参数**：

| 参数名 | 类型   | 必填 | 描述                            |
|--------|--------|------|----------------------------------|
| path   | string | 是   | 文件路径，例如 'folder1/file1.log' |

**响应**：
- 文件内容（二进制流）
- Content-Disposition 头设置为 attachment，浏览器将提示下载文件

**错误响应**：

```json
{
  "error": "缺少文件路径"
}
```

**说明**：
- 下载指定路径的文件
- 返回文件的二进制内容，浏览器将提示下载
- 需要预先配置腾讯云API密钥
- 需要安装腾讯云对象存储SDK（cos-python-sdk-v5）
