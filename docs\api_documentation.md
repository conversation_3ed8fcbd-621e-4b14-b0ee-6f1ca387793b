# ICMS2 API 接口文档

本文档详细描述了 ICMS2 系统的所有 API 接口，包括请求方式、URL、参数和响应格式。

## 目录

- [认证与用户管理](#认证与用户管理)
  - [用户登录](#用户登录)
  - [验证 Token](#验证-token)
  - [获取用户列表](#获取用户列表)
  - [获取用户详情](#获取用户详情)
  - [分配用户角色](#分配用户角色)
  - [获取用户权限](#获取用户权限)
  - [获取所有用户的权限汇总](#获取所有用户的权限汇总)
- [LDAP 管理](#ldap-管理)
  - [获取所有 LDAP 配置](#获取所有-ldap-配置)
  - [获取 LDAP 配置详情](#获取-ldap-配置详情)
  - [创建 LDAP 配置](#创建-ldap-配置)
  - [更新 LDAP 配置](#更新-ldap-配置)
  - [删除 LDAP 配置](#删除-ldap-配置)
  - [测试 LDAP 搜索](#测试-ldap-搜索)
  - [从 LDAP 导入用户](#从-ldap-导入用户)
- [角色管理](#角色管理)
  - [获取所有角色](#获取所有角色)
  - [获取角色详情](#获取角色详情)
  - [创建角色](#创建角色)
  - [更新角色](#更新角色)
  - [删除角色](#删除角色)
- [模块与菜单管理](#模块与菜单管理)
  - [获取所有模块](#获取所有模块)
  - [获取菜单列表](#获取菜单列表)
  - [获取模块及其菜单](#获取模块及其菜单)

## 认证与用户管理

### 用户登录

**请求方式**：POST

**URL**：`/api/login`

**请求参数**：

| 参数名   | 类型   | 必填 | 描述   |
|----------|--------|------|--------|
| username | string | 是   | 用户名 |
| password | string | 是   | 密码   |

**请求示例**：
```json
{
  "username": "admin",
  "password": "password123"
}
```

**响应示例**：

成功响应：
```json
{
  "id": 1,
  "username": "admin",
  "display_name": "系统管理员",
  "is_local_admin": true,
  "permissions": {
    "roles": [
      {
        "id": 1,
        "name": "超级管理员"
      }
    ],
    "modules": [
      {
        "id": 1,
        "name": "resource_query",
        "display_name": "资源查询",
        "menus": [
          {
            "id": 1,
            "name": "app_query",
            "display_name": "应用查询"
          }
        ]
      }
    ]
  },
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

失败响应：
```json
{
  "error": "用户不存在或密码错误"
}
```

**说明**：
- 登录流程首先检查本地用户，如果存在则验证密码
- 如果不是本地用户，则尝试通过 LDAP 进行认证
- LDAP 认证会尝试所有活跃的 LDAP 配置，直到找到用户或尝试完所有配置
- 登录成功后返回用户信息、权限信息和 JWT token
- token 有效期为 24 小时
- 同时会设置 HTTP-only cookie，包含相同的 token

### 验证 Token

**请求方式**：GET

**URL**：`/api/validate-token`

**请求头**：
```
Authorization: Bearer <token>
```

或者使用 Cookie 中的 token

**响应示例**：

成功响应：
```json
{
  "valid": true,
  "user": {
    "id": 1,
    "username": "admin",
    "display_name": "系统管理员",
    "is_local_admin": true
  },
  "permissions": {
    "roles": [
      {
        "id": 1,
        "name": "超级管理员"
      }
    ],
    "modules": [
      {
        "id": 1,
        "name": "resource_query",
        "display_name": "资源查询",
        "menus": [
          {
            "id": 1,
            "name": "app_query",
            "display_name": "应用查询"
          }
        ]
      }
    ]
  },
  "new_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

失败响应：
```json
{
  "valid": false,
  "error": "认证令牌已过期"
}
```

**说明**：
- 验证 token 的有效性，并返回用户信息和权限
- 如果 token 有效，返回用户信息和权限
- 如果 token 即将过期（小于 30 分钟），返回新 token
- 如果 token 无效或已过期，返回错误信息

### 获取用户列表

**请求方式**：GET

**URL**：`/api/users`

**请求参数**：

| 参数名   | 类型   | 必填 | 描述     |
|----------|--------|------|----------|
| username | string | 否   | 用户名过滤 |

**响应示例**：
```json
[
  {
    "id": 1,
    "username": "admin",
    "display_name": "系统管理员",
    "email": "<EMAIL>",
    "roles": ["超级管理员"]
  },
  {
    "id": 2,
    "username": "user1",
    "display_name": "用户1",
    "email": "<EMAIL>",
    "roles": ["普通用户"]
  }
]
```

**说明**：
- 返回系统中的用户列表
- 如果提供了 username 参数，则按用户名过滤
- 每个用户包含基本信息和所属角色

### 获取用户详情

**请求方式**：GET

**URL**：`/api/users/<user_id>`

**URL 参数**：

| 参数名  | 类型    | 必填 | 描述   |
|---------|---------|------|--------|
| user_id | integer | 是   | 用户ID |

**请求参数**：

| 参数名     | 类型   | 必填 | 描述     |
|------------|--------|------|----------|
| user_type  | string | 否   | 用户类型，默认为 'ldap' |

**响应示例**：
```json
{
  "id": 1,
  "username": "admin",
  "display_name": "系统管理员",
  "email": "<EMAIL>",
  "roles": [
    {
      "id": 1,
      "name": "超级管理员"
    }
  ]
}
```

**说明**：
- 返回指定用户的详细信息
- 包括用户基本信息和所属角色
- user_type 参数用于区分 LDAP 用户和本地用户

### 分配用户角色

**请求方式**：PUT

**URL**：`/api/users/<user_id>/roles`

**URL 参数**：

| 参数名  | 类型    | 必填 | 描述   |
|---------|---------|------|--------|
| user_id | integer | 是   | 用户ID |

**请求参数**：

| 参数名     | 类型   | 必填 | 描述     |
|------------|--------|------|----------|
| roles      | array  | 是   | 角色ID列表 |
| user_type  | string | 否   | 用户类型，默认为 'ldap' |

**请求示例**：
```json
{
  "roles": [1, 2],
  "user_type": "ldap"
}
```

**响应示例**：
```json
{
  "message": "用户角色分配成功"
}
```

**说明**：
- 为指定用户分配角色
- 会先清除用户现有的所有角色，然后分配新角色
- 用户的权限是通过角色关联获取的，分配角色后用户将获得这些角色的所有权限
- 需要指定用户类型（'ldap'或'local'），以区分不同来源的用户

### 获取用户权限

**请求方式**：GET

**URL**：`/api/users/<user_id>/permissions`

**URL 参数**：

| 参数名  | 类型    | 必填 | 描述   |
|---------|---------|------|--------|
| user_id | integer | 是   | 用户ID |

**请求参数**：

| 参数名     | 类型   | 必填 | 描述     |
|------------|--------|------|----------|
| user_type  | string | 否   | 用户类型，默认为 'ldap' |

**响应示例**：
```json
{
  "roles": [
    {
      "id": 1,
      "name": "超级管理员"
    }
  ],
  "modules": [
    {
      "id": 1,
      "name": "resource_query",
      "display_name": "资源查询",
      "menus": [
        {
          "id": 1,
          "name": "app_query",
          "display_name": "应用查询"
        },
        {
          "id": 2,
          "name": "host_query",
          "display_name": "主机查询"
        }
      ]
    },
    {
      "id": 2,
      "name": "user_management",
      "display_name": "用户管理",
      "menus": [
        {
          "id": 10,
          "name": "user_query",
          "display_name": "用户查询"
        }
      ]
    }
  ]
}
```

**说明**：
- 返回用户拥有的所有权限，以树形结构组织
- `modules` 字段包含用户有权访问的所有模块
- 每个模块下的 `menus` 字段包含该模块下用户有权访问的所有菜单
- 此接口适用于前端构建用户的导航菜单和权限控制
- 系统会自动将用户所有角色中的权限（存储为逗号分隔的ID字符串）合并并转换为树形结构

### 获取所有用户的权限汇总

**请求方式**：GET

**URL**：`/api/users/permissions`

**响应示例**：
```json
[
  {
    "id": 1,
    "username": "admin",
    "display_name": "系统管理员",
    "roles": ["超级管理员"],
    "modules": ["资源查询", "基础数据管理", "系统管理"],
    "menus": ["应用查询", "主机查询", "用户管理", "角色管理", "LDAP配置"]
  },
  {
    "id": 2,
    "username": "user1",
    "display_name": "用户1",
    "roles": ["管理员"],
    "modules": ["资源查询", "基础数据管理"],
    "menus": ["应用查询", "主机查询", "系统管理"]
  }
]
```

**说明**：
- 返回所有用户的权限汇总信息
- 每个用户包含其拥有的角色、模块和菜单的列表
- 此接口适用于管理员查看系统中所有用户的权限分配情况
- 系统会自动将用户所有角色中的权限（存储为逗号分隔的ID字符串）合并并提取模块和菜单名称

## LDAP 管理

### 获取所有 LDAP 配置

**请求方式**：GET

**URL**：`/api/ldap-config`

**响应示例**：
```json
[
  {
    "id": 1,
    "server": "ldap.example.com",
    "port": 389,
    "bind_dn": "cn=admin,dc=example,dc=com",
    "ou_path": "ou=users,dc=example,dc=com",
    "is_active": true
  },
  {
    "id": 2,
    "server": "ldap2.example.com",
    "port": 389,
    "bind_dn": "cn=admin,dc=example2,dc=com",
    "ou_path": "ou=users,dc=example2,dc=com",
    "is_active": false
  }
]
```

**说明**：
- 返回系统中所有的 LDAP 配置
- 每个配置包含服务器地址、端口、绑定 DN、OU 路径和是否激活
- 不返回密码信息

### 获取 LDAP 配置详情

**请求方式**：GET

**URL**：`/api/ldap-configs`

**响应示例**：
```json
[
  {
    "id": 1,
    "server": "ldap.example.com",
    "port": 389,
    "bind_dn": "cn=admin,dc=example,dc=com",
    "ou_path": "ou=users,dc=example,dc=com",
    "is_active": true,
    "create_time": "2023-01-01T12:00:00",
    "update_time": "2023-01-02T14:30:00"
  }
]
```

**说明**：
- 返回系统中所有 LDAP 配置的详细信息
- 包含创建时间和更新时间
- 不返回密码信息

### 创建 LDAP 配置

**请求方式**：POST

**URL**：`/api/ldap-config`

**请求参数**：

| 参数名    | 类型    | 必填 | 描述         |
|-----------|---------|------|--------------|
| server    | string  | 是   | LDAP 服务器地址 |
| port      | integer | 是   | 端口号       |
| bind_dn   | string  | 是   | 绑定 DN      |
| password  | string  | 是   | 绑定密码     |
| ou_path   | string  | 是   | OU 路径      |
| is_active | boolean | 否   | 是否激活，默认为 false |

**请求示例**：
```json
{
  "server": "ldap.example.com",
  "port": 389,
  "bind_dn": "cn=admin,dc=example,dc=com",
  "password": "admin_password",
  "ou_path": "ou=users,dc=example,dc=com",
  "is_active": true
}
```

**响应示例**：
```json
{
  "message": "LDAP 配置创建成功",
  "id": 1
}
```

**说明**：
- 创建新的 LDAP 配置
- 如果 is_active 为 true，则会将其他配置设置为非活跃
- 返回新创建的配置 ID

### 更新 LDAP 配置

**请求方式**：PUT

**URL**：`/api/ldap-config/<config_id>`

**URL 参数**：

| 参数名    | 类型    | 必填 | 描述         |
|-----------|---------|------|--------------|
| config_id | integer | 是   | LDAP 配置 ID  |

**请求参数**：

| 参数名    | 类型    | 必填 | 描述         |
|-----------|---------|------|--------------|
| server    | string  | 否   | LDAP 服务器地址 |
| port      | integer | 否   | 端口号       |
| bind_dn   | string  | 否   | 绑定 DN      |
| password  | string  | 否   | 绑定密码     |
| ou_path   | string  | 否   | OU 路径      |
| is_active | boolean | 否   | 是否激活     |

**请求示例**：
```json
{
  "server": "ldap.example.com",
  "port": 636,
  "is_active": true
}
```

**响应示例**：
```json
{
  "message": "LDAP 配置更新成功"
}
```

**说明**：
- 更新指定的 LDAP 配置
- 只需提供需要更新的字段，未提供的字段保持不变
- 如果更新 is_active 为 true，则会将其他配置设置为非活跃

### 删除 LDAP 配置

**请求方式**：DELETE

**URL**：`/api/ldap-config/<config_id>`

**URL 参数**：

| 参数名    | 类型    | 必填 | 描述         |
|-----------|---------|------|--------------|
| config_id | integer | 是   | LDAP 配置 ID  |

**响应示例**：
```json
{
  "message": "LDAP 配置删除成功"
}
```

**说明**：
- 删除指定的 LDAP 配置
- 如果系统中只有一个 LDAP 配置，可能会拒绝删除

### 测试 LDAP 搜索

**请求方式**：POST

**URL**：`/api/ldap-test-search`

**请求参数**：

| 参数名    | 类型    | 必填 | 描述         |
|-----------|---------|------|--------------|
| config_id | integer | 是   | LDAP 配置 ID  |
| username  | string  | 是   | 要搜索的用户名 |

**请求示例**：
```json
{
  "config_id": 1,
  "username": "john.doe"
}
```

**响应示例**：

成功找到用户：
```json
{
  "found": true,
  "count": 1,
  "users": [
    {
      "cn": "John Doe",
      "sAMAccountName": "john.doe",
      "displayName": "John Doe",
      "mail": "<EMAIL>"
    }
  ]
}
```

未找到用户：
```json
{
  "found": false,
  "message": "在LDAP中未找到用户: john.doe"
}
```

错误响应：
```json
{
  "error": "LDAP测试搜索失败: 连接超时"
}
```

**说明**：
- 使用指定的 LDAP 配置搜索用户
- 返回找到的用户信息，包括 CN、sAMAccountName、displayName 和 mail
- 如果未找到用户，返回相应的消息
- 如果搜索过程中出错，返回错误信息

### 从 LDAP 导入用户

**请求方式**：POST

**URL**：`/api/ldap-fetch-users`

**请求参数**：

| 参数名    | 类型    | 必填 | 描述         |
|-----------|---------|------|--------------|
| config_id | integer | 是   | LDAP 配置 ID  |

**请求示例**：
```json
{
  "config_id": 1
}
```

**响应示例**：

成功响应：
```json
{
  "message": "用户导入成功",
  "added": 10,
  "updated": 5,
  "total": 15
}
```

失败响应：
```json
{
  "error": "获取LDAP用户失败: LDAP服务器连接失败"
}
```

**说明**：
- 从指定的 LDAP 配置中导入所有用户
- 对于已存在的用户，更新其信息
- 对于不存在的用户，创建新用户
- 返回新增用户数、更新用户数和总用户数
- 如果导入过程中出错，返回错误信息

## 角色管理

### 获取所有角色

**请求方式**：GET

**URL**：`/api/roles`

**响应示例**：
```json
[
  {
    "id": 1,
    "role_name": "超级管理员",
    "description": "系统超级管理员",
    "modules": [
      {
        "id": 1,
        "name": "资源查询",
        "menus": [
          {
            "id": 1,
            "name": "应用查询"
          },
          {
            "id": 2,
            "name": "主机查询"
          }
        ]
      },
      {
        "id": 2,
        "name": "用户管理",
        "menus": [
          {
            "id": 10,
            "name": "用户查询"
          }
        ]
      }
    ]
  },
  {
    "id": 2,
    "role_name": "普通用户",
    "description": "普通用户角色",
    "modules": [
      {
        "id": 1,
        "name": "资源查询",
        "menus": [
          {
            "id": 1,
            "name": "应用查询"
          }
        ]
      }
    ]
  }
]
```

**说明**：
- 返回所有角色的列表
- 每个角色包含基本信息（ID、名称、描述）和权限信息
- 权限信息以树形结构组织，包含模块和菜单
- 系统会自动将存储在数据库中的逗号分隔ID字符串转换为对应的模块和菜单对象

### 获取角色详情

**请求方式**：GET

**URL**：`/api/roles/<role_id>`

**URL 参数**：

| 参数名  | 类型    | 必填 | 描述   |
|---------|---------|------|--------|
| role_id | integer | 是   | 角色ID |

**响应示例**：
```json
{
  "id": 1,
  "role_name": "超级管理员",
  "description": "系统超级管理员",
  "modules": [
    {
      "id": 1,
      "name": "资源查询",
      "menus": [
        {
          "id": 1,
          "name": "应用查询"
        },
        {
          "id": 2,
          "name": "主机查询"
        }
      ]
    }
  ],
  "module_ids": [1, 2, 3],
  "menu_ids": [1, 2, 3, 4, 5]
}
```

**说明**：
- 返回指定角色的详细信息
- 包括角色基本信息、权限树形结构以及原始的模块ID列表和菜单ID列表
- 模块ID列表和菜单ID列表用于前端编辑角色权限

### 创建角色

**请求方式**：POST

**URL**：`/api/roles`

**请求参数**：

| 参数名      | 类型   | 必填 | 描述     |
|-------------|--------|------|----------|
| role_name   | string | 是   | 角色名称 |
| description | string | 否   | 角色描述 |
| module_ids  | array  | 否   | 模块ID列表 |
| menu_ids    | array  | 否   | 菜单ID列表 |

**请求示例**：
```json
{
  "role_name": "运维人员",
  "description": "负责系统运维的人员",
  "module_ids": [1, 2],
  "menu_ids": [1, 2, 3, 4]
}
```

**响应示例**：
```json
{
  "message": "角色创建成功",
  "id": 3
}
```

**说明**：
- 创建新角色并分配指定的模块和菜单权限
- 角色名称必须唯一
- 返回新创建角色的ID
- 系统会将提供的模块ID数组和菜单ID数组转换为逗号分隔的字符串存储在数据库中

### 更新角色

**请求方式**：PUT

**URL**：`/api/roles/<role_id>`

**URL 参数**：

| 参数名  | 类型    | 必填 | 描述   |
|---------|---------|------|--------|
| role_id | integer | 是   | 角色ID |

**请求参数**：

| 参数名      | 类型   | 必填 | 描述     |
|-------------|--------|------|----------|
| role_name   | string | 否   | 角色名称 |
| description | string | 否   | 角色描述 |
| module_ids  | array  | 否   | 模块ID列表 |
| menu_ids    | array  | 否   | 菜单ID列表 |

**请求示例**：
```json
{
  "role_name": "高级运维",
  "description": "负责核心系统运维的人员",
  "module_ids": [1, 2, 3],
  "menu_ids": [1, 2, 3, 4, 5, 6]
}
```

**响应示例**：
```json
{
  "message": "角色更新成功"
}
```

**说明**：
- 更新指定角色的基本信息和权限
- 如果提供了模块ID和菜单ID，会更新对应的权限
- 如果某个字段未提供，则保持原值不变
- 系统会将提供的模块ID数组和菜单ID数组转换为逗号分隔的字符串存储在数据库中

### 删除角色

**请求方式**：DELETE

**URL**：`/api/roles/<role_id>`

**URL 参数**：

| 参数名  | 类型    | 必填 | 描述   |
|---------|---------|------|--------|
| role_id | integer | 是   | 角色ID |

**响应示例**：

成功响应：
```json
{
  "message": "角色删除成功"
}
```

失败响应（角色已分配给用户）：
```json
{
  "error": "无法删除角色，该角色已分配给用户",
  "user_count": 15,
  "users": [
    {
      "id": 1,
      "username": "user1",
      "display_name": "用户1"
    },
    {
      "id": 2,
      "username": "user2",
      "display_name": "用户2"
    }
  ],
  "has_more": true
}
```

**说明**：
- 删除指定的角色
- 如果角色已分配给用户，则无法删除，会返回已分配该角色的用户列表
- 为避免响应过大，只返回前 10 个用户，并通过 has_more 字段指示是否还有更多用户

## 模块与菜单管理

### 获取所有模块

**请求方式**：GET

**URL**：`/api/modules`

**响应示例**：
```json
[
  {
    "id": 1,
    "module_name": "resource_query",
    "display_name": "资源查询",
    "description": "提供对企业IT资源的综合查询功能"
  },
  {
    "id": 2,
    "module_name": "basic_data",
    "display_name": "基础数据管理",
    "description": "管理系统基础数据"
  },
  {
    "id": 3,
    "module_name": "user_management",
    "display_name": "用户管理",
    "description": "管理系统用户和权限"
  }
]
```

**说明**：
- 返回系统中所有模块的列表
- 每个模块包含ID、模块名称（英文标识）、显示名称和描述信息
- 此接口适用于需要获取所有模块基本信息的场景，如