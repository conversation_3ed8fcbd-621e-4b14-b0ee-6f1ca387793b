#!/bin/sh

# 等待数据库准备就绪
echo 'Waiting for MySQL to be ready...'
while ! nc -z db 3306; do
  sleep 1
done
echo 'MySQL is ready!'

# 初始化数据库表
python -c 'from app import create_app, db; from config import config; app = create_app(config["production"]); app.app_context().push(); db.create_all()'

# 初始化模块和菜单数据
python -c 'from scripts.init_modules_menus import init_modules_menus; init_modules_menus()'

# 启动应用
gunicorn --bind 0.0.0.0:5000 --workers 4 --timeout 120 run:app
