"""
用户工具模块，提供用户相关的工具函数
"""
from flask import g, current_app

def get_current_user_id():
    """
    获取当前用户ID
    
    Returns:
        int: 当前用户ID，如果没有用户信息，则返回1（系统用户）
    """
    if hasattr(g, 'user') and g.user:
        user_id = getattr(g.user, 'id', 1)
        current_app.logger.debug(f"获取当前用户ID: {user_id}, 用户名: {getattr(g.user, 'username', 'unknown')}")
        return user_id
    else:
        current_app.logger.debug("获取当前用户ID: 未找到用户信息，使用默认ID 1")
        return 1  # 默认为系统用户ID

def get_current_username():
    """
    获取当前用户名
    
    Returns:
        str: 当前用户名，如果没有用户信息，则返回'system'
    """
    if hasattr(g, 'user') and g.user:
        username = getattr(g.user, 'username', 'system')
        return username
    elif hasattr(g, 'username'):
        return g.username
    else:
        return 'system'  # 默认为系统用户
