# 腾讯云虚拟机管理模块 API 文档

## 概述

腾讯云虚拟机管理模块提供了创建、监控和管理腾讯云CVM实例的完整功能。该模块支持自动化的虚拟机创建流程，包括主机名管理、网络配置、实例监控和资源绑定。

## 基础信息

- **模块名称**: cvm_management
- **URL前缀**: `/api/cvm`
- **认证方式**: JWT Token
- **数据格式**: JSON

## API 接口列表

### 1. 获取网络安全区列表

获取所有可用的网络安全区，供前端下拉列表选择使用。

**接口地址**: `GET /api/cvm/net-areas`

**请求参数**: 无

**响应示例**:
```json
{
    "message": "获取网络安全区列表成功",
    "data": [
        {
            "id": 1,
            "net_area_name": "生产区",
            "net_area_des": "生产环境网络安全区"
        },
        {
            "id": 2,
            "net_area_name": "测试区",
            "net_area_des": "测试环境网络安全区"
        }
    ]
}
```

### 2. 创建腾讯云虚拟机

创建新的腾讯云CVM实例，支持自动主机名分配和网络配置。

**接口地址**: `POST /api/cvm/create`

**请求参数**:
```json
{
    "application_id": 1,
    "environment_id": 2,
    "app_en_name": "myapp",
    "env_en_name": "prod",
    "net_area_id": 1,
    "server_port": "8080,8081"
}
```

**参数说明**:
- `application_id` (必需): 应用ID
- `environment_id` (必需): 环境ID
- `app_en_name` (必需): 系统英文名
- `env_en_name` (必需): 环境英文名
- `net_area_id` (必需): 网络安全区ID
- `server_port` (可选): 应用端口，多个端口用逗号分隔

**响应示例**:
```json
{
    "message": "CVM创建请求已提交",
    "data": {
        "creation_record_id": 123,
        "hostname": "htsh101269",
        "instance_id": "ins-htsh101269",
        "status": "creating"
    }
}
```

### 3. 获取创建状态

查询CVM创建任务的当前状态。

**接口地址**: `GET /api/cvm/status/{record_id}`

**路径参数**:
- `record_id`: 创建记录ID

**响应示例**:
```json
{
    "message": "获取创建状态成功",
    "data": {
        "id": 123,
        "application_id": 1,
        "environment_id": 2,
        "hostname": "htsh101269",
        "instance_id": "ins-htsh101269",
        "creation_status": "success",
        "create_time": "2025-05-24T10:30:00",
        "complete_time": "2025-05-24T10:35:00",
        "error_message": null
    }
}
```

### 4. 获取创建记录列表

获取CVM创建记录的分页列表，支持多种过滤条件。

**接口地址**: `GET /api/cvm/records`

**查询参数**:
- `page` (可选): 页码，默认1
- `per_page` (可选): 每页数量，默认20
- `application_id` (可选): 按应用ID过滤
- `environment_id` (可选): 按环境ID过滤
- `status` (可选): 按状态过滤 (creating/success/failed)

**响应示例**:
```json
{
    "message": "获取创建记录成功",
    "data": [
        {
            "id": 123,
            "application_id": 1,
            "application_name": "示例应用",
            "environment_id": 2,
            "environment_name": "生产环境",
            "net_area_id": 1,
            "net_area_name": "生产区",
            "hostname": "htsh101269",
            "instance_id": "ins-htsh101269",
            "creation_status": "success",
            "create_time": "2025-05-24T10:30:00",
            "complete_time": "2025-05-24T10:35:00"
        }
    ],
    "pagination": {
        "page": 1,
        "per_page": 20,
        "total": 50,
        "pages": 3,
        "has_prev": false,
        "has_next": true
    }
}
```

### 5. 检查CVM状态

检查CVM实例的当前状态并自动更新创建记录。

**接口地址**: `POST /api/cvm/check-status/{record_id}`

**路径参数**:
- `record_id`: 创建记录ID

**响应示例**:
```json
{
    "message": "实例已运行，状态已更新",
    "data": {
        "id": 123,
        "creation_status": "success",
        "complete_time": "2025-05-24T10:35:00"
    },
    "instance_state": "RUNNING"
}
```

### 6. 完成CVM创建流程

完成CVM创建流程，获取实例详细信息并写入服务器资源表，建立应用环境绑定关系。

**接口地址**: `POST /api/cvm/finalize/{record_id}`

**路径参数**:
- `record_id`: 创建记录ID

**响应示例**:
```json
{
    "message": "CVM创建流程完成",
    "data": {
        "server_id": 456,
        "hostname": "htsh101269",
        "ipv4": "**********",
        "instance_id": "ins-htsh101269"
    }
}
```

## 创建流程说明

### 1. 完整创建流程

1. **获取网络安全区**: 调用 `/net-areas` 获取可选的网络安全区
2. **提交创建请求**: 调用 `/create` 提交CVM创建请求
3. **监控创建状态**:
   - 方式一：定期调用 `/status/{record_id}` 查看创建状态
   - 方式二：调用 `/check-status/{record_id}` 主动检查并更新状态
4. **完成创建流程**: 实例运行后调用 `/finalize/{record_id}` 完成流程

### 2. 推荐的状态监控流程

```
创建请求 -> 等待30秒 -> 检查状态 -> 实例运行? -> 完成流程
    |                        |           |
    |                        |           否 -> 等待10秒 -> 重新检查
    |                        |
    失败 <- 检查失败 <----------
```

### 3. 主机名分配规则

- 环境英文名为 `dr` 或 `cow` 时，使用 `htsz` 前缀
- 其他环境使用 `htsh` 前缀
- 主机名格式：前缀 + 6位数字（如：htsh101269）
- 每次创建后自动递增数字部分

### 4. Region判断规则

- placement包含 `ap-shanghai-fsi` 时，Region为 `ap-shanghai-fsi`
- placement包含 `ap-shenzhen-fsi` 时，Region为 `ap-shenzhen-fsi`
- 默认使用 `ap-shanghai-fsi`

### 5. 网络配置获取

系统根据 `environment_id` 和 `net_area_id` 从 `env_net_conf` 表获取：
- 子网ID (subnetid)
- 可用区 (placement)
- VPC ID (vpcid)
- 镜像ID (imageid)
- 实例机型 (instance_type)
- 安全组ID (securitygroupid)

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| 400 | 请求参数错误 |
| 404 | 资源不存在（应用、环境、创建记录等） |
| 500 | 服务器内部错误 |

## 注意事项

1. **权限要求**: 所有接口都需要有效的JWT Token
2. **腾讯云集成**: 当前版本使用模拟数据，实际部署时需要配置腾讯云SDK
3. **主机名唯一性**: 系统自动确保主机名的唯一性
4. **资源绑定**: 创建成功的CVM会自动绑定到指定的应用环境
5. **状态跟踪**: 所有创建操作都会记录详细的状态和历史信息

## 数据库依赖

该模块依赖以下数据库表：
- `net_area_base`: 网络安全区基表
- `env_net_conf`: 环境网络配置表
- `creat_cvm_hostname`: CVM主机名管理表
- `cvm_creation_record`: CVM创建记录表
- `application`: 应用表
- `environment_base`: 环境基表
- `server_resource`: 服务器资源表
- `application_env_server`: 应用环境服务器关联表
