"""
日志下载模块
"""
from flask import Blueprint, request, jsonify, current_app, g, Response, stream_with_context
from .. import db, beijing_tz
from ..utils.cloud_api import get_tencent_credentials
from ..utils.decorators import log_route, log_operation
import json
from sqlalchemy import or_
import os
import re

# 创建蓝图
log_download = Blueprint('log_download', __name__)

# 定义常量
BUCKET_NAME = 'cvmlog-private-prd-sh-v5-1251979869'
REGION = 'ap-shanghai-fsi'  # 上海金融区域

@log_download.route('/logs/folders', methods=['GET'])
@log_route
def get_top_folders():
    """
    获取顶级文件夹列表

    Query Parameters:
        prefix (str, optional): 文件夹名称前缀，用于过滤

    Returns:
        JSON: 文件夹列表
    """
    try:
        # 获取查询参数
        prefix = request.args.get('prefix', '')

        # 记录请求日志
        client_ip = request.remote_addr
        user_id = g.user.id if hasattr(g, 'user') else 'unknown'
        current_app.logger.info(f"获取顶级文件夹列表: client_ip={client_ip}, user_id={user_id}, prefix={prefix}")

        # 获取腾讯云API密钥
        secret_id, secret_key = get_tencent_credentials()
        if not secret_id or not secret_key:
            current_app.logger.error(f"获取顶级文件夹列表失败: client_ip={client_ip}, user_id={user_id}, error=未配置腾讯云API密钥")
            return jsonify({'error': '未配置腾讯云API密钥'}), 400

        # 导入腾讯云SDK
        try:
            from qcloud_cos import CosConfig
            from qcloud_cos import CosS3Client
        except ImportError:
            current_app.logger.error(f"获取顶级文件夹列表失败: client_ip={client_ip}, user_id={user_id}, error=未安装腾讯云对象存储SDK")
            return jsonify({'error': '未安装腾讯云对象存储SDK，请执行 pip install -U cos-python-sdk-v5'}), 500

        # 创建配置
        config = CosConfig(Region=REGION, SecretId=secret_id, SecretKey=secret_key)

        # 创建客户端
        client = CosS3Client(config)

        # 记录调试信息
        current_app.logger.debug(f"查询参数: prefix={prefix}")

        # 如果是搜索模式（前缀不为空且不以斜杠结尾），使用不同的查询方式
        is_search_mode = prefix and not prefix.endswith('/')

        if is_search_mode:
            # 搜索模式：不使用分隔符，获取所有匹配前缀的对象
            response = client.list_objects(
                Bucket=BUCKET_NAME,
                Prefix=prefix,
                MaxKeys=1000  # 增加返回的最大数量
            )

            # 记录调试信息
            if 'Contents' in response:
                current_app.logger.debug(f"搜索模式返回的对象数量: {len(response.get('Contents', []))}")

            # 提取文件夹列表（通过分析对象路径）
            folders = []
            folder_set = set()  # 用于去重

            if 'Contents' in response:
                for item in response['Contents']:
                    key = item.get('Key', '')

                    # 跳过不是文件夹的对象
                    if not '/' in key:
                        continue

                    # 提取第一级文件夹
                    parts = key.split('/')
                    if len(parts) > 1:
                        folder_path = parts[0] + '/'
                        folder_name = parts[0]

                        # 去重
                        if folder_path not in folder_set:
                            folder_set.add(folder_path)
                            folders.append({
                                'name': folder_name,
                                'path': folder_path
                            })
        else:
            # 标准模式：使用分隔符获取文件夹
            response = client.list_objects(
                Bucket=BUCKET_NAME,
                Delimiter='/',  # 使用分隔符获取文件夹
                Prefix=prefix
            )

            # 提取文件夹列表
            folders = []
            if 'CommonPrefixes' in response:
                for folder in response['CommonPrefixes']:
                    folder_path = folder.get('Prefix', '')

                    # 提取文件夹名称（去掉路径和末尾的斜杠）
                    folder_name = folder_path
                    if prefix and folder_name.startswith(prefix):
                        folder_name = folder_name[len(prefix):]
                    folder_name = folder_name.rstrip('/')

                    # 如果文件夹名包含路径，只保留最后一部分
                    if '/' in folder_name:
                        folder_name = folder_name.split('/')[0]

                    if folder_name:  # 确保文件夹名不为空
                        folders.append({
                            'name': folder_name,
                            'path': folder_path
                        })

        # 记录响应日志
        current_app.logger.info(f"获取顶级文件夹列表成功: client_ip={client_ip}, user_id={user_id}, count={len(folders)}")

        return jsonify({'folders': folders})
    except Exception as e:
        client_ip = request.remote_addr
        user_id = g.user.id if hasattr(g, 'user') else 'unknown'
        current_app.logger.error(f"获取顶级文件夹列表时出错: client_ip={client_ip}, user_id={user_id}, error={str(e)}")
        return jsonify({'error': f'获取顶级文件夹列表时出错: {str(e)}'}), 500

@log_download.route('/logs/objects', methods=['GET'])
@log_route
def get_objects():
    """
    获取指定路径下的文件夹和文件列表

    Query Parameters:
        path (str): 路径，例如 'folder1/' 或 'folder1/folder2/'

    Returns:
        JSON: 文件夹和文件列表
    """
    try:
        # 获取查询参数
        path = request.args.get('path', '')
        search = request.args.get('search', '')  # 添加搜索参数

        # 记录请求日志
        client_ip = request.remote_addr
        user_id = g.user.id if hasattr(g, 'user') else 'unknown'
        current_app.logger.info(f"获取对象列表: client_ip={client_ip}, user_id={user_id}, path={path}, search={search}")

        # 获取腾讯云API密钥
        secret_id, secret_key = get_tencent_credentials()
        if not secret_id or not secret_key:
            current_app.logger.error(f"获取对象列表失败: client_ip={client_ip}, user_id={user_id}, error=未配置腾讯云API密钥")
            return jsonify({'error': '未配置腾讯云API密钥'}), 400

        # 导入腾讯云SDK
        try:
            from qcloud_cos import CosConfig
            from qcloud_cos import CosS3Client
        except ImportError:
            current_app.logger.error(f"获取对象列表失败: client_ip={client_ip}, user_id={user_id}, error=未安装腾讯云对象存储SDK")
            return jsonify({'error': '未安装腾讯云对象存储SDK，请执行 pip install -U cos-python-sdk-v5'}), 500

        # 创建配置
        config = CosConfig(Region=REGION, SecretId=secret_id, SecretKey=secret_key)

        # 创建客户端
        client = CosS3Client(config)

        # 确保路径以斜杠结尾
        if path and not path.endswith('/'):
            path = path + '/'

        # 构建搜索前缀
        search_prefix = path
        if search:
            # 如果有搜索关键字，将其添加到路径后面
            search_prefix = path + search

        # 记录调试信息
        current_app.logger.debug(f"查询参数: path={path}, search={search}, search_prefix={search_prefix}")

        # 判断是否为搜索模式
        is_search_mode = search != ''

        if is_search_mode:
            # 搜索模式：不使用分隔符，获取所有匹配前缀的对象
            response = client.list_objects(
                Bucket=BUCKET_NAME,
                Prefix=search_prefix,
                MaxKeys=1000  # 增加返回的最大数量
            )
        else:
            # 标准模式：使用分隔符获取文件夹和文件
            response = client.list_objects(
                Bucket=BUCKET_NAME,
                Delimiter='/',  # 使用分隔符获取文件夹
                Prefix=path
            )

        # 提取文件夹和文件列表
        folders = []
        files = []

        # 处理文件夹
        if is_search_mode:
            # 搜索模式：通过分析对象路径提取文件夹
            folder_set = set()  # 用于去重

            if 'Contents' in response:
                for item in response['Contents']:
                    key = item.get('Key', '')

                    # 如果是当前路径下的文件，跳过
                    if not '/' in key[len(path):]:
                        continue

                    # 提取当前路径下的第一级文件夹
                    relative_path = key[len(path):]
                    parts = relative_path.split('/')
                    if len(parts) > 0:
                        folder_name = parts[0]
                        folder_path = path + folder_name + '/'

                        # 去重
                        if folder_path not in folder_set:
                            folder_set.add(folder_path)
                            folders.append({
                                'name': folder_name,
                                'path': folder_path,
                                'type': 'folder'
                            })
        else:
            # 标准模式：直接使用CommonPrefixes
            if 'CommonPrefixes' in response:
                for folder in response['CommonPrefixes']:
                    folder_path = folder.get('Prefix', '')
                    # 提取文件夹名称（去掉路径和末尾的斜杠）
                    folder_name = folder_path
                    if path and folder_name.startswith(path):
                        folder_name = folder_name[len(path):]
                    folder_name = folder_name.rstrip('/')

                    if folder_name:  # 确保文件夹名不为空
                        folders.append({
                            'name': folder_name,
                            'path': folder_path,
                            'type': 'folder'
                        })

        # 处理文件
        if 'Contents' in response:
            for file in response['Contents']:
                file_key = file.get('Key', '')
                # 跳过与路径相同的对象（通常是表示文件夹的空对象）
                if file_key == path:
                    continue

                # 提取文件名（去掉路径）
                file_name = file_key
                if path and file_name.startswith(path):
                    file_name = file_name[len(path):]

                if is_search_mode:
                    # 搜索模式：只显示当前路径下的文件，不包括子文件夹中的文件
                    if '/' in file_name:
                        # 这是子文件夹中的文件，跳过
                        continue
                else:
                    # 标准模式：跳过包含斜杠的对象（这些是子文件夹中的文件）
                    if '/' in file_name:
                        continue

                if file_name:  # 确保文件名不为空
                    files.append({
                        'name': file_name,
                        'path': file_key,
                        'size': file.get('Size', 0),
                        'last_modified': file.get('LastModified', ''),
                        'type': 'file'
                    })

        # 记录响应日志
        current_app.logger.info(f"获取对象列表成功: client_ip={client_ip}, user_id={user_id}, path={path}, folders={len(folders)}, files={len(files)}")

        return jsonify({
            'folders': folders,
            'files': files
        })
    except Exception as e:
        client_ip = request.remote_addr
        user_id = g.user.id if hasattr(g, 'user') else 'unknown'
        current_app.logger.error(f"获取对象列表时出错: client_ip={client_ip}, user_id={user_id}, path={path}, error={str(e)}")
        return jsonify({'error': f'获取对象列表时出错: {str(e)}'}), 500

@log_download.route('/logs/download', methods=['GET'])
@log_route
@log_operation('下载日志文件')
def download_file():
    """
    下载文件

    Query Parameters:
        path (str): 文件路径，例如 'folder1/folder2/file.log'

    Returns:
        File: 文件内容
    """
    try:
        # 获取查询参数
        path = request.args.get('path', '')

        # 记录请求日志
        client_ip = request.remote_addr
        user_id = g.user.id if hasattr(g, 'user') else 'unknown'
        current_app.logger.info(f"下载文件: client_ip={client_ip}, user_id={user_id}, path={path}")

        if not path:
            current_app.logger.warning(f"下载文件失败: client_ip={client_ip}, user_id={user_id}, error=缺少文件路径")
            return jsonify({'error': '缺少文件路径'}), 400

        # 获取腾讯云API密钥
        secret_id, secret_key = get_tencent_credentials()
        if not secret_id or not secret_key:
            current_app.logger.error(f"下载文件失败: client_ip={client_ip}, user_id={user_id}, error=未配置腾讯云API密钥")
            return jsonify({'error': '未配置腾讯云API密钥'}), 400

        # 导入腾讯云SDK
        try:
            from qcloud_cos import CosConfig
            from qcloud_cos import CosS3Client
        except ImportError:
            current_app.logger.error(f"下载文件失败: client_ip={client_ip}, user_id={user_id}, error=未安装腾讯云对象存储SDK")
            return jsonify({'error': '未安装腾讯云对象存储SDK，请执行 pip install -U cos-python-sdk-v5'}), 500

        # 创建配置
        config = CosConfig(Region=REGION, SecretId=secret_id, SecretKey=secret_key)

        # 创建客户端
        client = CosS3Client(config)

        # 获取文件对象
        response = client.get_object(
            Bucket=BUCKET_NAME,
            Key=path
        )

        # 获取文件内容
        file_content = response['Body'].get_raw_stream()

        # 获取文件名
        file_name = os.path.basename(path)

        # 创建响应
        def generate():
            for chunk in iter(lambda: file_content.read(8192), b''):
                yield chunk

        # 记录响应日志
        current_app.logger.info(f"下载文件成功: client_ip={client_ip}, user_id={user_id}, path={path}")

        response = Response(stream_with_context(generate()), mimetype='application/octet-stream')
        response.headers['Content-Disposition'] = f'attachment; filename={file_name}'
        return response
    except Exception as e:
        client_ip = request.remote_addr
        user_id = g.user.id if hasattr(g, 'user') else 'unknown'
        current_app.logger.error(f"下载文件时出错: client_ip={client_ip}, user_id={user_id}, path={path}, error={str(e)}")
        return jsonify({'error': f'下载文件时出错: {str(e)}'}), 500
