import os
from app import create_app
from scripts.init_modules_menus import init_modules_menus
from scripts.update_system_management_menu import update_system_management_menu
from config import config

# 获取环境配置
config_name = os.environ.get('FLASK_CONFIG') or 'default'
app = create_app(config[config_name])

if __name__ == '__main__':
    # 初始化模块和菜单数据
    with app.app_context():
        init_modules_menus()
        update_system_management_menu()

    # 打印所有路由
    print("Registered routes:")
    for rule in app.url_map.iter_rules():
        print(f"{rule.endpoint}: {rule}")

    app.run(host='0.0.0.0', port=5000, debug=True)
