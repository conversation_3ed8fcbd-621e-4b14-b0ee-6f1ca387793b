# ICMS2 文档

欢迎查阅ICMS2（集成内容管理系统2）文档。本文档提供了关于ICMS2系统的全面信息，包括其架构、安装、配置和使用方法。

## 目录

- [概述](#概述)
- [安装指南](安装指南.md)
- [系统架构](系统架构.md)
- [API参考](API参考.md)
- [数据模型](数据模型.md)
- [认证系统](认证系统.md)
- [配置指南](配置指南.md)

### 模块API文档

- [认证模块](auth.md)
- [角色管理](role_api.md)
- [模块菜单](module_menu_api.md)
- [服务器资源](server_resource_api.md)
- [负载均衡器](load_balancer_api.md)
- [日志下载](log_download_api.md)
- [应用系统](application_api.md)
- [应用环境](application_environment_api.md)

## 概述

ICMS2是一个基于Flask的Web应用程序，提供了全面的内容管理系统，具有基于角色的访问控制。它支持LDAP和本地认证，允许在各种环境中灵活管理用户。系统提供了丰富的功能模块，包括用户认证、角色管理、服务器资源管理、负载均衡器管理、应用系统管理、应用环境管理以及日志下载等功能。

### 主要特点

- **双重认证**：支持LDAP和本地用户认证
- **基于角色的访问控制**：通过角色进行细粒度的权限管理
- **模块化系统**：模块化架构便于扩展
- **RESTful API**：结构良好的API便于前端集成
- **可配置性**：多种环境配置（开发、测试、生产）
- **云服务集成**：与腾讯云API集成，支持服务器和负载均衡器管理
- **应用环境管理**：支持应用系统和环境的完整生命周期管理
- **资源变更追踪**：记录所有资源变更历史

### 技术栈

- **后端**：Python与Flask框架
- **数据库**：MySQL与SQLAlchemy ORM
- **认证**：JWT（JSON Web Tokens）用于无状态认证
- **目录服务**：LDAP集成用于企业环境
- **云服务**：腾讯云API集成
- **对象存储**：腾讯云对象存储用于日志管理

### 系统模块

- **认证模块**：处理用户认证和授权
- **角色管理**：管理用户角色和权限
- **模块菜单**：管理系统模块和菜单
- **服务器资源**：管理服务器资源
- **负载均衡器**：管理负载均衡器资源
- **应用系统**：管理应用系统信息
- **应用环境**：管理应用环境和资源分配
- **日志下载**：提供日志浏览和下载功能

## 快速开始

要快速开始，请参阅[安装指南](安装指南.md)获取设置说明。

## 许可证

[在此处包含许可证信息]
