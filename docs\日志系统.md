# 日志系统

本文档描述了ICMS2系统的日志记录功能，包括配置、使用方法和最佳实践。

## 目录

- [概述](#概述)
- [日志配置](#日志配置)
- [日志文件命名](#日志文件命名)
- [日志格式](#日志格式)
- [日志级别](#日志级别)
- [日志工具](#日志工具)
- [最佳实践](#最佳实践)

## 概述

ICMS2系统使用Python标准库的`logging`模块进行日志记录，并进行了定制化扩展，以满足系统的特定需求。主要特点包括：

1. **按天分割日志文件**：每天生成一个新的日志文件（格式如：`icms2-2025-05-23.log`），便于管理和查询
2. **丰富的上下文信息**：记录请求IP、用户ID、路径等信息，方便排查问题
3. **统一的日志格式**：所有模块使用统一的日志格式，便于解析和分析
4. **多级别日志**：支持DEBUG、INFO、WARNING、ERROR、CRITICAL多个日志级别
5. **装饰器支持**：提供装饰器简化日志记录

## 日志配置

日志配置在`config.py`文件中定义，主要配置项包括：

```python
# 日志配置
LOG_DIR = os.environ.get('LOG_DIR') or 'logs'  # 日志目录
LOG_FILE = 'icms2.log'  # 日志文件名
LOG_LEVEL = os.environ.get('LOG_LEVEL') or 'INFO'  # 日志级别
LOG_BACKUP_COUNT = 30  # 保留30天的日志
LOG_TO_CONSOLE = True  # 是否输出到控制台
# 日志格式
LOG_FORMAT = '%(asctime)s [%(levelname)s] [%(remote_addr)s] [用户:%(user_id)s] %(message)s [in %(pathname)s:%(lineno)d]'
```

可以通过环境变量覆盖这些配置：

```bash
# Windows
set LOG_LEVEL=DEBUG
set LOG_DIR=D:\logs

# Linux/macOS
export LOG_LEVEL=DEBUG
export LOG_DIR=/var/log/icms2
```

## 日志文件命名

系统使用日期作为日志文件名的一部分，格式为：`icms2-YYYY-MM-DD.log`。例如：

```
icms2-2025-05-23.log  # 2025年5月23日的日志文件
```

这种命名方式有以下优点：
1. 直观明了，可以直接从文件名看出日期
2. 避免了日志轮转时的文件锁定问题
3. 便于按日期查找和管理日志

系统会自动保留最近30天（可配置）的日志文件，过期的日志文件会被自动删除。

## 日志格式

系统使用的日志格式包含以下字段：

- **时间戳**：日志记录的时间，格式为`YYYY-MM-DD HH:MM:SS,SSS`
- **日志级别**：DEBUG、INFO、WARNING、ERROR或CRITICAL
- **客户端IP**：发起请求的客户端IP地址
- **用户ID**：当前登录用户的ID
- **消息内容**：具体的日志消息
- **文件路径**：生成日志的源代码文件路径
- **行号**：生成日志的源代码行号

示例：
```
2023-07-01 12:34:56,789 [INFO] [*************] [用户:123] 应用启动成功，环境：development [in app/__init__.py:39]
```

## 日志级别

系统支持以下日志级别，按严重程度从低到高排序：

1. **DEBUG**：详细的调试信息，通常仅在开发环境使用
2. **INFO**：一般信息，记录系统正常运行的状态变化
3. **WARNING**：警告信息，表示可能出现的问题
4. **ERROR**：错误信息，表示发生了错误但不影响系统继续运行
5. **CRITICAL**：严重错误，可能导致系统无法继续运行

在生产环境中，建议将日志级别设置为INFO或WARNING，以减少日志量。

## 日志工具

系统提供了以下日志工具：

### 1. Flask应用日志记录器

在路由函数中，可以直接使用`current_app.logger`记录日志：

```python
from flask import current_app

# 记录信息
current_app.logger.info("这是一条信息日志")

# 记录警告
current_app.logger.warning("这是一条警告日志")

# 记录错误
current_app.logger.error("这是一条错误日志")
```

### 2. 日志装饰器

系统提供了两个装饰器，简化日志记录：

#### 路由日志装饰器

记录路由函数的执行情况，包括请求开始、结束和异常：

```python
from app.utils.decorators import log_route

@app.route('/example')
@log_route
def example():
    return "Hello World"
```

#### 操作日志装饰器

记录业务操作，包括操作类型、数据和结果：

```python
from app.utils.decorators import log_operation

@app.route('/users', methods=['POST'])
@log_operation('创建用户')
def create_user():
    # 创建用户的代码
    return jsonify({"message": "用户创建成功"})
```

## 最佳实践

### 1. 选择合适的日志级别

- **DEBUG**：用于详细的调试信息，如变量值、函数调用等
- **INFO**：用于记录正常的系统状态变化，如启动、关闭、用户登录等
- **WARNING**：用于记录可能的问题，如参数验证失败、重试操作等
- **ERROR**：用于记录错误，如数据库连接失败、API调用失败等
- **CRITICAL**：用于记录严重错误，如系统无法启动、关键服务不可用等

### 2. 记录有用的上下文信息

日志应包含足够的上下文信息，以便于排查问题：

```python
# 不好的例子
logger.error("数据库连接失败")

# 好的例子
logger.error(f"数据库连接失败: host={db_host}, user={db_user}, error={str(e)}")
```

### 3. 使用结构化日志

对于复杂的数据，使用结构化格式记录：

```python
import json

# 记录结构化数据
user_data = {"id": 123, "name": "张三", "role": "admin"}
logger.info(f"用户数据: {json.dumps(user_data)}")
```

### 4. 避免敏感信息

不要在日志中记录敏感信息，如密码、令牌等：

```python
# 不好的例子
logger.info(f"用户登录: username={username}, password={password}")

# 好的例子
logger.info(f"用户登录: username={username}")
```

### 5. 使用异常处理记录错误

在异常处理中记录详细的错误信息：

```python
try:
    # 可能抛出异常的代码
    result = some_function()
except Exception as e:
    logger.error(f"函数调用失败: function=some_function, error={str(e)}", exc_info=True)
    raise  # 重新抛出异常或返回错误响应
```

### 6. 定期检查和分析日志

定期检查日志文件，特别是ERROR和CRITICAL级别的日志，及时发现和解决问题。
