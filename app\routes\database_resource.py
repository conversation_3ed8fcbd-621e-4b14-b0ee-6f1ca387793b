"""
数据库资源管理模块
"""
from flask import Blueprint, request, jsonify, current_app, g
from .. import db, beijing_tz
from ..models import (
    DatabaseResource, ResourceChangeHistory, OperationType,
    ApplicationEnvDatabase
)
from ..utils.decorators import log_route, log_operation
from ..utils.user_utils import get_current_user_id
from datetime import datetime
import json
from sqlalchemy import or_

# 创建蓝图
database_resource = Blueprint('database_resource', __name__)

@database_resource.route('/databases', methods=['GET'])
@log_route
def get_databases():
    """
    查询数据库资源列表，支持按名称、IP、类型进行模糊查询

    Query Parameters:
        db_name (str, optional): 数据库名称关键字
        db_ip (str, optional): 数据库IP关键字
        db_type (str, optional): 数据库类型关键字
        page (int, optional): 页码，默认为1
        per_page (int, optional): 每页记录数，默认为20

    Returns:
        JSON: 数据库资源列表和分页信息
    """
    # 获取查询参数
    db_name = request.args.get('db_name', '')
    db_ip = request.args.get('db_ip', '')
    db_type = request.args.get('db_type', '')
    page = int(request.args.get('page', 1))
    per_page = int(request.args.get('per_page', 20))

    # 构建查询
    query = DatabaseResource.query

    # 应用过滤条件
    if db_name:
        query = query.filter(DatabaseResource.db_name.like(f'%{db_name}%'))
    if db_ip:
        query = query.filter(DatabaseResource.db_ip.like(f'%{db_ip}%'))
    if db_type:
        query = query.filter(DatabaseResource.db_type.like(f'%{db_type}%'))

    # 只查询在线状态的数据库资源
    query = query.filter(DatabaseResource.db_status == 'online')

    # 分页
    total = query.count()
    pagination = query.order_by(DatabaseResource.creat_time.desc()).paginate(page=page, per_page=per_page)

    # 构建响应
    result = {
        'total': total,
        'page': page,
        'per_page': per_page,
        'pages': (total + per_page - 1) // per_page,
        'databases': [db_resource.to_dict() for db_resource in pagination.items]
    }

    return jsonify(result)

@database_resource.route('/databases', methods=['POST'])
@log_route
@log_operation('创建数据库资源')
def create_database():
    """
    创建数据库资源

    Request Body:
        db_name (str): 数据库名称
        db_ip (str): 数据库IP
        db_type (str): 数据库类型
        purpose (str): 数据库用途
        remark (str, optional): 备注

    Returns:
        JSON: 创建结果
    """
    data = request.get_json()

    # 验证必填字段
    required_fields = ['db_name', 'db_ip', 'db_type', 'purpose']
    for field in required_fields:
        if field not in data or not data[field]:
            return jsonify({'error': f'缺少必填字段: {field}'}), 400

    # 创建数据库资源记录
    db_resource = DatabaseResource(
        db_name=data['db_name'],
        db_ip=data['db_ip'],
        db_type=data['db_type'],
        purpose=data['purpose'],
        remark=data.get('remark', ''),
        db_status='online',
        creat_time=datetime.now(beijing_tz).replace(tzinfo=None)
    )

    db.session.add(db_resource)
    db.session.flush()  # 获取ID

    # 记录变更历史
    history = ResourceChangeHistory(
        user_id=get_current_user_id(),  # 使用工具函数获取当前用户ID
        resource_type='database',
        resource_id=db_resource.id,
        operation_type=OperationType.CREATE.value,
        after_data=json.dumps({
            'db_name': db_resource.db_name,
            'db_ip': db_resource.db_ip,
            'db_type': db_resource.db_type,
            'purpose': db_resource.purpose,
            'remark': db_resource.remark,
            'db_status': db_resource.db_status
        })
    )

    db.session.add(history)
    db.session.commit()

    return jsonify({
        'message': '数据库资源创建成功',
        'id': db_resource.id
    }), 201

@database_resource.route('/databases/<int:db_id>', methods=['PUT'])
@log_route
@log_operation('更新数据库资源')
def update_database(db_id):
    """
    更新数据库资源

    Path Parameters:
        db_id (int): 数据库资源ID

    Request Body:
        db_name (str, optional): 数据库名称
        db_ip (str, optional): 数据库IP
        db_type (str, optional): 数据库类型
        purpose (str, optional): 数据库用途
        remark (str, optional): 备注

    Returns:
        JSON: 更新结果
    """
    # 查找数据库资源
    db_resource = DatabaseResource.query.get_or_404(db_id)

    # 检查数据库资源是否已下线
    if db_resource.db_status == 'offline':
        return jsonify({'error': '已下线的数据库资源不能更新'}), 400

    data = request.get_json()

    # 记录变更前数据
    before_data = {
        'db_name': db_resource.db_name,
        'db_ip': db_resource.db_ip,
        'db_type': db_resource.db_type,
        'purpose': db_resource.purpose,
        'remark': db_resource.remark
    }

    # 更新字段
    changes_made = False

    if 'db_name' in data and data['db_name'] != db_resource.db_name:
        db_resource.db_name = data['db_name']
        changes_made = True

    if 'db_ip' in data and data['db_ip'] != db_resource.db_ip:
        db_resource.db_ip = data['db_ip']
        changes_made = True

    if 'db_type' in data and data['db_type'] != db_resource.db_type:
        db_resource.db_type = data['db_type']
        changes_made = True

    if 'purpose' in data and data['purpose'] != db_resource.purpose:
        db_resource.purpose = data['purpose']
        changes_made = True

    if 'remark' in data and data['remark'] != db_resource.remark:
        db_resource.remark = data['remark']
        changes_made = True

    if not changes_made:
        return jsonify({'message': '没有变更需要保存'}), 200

    # 记录变更历史
    history = ResourceChangeHistory(
        user_id=get_current_user_id(),  # 使用工具函数获取当前用户ID
        resource_type='database',
        resource_id=db_resource.id,
        operation_type=OperationType.UPDATE.value,
        before_data=json.dumps(before_data),
        after_data=json.dumps({
            'db_name': db_resource.db_name,
            'db_ip': db_resource.db_ip,
            'db_type': db_resource.db_type,
            'purpose': db_resource.purpose,
            'remark': db_resource.remark
        })
    )

    db.session.add(history)
    db.session.commit()

    return jsonify({'message': '数据库资源更新成功'})

@database_resource.route('/databases/<int:db_id>/offline', methods=['PUT'])
@log_route
@log_operation('下线数据库资源')
def offline_database(db_id):
    """
    下线数据库资源

    Path Parameters:
        db_id (int): 数据库资源ID

    Returns:
        JSON: 下线结果
    """
    # 检查请求体是否为JSON格式（如果有请求体）
    if request.data and request.content_type and 'application/json' in request.content_type:
        try:
            _ = request.get_json()
        except Exception as e:
            current_app.logger.error(f"JSON解析错误: {str(e)}")
            return jsonify({'error': f'请求体JSON格式错误: {str(e)}'}), 400

    # 查找数据库资源
    db_resource = DatabaseResource.query.get_or_404(db_id)

    # 检查数据库资源是否已下线
    if db_resource.db_status == 'offline':
        return jsonify({'message': '数据库资源已经是下线状态'}), 400

    try:
        # 记录变更前数据
        before_data = {
            'db_name': db_resource.db_name,
            'db_ip': db_resource.db_ip,
            'db_status': db_resource.db_status,
            'offline_time': db_resource.offline_time.strftime('%Y-%m-%d %H:%M:%S') if db_resource.offline_time else None
        }

        # 更新数据库资源状态为下线
        db_resource.db_status = 'offline'
        db_resource.offline_time = datetime.now(beijing_tz).replace(tzinfo=None)

        # 解除与应用环境的绑定关系
        env_dbs = ApplicationEnvDatabase.query.filter_by(db_id=db_resource.id).all()
        for env_db in env_dbs:
            db.session.delete(env_db)

        # 记录变更历史
        history = ResourceChangeHistory(
            user_id=g.user.id if hasattr(g, 'user') else 1,
            resource_type='database',
            resource_id=db_resource.id,
            operation_type=OperationType.UPDATE.value,
            before_data=json.dumps(before_data),
            after_data=json.dumps({
                'db_status': db_resource.db_status,
                'offline_time': db_resource.offline_time.strftime('%Y-%m-%d %H:%M:%S')
            })
        )
        db.session.add(history)

        db.session.commit()

        return jsonify({'message': '数据库资源已成功下线'})

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"下线数据库资源时出错: {str(e)}")
        return jsonify({'error': f'下线数据库资源时出错: {str(e)}'}), 500
