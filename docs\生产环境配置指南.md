# 生产环境配置指南

本文档详细说明了如何配置ICMS2应用的生产环境参数和数据库。

## 环境变量配置

ICMS2应用使用环境变量来配置生产环境。在Docker部署中，这些环境变量通过`.env`文件和`docker-compose.yml`文件进行管理。

### 核心环境变量

以下是生产环境中需要配置的核心环境变量：

| 环境变量 | 描述 | 示例值 |
|---------|------|--------|
| `FLASK_CONFIG` | 应用环境配置 | `production` |
| `SECRET_KEY` | 用于加密会话和令牌的密钥 | `complex-random-string` |
| `DATABASE_URL` | 数据库连接URL | `mysql+pymysql://用户名:密码@主机/数据库名` |
| `LOG_DIR` | 日志目录 | `/app/logs` |
| `LOG_LEVEL` | 日志级别 | `INFO` |

### 数据库环境变量

如果使用Docker Compose部署MySQL数据库，需要配置以下环境变量：

| 环境变量 | 描述 | 示例值 |
|---------|------|--------|
| `MYSQL_ROOT_PASSWORD` | MySQL root用户密码 | `secure-root-password` |
| `MYSQL_DATABASE` | 要创建的数据库名称 | `icms2` |
| `MYSQL_USER` | 应用使用的数据库用户 | `icmsopr` |
| `MYSQL_PASSWORD` | 数据库用户密码 | `secure-db-password` |

## 生产环境数据库配置

### 使用Docker容器数据库

如果使用Docker Compose部署的MySQL数据库，系统会自动完成以下配置：

1. 创建数据库和用户
2. 设置适当的字符集和排序规则
3. 授予必要的权限

这些配置在`docker/mysql/init/01-init.sql`文件中定义。

### 使用外部数据库

如果需要使用外部已有的MySQL数据库，请按照以下步骤配置：

1. 创建数据库和用户：

```sql
CREATE DATABASE icms2 DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER 'icmsopr'@'%' IDENTIFIED BY '安全密码';
GRANT ALL PRIVILEGES ON icms2.* TO 'icmsopr'@'%';
FLUSH PRIVILEGES;
```

2. 修改应用的数据库连接URL：

在`.env`文件中设置：
```
DATABASE_URL=mysql+pymysql://icmsopr:安全密码@外部数据库主机/icms2
```

或者在`docker-compose.yml`中直接设置：
```yaml
app:
  environment:
    - DATABASE_URL=mysql+pymysql://icmsopr:安全密码@外部数据库主机/icms2
```

### 数据库性能优化

在生产环境中，建议对MySQL进行以下优化：

1. 增加连接池大小
2. 优化InnoDB缓冲池大小
3. 启用查询缓存
4. 配置适当的事务隔离级别

可以通过在`docker-compose.yml`中添加自定义MySQL配置文件来实现：

```yaml
db:
  volumes:
    - ./docker/mysql/my.cnf:/etc/mysql/conf.d/custom.cnf
```

## 应用配置

### 生产环境类

ICMS2使用`ProductionConfig`类来配置生产环境。该类在`config.py`文件中定义：

```python
class ProductionConfig(Config):
    # 生产环境可以使用更强的密钥
    SECRET_KEY = os.environ.get('SECRET_KEY') or os.urandom(24)

    # 生产环境日志级别设置为INFO
    LOG_LEVEL = 'INFO'

    @staticmethod
    def init_app(app):
        # 调用父类方法
        Config.init_app(app)

        # 生产环境特定配置
        app.logger.info("生产环境配置已加载")
```

### 自定义生产配置

如果需要自定义生产环境配置，可以通过以下方式：

1. 修改`config.py`文件中的`ProductionConfig`类
2. 通过环境变量覆盖默认配置
3. 在Docker容器中挂载自定义配置文件

## 日志配置

生产环境中的日志配置非常重要，ICMS2使用以下日志配置：

1. 日志存储在`LOG_DIR`指定的目录中
2. 日志文件名为`icms2.log`
3. 日志级别由`LOG_LEVEL`环境变量控制
4. 日志保留30天（`LOG_BACKUP_COUNT=30`）

在Docker部署中，日志目录通过卷挂载到主机：

```yaml
volumes:
  - ./logs:/app/logs
```

这样可以在容器外部查看和管理日志文件。

## 安全配置

在生产环境中，安全配置至关重要：

1. **密钥管理**：使用强随机密钥，定期轮换
2. **数据库安全**：限制数据库用户权限，使用强密码
3. **网络安全**：使用反向代理（如Nginx）提供HTTPS支持
4. **容器安全**：限制容器权限，定期更新基础镜像

### 配置HTTPS

建议在生产环境中使用HTTPS。可以通过Nginx反向代理实现：

```nginx
server {
    listen 443 ssl;
    server_name your-domain.com;

    ssl_certificate /path/to/cert.pem;
    ssl_certificate_key /path/to/key.pem;

    location / {
        proxy_pass http://localhost:5000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

## 性能优化

生产环境中的性能优化包括：

1. **Gunicorn配置**：调整工作进程数和超时时间
2. **数据库连接池**：优化数据库连接管理
3. **静态资源缓存**：使用CDN或Nginx缓存静态资源
4. **应用缓存**：实现适当的缓存机制

## 监控和维护

生产环境需要定期监控和维护：

1. **日志监控**：定期检查应用日志
2. **性能监控**：监控CPU、内存和磁盘使用情况
3. **数据库备份**：定期备份数据库
4. **系统更新**：定期更新应用和依赖项

## 故障排除

常见生产环境问题的排查方法：

1. **应用无法启动**：检查日志文件，验证环境变量
2. **数据库连接失败**：检查数据库配置和网络连接
3. **性能问题**：分析日志，检查数据库查询性能
4. **内存泄漏**：监控容器内存使用，定期重启服务
