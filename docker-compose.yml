version: '3'

services:
  # MySQL数据库服务
  db:
    image: mysql:5.7
    container_name: icms2-mysql
    restart: always
    environment:
      MYSQL_ROOT_PASSWORD: ${MYSQL_ROOT_PASSWORD:-rootpassword}
      MYSQL_DATABASE: ${MYSQL_DATABASE:-icms2}
      MYSQL_USER: ${MYSQL_USER:-icmsopr}
      MYSQL_PASSWORD: ${MYSQL_PASSWORD:-Icopr1234}
    volumes:
      - mysql_data:/var/lib/mysql
      - ./docker/mysql/init:/docker-entrypoint-initdb.d
    ports:
      - "3306:3306"
    command: --character-set-server=utf8mb4 --collation-server=utf8mb4_unicode_ci
    networks:
      - icms2-network

  # ICMS2应用服务
  app:
    build: .
    container_name: icms2-app
    restart: always
    depends_on:
      - db
    environment:
      - FLASK_CONFIG=production
      - SECRET_KEY=${SECRET_KEY:-your-production-secret-key}
      - DATABASE_URL=mysql+pymysql://${MYSQL_USER:-icmsopr}:${MYSQL_PASSWORD:-Icopr1234}@db/${MYSQL_DATABASE:-icms2}
      - LOG_DIR=/app/logs
      - LOG_LEVEL=INFO
    volumes:
      - ./logs:/app/logs
    ports:
      - "5000:5000"
    networks:
      - icms2-network
    command: >
      sh -c "
        # 等待数据库准备就绪
        echo 'Waiting for MySQL to be ready...'
        while ! nc -z db 3306; do
          sleep 1
        done
        echo 'MySQL is ready!'
        
        # 初始化数据库表
        python -c 'from app import create_app, db; from config import config; app = create_app(config["production"]); app.app_context().push(); db.create_all()'
        
        # 初始化模块和菜单数据
        python -c 'from scripts.init_modules_menus import init_modules_menus; init_modules_menus()'
        
        # 启动应用
        gunicorn --bind 0.0.0.0:5000 --workers 4 --timeout 120 run:app
      "

networks:
  icms2-network:
    driver: bridge

volumes:
  mysql_data:
    driver: local
