"""
腾讯云CVM操作工具模块
"""
import json
import time
from flask import current_app
from .cloud_api import get_tencent_credentials

# 注意：实际使用时需要安装腾讯云SDK
# pip install tencentcloud-sdk-python
try:
    from tencentcloud.common import credential
    from tencentcloud.common.profile.client_profile import ClientProfile
    from tencentcloud.common.profile.http_profile import HttpProfile
    from tencentcloud.cvm.v20170312 import cvm_client, models
    TENCENT_SDK_AVAILABLE = True
except ImportError:
    TENCENT_SDK_AVAILABLE = False
    # 注意：在模块导入时current_app可能不可用，所以这里不记录日志

class TencentCVMClient:
    """腾讯云CVM客户端"""

    def __init__(self, region='ap-shanghai-fsi'):
        self.region = region
        self.client = None
        self._init_client()

    def _init_client(self):
        """初始化腾讯云客户端"""
        if not TENCENT_SDK_AVAILABLE:
            if current_app:
                current_app.logger.warning("腾讯云SDK不可用，使用模拟模式")
            return

        try:
            secret_id, secret_key = get_tencent_credentials()
            if not secret_id or not secret_key:
                if current_app:
                    current_app.logger.error("无法获取腾讯云API密钥")
                return

            # 实例化一个认证对象
            cred = credential.Credential(secret_id, secret_key)

            # 实例化一个http选项
            httpProfile = HttpProfile()
            httpProfile.endpoint = "cvm.tencentcloudapi.com"

            # 实例化一个client选项
            clientProfile = ClientProfile()
            clientProfile.httpProfile = httpProfile

            # 实例化要请求产品的client对象
            self.client = cvm_client.CvmClient(cred, self.region, clientProfile)

            if current_app:
                current_app.logger.info(f"腾讯云CVM客户端初始化成功，区域: {self.region}")

        except Exception as e:
            if current_app:
                current_app.logger.error(f"初始化腾讯云CVM客户端失败: {str(e)}")
            self.client = None

    def create_instance(self, params):
        """
        创建CVM实例

        Args:
            params (dict): 创建实例的参数

        Returns:
            dict: 创建结果
        """
        if not TENCENT_SDK_AVAILABLE or not self.client:
            # 模拟模式
            return self._mock_create_instance(params)

        try:
            # 实例化一个请求对象
            req = models.RunInstancesRequest()
            req.from_json_string(json.dumps(params))

            # 返回的resp是一个RunInstancesResponse的实例
            resp = self.client.RunInstances(req)

            # 输出json格式的字符串回包
            result = json.loads(resp.to_json_string())
            current_app.logger.info(f"CVM创建请求成功: {result}")

            return {
                'success': True,
                'data': result,
                'instance_ids': result.get('InstanceIdSet', []),
                'request_id': result.get('RequestId')
            }

        except Exception as e:
            current_app.logger.error(f"创建CVM实例失败: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'data': None
            }

    def describe_instances(self, instance_ids):
        """
        查询实例详细信息

        Args:
            instance_ids (list): 实例ID列表

        Returns:
            dict: 实例信息
        """
        if not TENCENT_SDK_AVAILABLE or not self.client:
            # 模拟模式
            return self._mock_describe_instances(instance_ids)

        try:
            # 实例化一个请求对象
            req = models.DescribeInstancesRequest()
            params = {
                "InstanceIds": instance_ids
            }
            req.from_json_string(json.dumps(params))

            # 返回的resp是一个DescribeInstancesResponse的实例
            resp = self.client.DescribeInstances(req)

            # 输出json格式的字符串回包
            result = json.loads(resp.to_json_string())
            current_app.logger.info(f"查询实例信息成功: {len(result.get('InstanceSet', []))}个实例")

            return {
                'success': True,
                'data': result,
                'instances': result.get('InstanceSet', [])
            }

        except Exception as e:
            current_app.logger.error(f"查询实例信息失败: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'data': None
            }

    def wait_for_instance_running(self, instance_id, max_wait_time=300, check_interval=10):
        """
        等待实例进入运行状态

        Args:
            instance_id (str): 实例ID
            max_wait_time (int): 最大等待时间（秒）
            check_interval (int): 检查间隔（秒）

        Returns:
            dict: 等待结果
        """
        start_time = time.time()

        while time.time() - start_time < max_wait_time:
            result = self.describe_instances([instance_id])

            if not result['success']:
                return {
                    'success': False,
                    'error': f"查询实例状态失败: {result['error']}"
                }

            instances = result['instances']
            if not instances:
                return {
                    'success': False,
                    'error': f"实例 {instance_id} 不存在"
                }

            instance = instances[0]
            state = instance.get('InstanceState', '')

            current_app.logger.info(f"实例 {instance_id} 当前状态: {state}")

            if state == 'RUNNING':
                return {
                    'success': True,
                    'instance': instance,
                    'wait_time': time.time() - start_time
                }
            elif state in ['LAUNCH_FAILED', 'SHUTDOWN']:
                return {
                    'success': False,
                    'error': f"实例启动失败，状态: {state}"
                }

            time.sleep(check_interval)

        return {
            'success': False,
            'error': f"等待实例启动超时（{max_wait_time}秒）"
        }

    def _mock_create_instance(self, params):
        """模拟创建实例"""
        hostname = params.get('InstanceName', 'mock-instance')
        instance_id = f"ins-{hostname.lower()}"

        current_app.logger.info(f"模拟创建CVM实例: {instance_id}")

        return {
            'success': True,
            'data': {
                'InstanceIdSet': [instance_id],
                'RequestId': f"req-{int(time.time())}"
            },
            'instance_ids': [instance_id],
            'request_id': f"req-{int(time.time())}"
        }

    def _mock_describe_instances(self, instance_ids):
        """模拟查询实例信息"""
        instances = []

        for instance_id in instance_ids:
            hostname = instance_id.replace('ins-', '')
            instances.append({
                'InstanceId': instance_id,
                'InstanceName': hostname,
                'InstanceState': 'RUNNING',
                'PrivateIpAddresses': ['**********'],
                'PublicIpAddresses': [],
                'OsName': 'CentOS 7.6',
                'CPU': 2,
                'Memory': 4,
                'InstanceType': 'S3.MEDIUM4',
                'Placement': {
                    'Zone': 'ap-shanghai-fsi-1'
                },
                'VirtualPrivateCloud': {
                    'VpcId': 'vpc-test001',
                    'SubnetId': 'subnet-test001'
                },
                'CreatedTime': '2025-05-24T10:30:00Z'
            })

        current_app.logger.info(f"模拟查询实例信息: {len(instances)}个实例")

        return {
            'success': True,
            'data': {
                'InstanceSet': instances,
                'TotalCount': len(instances)
            },
            'instances': instances
        }

def create_cvm_client(region='ap-shanghai-fsi'):
    """
    创建腾讯云CVM客户端

    Args:
        region (str): 地域

    Returns:
        TencentCVMClient: CVM客户端实例
    """
    return TencentCVMClient(region)
