<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登录 - ICMS2</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background-color: #f5f5f5;
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
        }
        .login-container {
            background-color: #fff;
            border-radius: 5px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            padding: 30px;
            width: 350px;
        }
        .login-header {
            text-align: center;
            margin-bottom: 30px;
        }
        .login-header h1 {
            margin: 0;
            color: #333;
            font-size: 24px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
            color: #666;
        }
        .form-group input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
            font-size: 14px;
        }
        .form-group input:focus {
            border-color: #4a90e2;
            outline: none;
        }
        .login-button {
            background-color: #4a90e2;
            color: white;
            border: none;
            border-radius: 4px;
            padding: 12px;
            width: 100%;
            font-size: 16px;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        .login-button:hover {
            background-color: #357abf;
        }
        .error-message {
            color: #e74c3c;
            margin-top: 15px;
            text-align: center;
            display: none;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-header">
            <h1>ICMS2 系统登录</h1>
        </div>
        <form id="login-form">
            <div class="form-group">
                <label for="username">用户名</label>
                <input type="text" id="username" name="username" required>
            </div>
            <div class="form-group">
                <label for="password">密码</label>
                <input type="password" id="password" name="password" required>
            </div>
            <button type="submit" class="login-button">登录</button>
            <div id="error-message" class="error-message"></div>
        </form>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 检查是否已经登录
            const token = localStorage.getItem('token');
            if (token) {
                // 验证令牌
                fetch('/api/validate-token', {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`
                    },
                    credentials: 'include'
                })
                .then(response => response.json())
                .then(data => {
                    if (data.valid) {
                        // 如果令牌有效，重定向到首页
                        window.location.href = '/';
                    }
                })
                .catch(error => {
                    console.error('Error validating token:', error);
                });
            }

            // 处理登录表单提交
            const loginForm = document.getElementById('login-form');
            const errorMessage = document.getElementById('error-message');

            loginForm.addEventListener('submit', function(event) {
                event.preventDefault();
                
                const username = document.getElementById('username').value;
                const password = document.getElementById('password').value;
                
                // 发送登录请求
                fetch('/api/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        username: username,
                        password: password
                    }),
                    credentials: 'include'
                })
                .then(response => {
                    if (!response.ok) {
                        return response.json().then(data => {
                            throw new Error(data.error || '登录失败');
                        });
                    }
                    return response.json();
                })
                .then(data => {
                    // 登录成功，存储令牌和用户信息
                    localStorage.setItem('token', data.token);
                    localStorage.setItem('user', JSON.stringify({
                        id: data.id,
                        username: data.username,
                        display_name: data.display_name,
                        is_local_admin: data.is_local_admin
                    }));
                    localStorage.setItem('permissions', JSON.stringify(data.permissions));
                    
                    // 重定向到首页
                    window.location.href = '/';
                })
                .catch(error => {
                    // 显示错误信息
                    errorMessage.textContent = error.message;
                    errorMessage.style.display = 'block';
                });
            });
        });
    </script>
</body>
</html>
