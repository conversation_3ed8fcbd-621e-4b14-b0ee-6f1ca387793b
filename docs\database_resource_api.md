# 数据库资源管理 API 文档

本文档描述了数据库资源管理模块（`database_resource.py`）中的所有 API 接口。

## 目录

- [获取数据库资源列表](#获取数据库资源列表)
- [创建数据库资源](#创建数据库资源)
- [更新数据库资源](#更新数据库资源)
- [下线数据库资源](#下线数据库资源)

## 获取数据库资源列表

获取系统中的数据库资源列表，支持多种过滤条件和分页。

**请求方式**：GET

**URL**：`/api/databases`

**查询参数**：

| 参数名    | 类型    | 必填 | 描述                         |
|-----------|---------|------|------------------------------|
| db_name   | string  | 否   | 数据库名称关键字，用于模糊查询 |
| db_ip     | string  | 否   | 数据库IP关键字，用于模糊查询   |
| db_type   | string  | 否   | 数据库类型关键字，用于模糊查询 |
| page      | integer | 否   | 页码，默认为1                |
| per_page  | integer | 否   | 每页记录数，默认为20         |

**响应示例**：
```json
{
  "total": 25,
  "page": 1,
  "per_page": 20,
  "pages": 2,
  "databases": [
    {
      "id": 1,
      "db_name": "icms_prod",
      "db_ip": "*********",
      "db_type": "MySQL",
      "purpose": "生产环境主数据库",
      "remark": "IT资源管理系统生产数据库",
      "creat_time": "2023-05-15 10:30:45",
      "db_status": "online",
      "offline_time": null
    },
    {
      "id": 2,
      "db_name": "oa_prod",
      "db_ip": "*********",
      "db_type": "Oracle",
      "purpose": "生产环境主数据库",
      "remark": "办公自动化系统生产数据库",
      "creat_time": "2023-05-16 09:15:30",
      "db_status": "online",
      "offline_time": null
    }
    // 更多数据库记录...
  ]
}
```

**说明**：
- 返回数据库资源列表及分页信息
- 支持按数据库名称、IP地址和类型进行模糊查询
- 结果按创建时间降序排列
- 分页信息包括总记录数、当前页码、每页记录数和总页数
- 只返回状态为"online"的数据库资源
- 返回完整的数据库字段信息，包括db_status和offline_time

## 创建数据库资源

手动添加数据库资源信息。

**请求方式**：POST

**URL**：`/api/databases`

**请求体**：
```json
{
  "db_name": "crm_test",
  "db_ip": "*********",
  "db_type": "MySQL",
  "purpose": "测试环境数据库",
  "remark": "客户关系管理系统测试数据库"
}
```

**请求参数**：

| 参数名   | 类型   | 必填 | 描述         |
|----------|--------|------|--------------|
| db_name  | string | 是   | 数据库名称   |
| db_ip    | string | 是   | 数据库IP地址 |
| db_type  | string | 是   | 数据库类型   |
| purpose  | string | 是   | 数据库用途   |
| remark   | string | 否   | 备注         |

**响应示例**：
```json
{
  "message": "数据库资源创建成功",
  "id": 3
}
```

**错误响应**：
```json
{
  "error": "缺少必填字段: db_name"
}
```

**说明**：
- 创建数据库资源记录并返回创建结果
- 必填字段包括：db_name、db_ip、db_type、purpose
- 如果缺少必填字段，将返回400错误
- 创建成功后会自动记录变更历史
- 默认状态为"online"

## 更新数据库资源

更新现有数据库资源的信息。

**请求方式**：PUT

**URL**：`/api/databases/<db_id>`

**URL参数**：

| 参数名 | 类型    | 必填 | 描述         |
|--------|---------|------|--------------|
| db_id  | integer | 是   | 数据库资源ID |

**请求体**：
```json
{
  "db_name": "crm_test_updated",
  "db_ip": "*********",
  "db_type": "PostgreSQL",
  "purpose": "更新后的用途",
  "remark": "更新后的备注"
}
```

**请求参数**：

| 参数名   | 类型   | 必填 | 描述         |
|----------|--------|------|--------------|
| db_name  | string | 否   | 数据库名称   |
| db_ip    | string | 否   | 数据库IP地址 |
| db_type  | string | 否   | 数据库类型   |
| purpose  | string | 否   | 数据库用途   |
| remark   | string | 否   | 备注         |

**响应示例**：
```json
{
  "message": "数据库资源更新成功"
}
```

**错误响应**：
```json
{
  "message": "没有变更需要保存"
}
```

或

```json
{
  "error": "已下线的数据库资源不能更新"
}
```

**说明**：
- 更新数据库资源记录并返回更新结果
- 所有字段都是可选的，只更新提供的字段
- 如果没有提供任何字段或提供的值与原值相同，将返回"没有变更需要保存"
- 已下线的数据库资源不能更新
- 更新成功后会自动记录变更历史

## 下线数据库资源

将数据库资源状态设置为下线。

**请求方式**：PUT

**URL**：`/api/databases/<db_id>/offline`

**URL参数**：

| 参数名 | 类型    | 必填 | 描述         |
|--------|---------|------|--------------|
| db_id  | integer | 是   | 数据库资源ID |

**响应示例**：
```json
{
  "message": "数据库资源已成功下线"
}
```

**错误响应**：
```json
{
  "message": "数据库资源已经是下线状态"
}
```

或

```json
{
  "error": "下线数据库资源时出错: [错误详情]"
}
```

**说明**：
- 将数据库资源状态设置为"offline"，并记录下线时间
- 下线操作会自动解除该数据库资源与所有应用环境的绑定关系
- 如果数据库资源已经是下线状态，将返回400错误
- 下线操作会被记录到资源变更历史
- 如果下线过程中出现异常，将返回500错误
