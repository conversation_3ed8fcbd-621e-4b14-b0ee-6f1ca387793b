#!/usr/bin/env python3
"""
CVM模块启动测试脚本
"""
from app import create_app
from app.models import NetAreaBase, EnvNetConf, CreatCvmHostname, CvmCreationRecord
from app import db

def test_models():
    """测试模型是否正确定义"""
    print("=== 测试数据库模型 ===")
    
    app = create_app()
    with app.app_context():
        try:
            # 测试NetAreaBase模型
            print("✓ NetAreaBase模型正常")
            
            # 测试EnvNetConf模型
            print("✓ EnvNetConf模型正常")
            
            # 测试CreatCvmHostname模型
            print("✓ CreatCvmHostname模型正常")
            
            # 测试CvmCreationRecord模型
            print("✓ CvmCreationRecord模型正常")
            
            print("所有模型测试通过！")
            
        except Exception as e:
            print(f"✗ 模型测试失败: {e}")

def test_routes():
    """测试路由是否正确注册"""
    print("\n=== 测试路由注册 ===")
    
    app = create_app()
    
    cvm_routes = []
    for rule in app.url_map.iter_rules():
        if 'cvm' in str(rule):
            cvm_routes.append(str(rule))
    
    expected_routes = [
        '/api/cvm/net-areas',
        '/api/cvm/create',
        '/api/cvm/status/<int:record_id>',
        '/api/cvm/records',
        '/api/cvm/finalize/<int:record_id>',
        '/api/cvm/check-status/<int:record_id>'
    ]
    
    print("已注册的CVM路由:")
    for route in cvm_routes:
        print(f"  ✓ {route}")
    
    missing_routes = set(expected_routes) - set(cvm_routes)
    if missing_routes:
        print("缺失的路由:")
        for route in missing_routes:
            print(f"  ✗ {route}")
    else:
        print("所有预期路由都已正确注册！")

def test_tencent_sdk():
    """测试腾讯云SDK状态"""
    print("\n=== 测试腾讯云SDK ===")
    
    try:
        from app.utils.tencent_cvm import TENCENT_SDK_AVAILABLE, create_cvm_client
        
        if TENCENT_SDK_AVAILABLE:
            print("✓ 腾讯云SDK已安装")
        else:
            print("⚠ 腾讯云SDK未安装，将使用模拟模式")
        
        # 测试创建客户端
        client = create_cvm_client()
        print("✓ CVM客户端创建成功")
        
    except Exception as e:
        print(f"✗ 腾讯云SDK测试失败: {e}")

def test_database_connection():
    """测试数据库连接"""
    print("\n=== 测试数据库连接 ===")
    
    app = create_app()
    with app.app_context():
        try:
            # 尝试查询一个简单的表
            result = db.session.execute(db.text("SELECT 1")).fetchone()
            if result:
                print("✓ 数据库连接正常")
            else:
                print("✗ 数据库查询返回空结果")
                
        except Exception as e:
            print(f"✗ 数据库连接失败: {e}")

def show_setup_instructions():
    """显示设置说明"""
    print("\n" + "="*50)
    print("CVM模块设置说明")
    print("="*50)
    
    print("\n1. 数据库设置:")
    print("   执行以下SQL脚本创建必要的表:")
    print("   mysql -u username -p database_name < scripts/create_cvm_tables.sql")
    
    print("\n2. 腾讯云SDK安装 (可选):")
    print("   pip install tencentcloud-sdk-python")
    
    print("\n3. 配置腾讯云API密钥:")
    print("   INSERT INTO cloud_api_key (secret_id, secret_key, cloudname)")
    print("   VALUES ('your_secret_id', 'your_secret_key', 'tencent');")
    
    print("\n4. 配置网络安全区:")
    print("   INSERT INTO net_area_base (net_area_name, net_area_des)")
    print("   VALUES ('生产区', '生产环境网络安全区');")
    
    print("\n5. 配置环境网络参数:")
    print("   INSERT INTO env_net_conf (env_id, net_area_id, subnetid, placement, vpcid, imageid, instance_type, securitygroupid)")
    print("   VALUES (1, 1, 'subnet-xxx', 'ap-shanghai-fsi-1', 'vpc-xxx', 'img-xxx', 'S3.MEDIUM4', 'sg-xxx');")
    
    print("\n6. 配置主机名管理:")
    print("   INSERT INTO creat_cvm_hostname (nscode, current_hostname)")
    print("   VALUES ('htsh', 'htsh101268'), ('htsz', 'htsz101268');")
    
    print("\n7. 启动应用:")
    print("   python run.py")
    
    print("\n8. 测试API:")
    print("   curl -X GET 'http://localhost:5000/api/cvm/net-areas'")

def main():
    """主函数"""
    print("CVM模块启动测试")
    print("="*50)
    
    # 运行各项测试
    test_models()
    test_routes()
    test_tencent_sdk()
    test_database_connection()
    
    # 显示设置说明
    show_setup_instructions()
    
    print("\n" + "="*50)
    print("测试完成！")
    print("如果所有测试都通过，CVM模块已准备就绪。")
    print("如果有测试失败，请参考上面的设置说明进行配置。")

if __name__ == "__main__":
    main()
