"""
服务器资源管理模块
"""
from flask import Blueprint, request, jsonify, current_app, g
from .. import db, beijing_tz
from ..models import (
    ServerResource, ResourceChangeHistory, OperationType,
    TencentRegion, User, RecycledHost
)
from ..utils.cloud_api import get_tencent_credentials, get_tencent_regions
from ..utils.decorators import log_route, log_operation
from ..utils.user_utils import get_current_user_id
from datetime import datetime
import json
from sqlalchemy import or_

# 创建蓝图
server_resource = Blueprint('server_resource', __name__)

@server_resource.route('/servers', methods=['GET'])
@log_route
def get_servers():
    """
    查询服务器列表，支持按主机名、IP地址、备注进行模糊查询

    Query Parameters:
        hostname (str, optional): 主机名关键字
        ip (str, optional): IP地址关键字
        remark (str, optional): 备注关键字
        page (int, optional): 页码，默认为1
        per_page (int, optional): 每页记录数，默认为20

    Returns:
        JSON: 服务器列表和分页信息
    """
    # 获取查询参数
    hostname = request.args.get('hostname', '')
    ip = request.args.get('ip', '')
    remark = request.args.get('remark', '')
    page = int(request.args.get('page', 1))
    per_page = int(request.args.get('per_page', 20))

    # 构建查询
    query = ServerResource.query

    # 应用过滤条件
    if hostname:
        query = query.filter(ServerResource.hostname.like(f'%{hostname}%'))
    if ip:
        query = query.filter(or_(
            ServerResource.ipv4.like(f'%{ip}%'),
            ServerResource.public_ip.like(f'%{ip}%')
        ))
    if remark:
        query = query.filter(ServerResource.remark.like(f'%{remark}%'))

    # 分页
    total = query.count()
    servers = query.order_by(ServerResource.create_time.desc()).paginate(page=page, per_page=per_page)

    # 构建响应
    result = {
        'total': total,
        'page': page,
        'per_page': per_page,
        'pages': (total + per_page - 1) // per_page,
        'servers': [server.to_dict() for server in servers.items]
    }

    return jsonify(result)

@server_resource.route('/servers', methods=['POST'])
@log_route
@log_operation('创建服务器')
def create_server():
    """
    手动录入服务器

    Request Body:
        hostname (str): 主机名
        ipv4 (str): IPv4地址
        cpu (str): CPU信息
        memory (str): 内存信息
        os (str): 操作系统
        region (str): 地域
        original_source (str): 来源，默认为manual
        remark (str, optional): 备注

    Returns:
        JSON: 创建结果
    """
    data = request.get_json()

    # 验证必填字段
    required_fields = ['hostname', 'ipv4', 'cpu', 'memory', 'os', 'region']
    for field in required_fields:
        if field not in data or not data[field]:
            return jsonify({'error': f'缺少必填字段: {field}'}), 400

    # 设置默认值
    data.setdefault('original_source', 'manual')
    data.setdefault('vm_creat_time', datetime.now(beijing_tz).replace(tzinfo=None))

    # 创建服务器记录
    server = ServerResource(
        hostname=data['hostname'],
        ipv4=data['ipv4'],
        cpu=data['cpu'],
        memory=data['memory'],
        os=data['os'],
        region=data['region'],
        original_source=data['original_source'],
        remark=data.get('remark', ''),
        vm_creat_time=data['vm_creat_time'],
        server_status='online'  # 手动录入的主机默认状态为online
    )

    # 可选字段
    if 'public_ip' in data:
        server.public_ip = data['public_ip']
    if 'availability_zone' in data:
        server.availability_zone = data['availability_zone']
    if 'vpc_id' in data:
        server.vpc_id = data['vpc_id']
    if 'subnet_id' in data:
        server.subnet_id = data['subnet_id']
    if 'physical_host_id' in data:
        server.physical_host_id = data['physical_host_id']

    db.session.add(server)
    db.session.flush()  # 获取ID

    # 记录变更历史
    history = ResourceChangeHistory(
        user_id=get_current_user_id(),  # 使用工具函数获取当前用户ID
        resource_type='server',
        resource_id=server.id,
        operation_type=OperationType.CREATE.value,
        after_data=json.dumps({
            'hostname': server.hostname,
            'ipv4': server.ipv4,
            'cpu': server.cpu,
            'memory': server.memory,
            'os': server.os,
            'region': server.region,
            'original_source': server.original_source,
            'remark': server.remark
        })
    )

    db.session.add(history)
    db.session.commit()

    return jsonify({
        'message': '服务器创建成功',
        'id': server.id
    }), 201

@server_resource.route('/servers/sync-tencent', methods=['POST'])
@log_route
@log_operation('同步腾讯云服务器')
def sync_tencent_servers():
    """
    同步腾讯云服务器数据（增量同步）

    Returns:
        JSON: 同步结果
    """
    # 检查请求体是否为JSON格式（如果有请求体）
    if request.data and request.content_type and 'application/json' in request.content_type:
        try:
            _ = request.get_json()
        except Exception as e:
            current_app.logger.error(f"JSON解析错误: {str(e)}")
            return jsonify({'error': f'请求体JSON格式错误: {str(e)}'}), 400

    # 记录请求信息
    client_ip = request.remote_addr
    user_id = g.user.id if hasattr(g, 'user') else 'unknown'
    current_app.logger.info(f"开始同步腾讯云服务器数据: client_ip={client_ip}, user_id={user_id}")

    try:
        # 获取腾讯云API密钥
        secret_id, secret_key = get_tencent_credentials()
        if not secret_id or not secret_key:
            current_app.logger.error(f"同步腾讯云服务器失败: client_ip={client_ip}, user_id={user_id}, error=未配置腾讯云API密钥")
            return jsonify({'error': '未配置腾讯云API密钥'}), 400

        # 获取区域列表
        regions = get_tencent_regions()
        if not regions:
            current_app.logger.error(f"同步腾讯云服务器失败: client_ip={client_ip}, user_id={user_id}, error=未配置腾讯云区域信息")
            return jsonify({'error': '未配置腾讯云区域信息'}), 400

        # 导入腾讯云SDK
        try:
            from tencentcloud.common import credential
            from tencentcloud.common.profile.client_profile import ClientProfile
            from tencentcloud.common.profile.http_profile import HttpProfile
            from tencentcloud.cvm.v20170312 import cvm_client, models
        except ImportError:
            return jsonify({'error': '未安装腾讯云SDK，请执行 pip install tencentcloud-sdk-python'}), 500

        # 创建认证对象
        cred = credential.Credential(secret_id, secret_key)

        # 统计信息
        stats = {
            'total': 0,
            'added': 0,
            'updated': 0,
            'unchanged': 0,
            'recycled': 0,
            'errors': 0,
            'error_details': []
        }

        # 遍历所有区域
        for region in regions:
            try:
                # 创建客户端配置
                http_profile = HttpProfile()
                http_profile.endpoint = "cvm.tencentcloudapi.com"
                client_profile = ClientProfile()
                client_profile.httpProfile = http_profile

                # 创建CVM客户端
                client = cvm_client.CvmClient(cred, region, client_profile)

                # 创建请求对象
                req = models.DescribeInstancesRequest()
                req.Limit = 100  # 每页最大数量

                # 获取当前区域中的所有腾讯云实例ID
                current_instance_ids = set()

                # 分页获取实例列表
                offset = 0
                while True:
                    req.Offset = offset
                    resp = client.DescribeInstances(req)
                    instances = resp.InstanceSet

                    if not instances:
                        break

                    # 处理实例数据
                    for instance in instances:
                        stats['total'] += 1
                        try:
                            # 提取实例信息
                            instance_id = instance.InstanceId
                            current_instance_ids.add(instance_id)

                            hostname = instance.InstanceName
                            private_ip = instance.PrivateIpAddresses[0] if instance.PrivateIpAddresses else ""
                            public_ip = instance.PublicIpAddresses[0] if instance.PublicIpAddresses else None
                            os_name = instance.OsName
                            cpu = str(instance.CPU)
                            memory = str(instance.Memory)
                            zone = instance.Placement.Zone
                            vpc_id = instance.VirtualPrivateCloud.VpcId if instance.VirtualPrivateCloud else None
                            subnet_id = instance.VirtualPrivateCloud.SubnetId if instance.VirtualPrivateCloud else None
                            created_time = datetime.strptime(instance.CreatedTime, "%Y-%m-%dT%H:%M:%SZ")
                            host_id = instance.Placement.HostId if instance.Placement else None

                            # 查找现有记录
                            existing = ServerResource.query.filter_by(instance_id=instance_id).first()

                            if existing:
                                # 更新现有记录
                                before_data = {
                                    'hostname': existing.hostname,
                                    'ipv4': existing.ipv4,
                                    'public_ip': existing.public_ip,
                                    'os': existing.os,
                                    'cpu': existing.cpu,
                                    'memory': existing.memory,
                                    'availability_zone': existing.availability_zone,
                                    'vpc_id': existing.vpc_id,
                                    'subnet_id': existing.subnet_id,
                                    'physical_host_id': existing.physical_host_id
                                }

                                # 检查是否有变化
                                changed = False
                                if (existing.hostname != hostname or
                                    existing.ipv4 != private_ip or
                                    existing.public_ip != public_ip or
                                    existing.os != os_name or
                                    existing.cpu != cpu or
                                    existing.memory != memory or
                                    existing.availability_zone != zone or
                                    existing.vpc_id != vpc_id or
                                    existing.subnet_id != subnet_id or
                                    existing.physical_host_id != host_id):
                                    changed = True

                                # 确保状态字段不为NULL，如果为NULL则设置为online
                                if existing.server_status is None:
                                    existing.server_status = 'online'
                                    existing.offline_time = None  # online状态时offline_time应该为NULL
                                    changed = True

                                if changed:
                                    # 更新记录
                                    existing.hostname = hostname
                                    existing.ipv4 = private_ip
                                    existing.public_ip = public_ip
                                    existing.os = os_name
                                    existing.cpu = cpu
                                    existing.memory = memory
                                    existing.availability_zone = zone
                                    existing.vpc_id = vpc_id
                                    existing.subnet_id = subnet_id
                                    existing.physical_host_id = host_id

                                    # 记录变更历史
                                    after_data = {
                                        'hostname': hostname,
                                        'ipv4': private_ip,
                                        'public_ip': public_ip,
                                        'os': os_name,
                                        'cpu': cpu,
                                        'memory': memory,
                                        'availability_zone': zone,
                                        'vpc_id': vpc_id,
                                        'subnet_id': subnet_id,
                                        'physical_host_id': host_id
                                    }

                                    history = ResourceChangeHistory(
                                        user_id=g.user.id if hasattr(g, 'user') else 1,
                                        resource_type='server',
                                        resource_id=existing.id,
                                        operation_type=OperationType.UPDATE.value,
                                        before_data=json.dumps(before_data),
                                        after_data=json.dumps(after_data)
                                    )

                                    db.session.add(history)
                                    stats['updated'] += 1
                                else:
                                    stats['unchanged'] += 1
                            else:
                                # 创建新记录
                                server = ServerResource(
                                    instance_id=instance_id,
                                    hostname=hostname,
                                    ipv4=private_ip,
                                    public_ip=public_ip,
                                    os=os_name,
                                    cpu=cpu,
                                    memory=memory,
                                    region=region,
                                    availability_zone=zone,
                                    vpc_id=vpc_id,
                                    subnet_id=subnet_id,
                                    original_source='tencent',
                                    vm_creat_time=created_time,
                                    physical_host_id=host_id,
                                    server_status='online'  # 腾讯云同步的主机默认状态为online
                                )

                                db.session.add(server)
                                db.session.flush()  # 获取ID

                                # 记录变更历史
                                history = ResourceChangeHistory(
                                    user_id=g.user.id if hasattr(g, 'user') else 1,
                                    resource_type='server',
                                    resource_id=server.id,
                                    operation_type=OperationType.CREATE.value,
                                    after_data=json.dumps({
                                        'instance_id': instance_id,
                                        'hostname': hostname,
                                        'ipv4': private_ip,
                                        'public_ip': public_ip,
                                        'os': os_name,
                                        'cpu': cpu,
                                        'memory': memory,
                                        'region': region,
                                        'availability_zone': zone,
                                        'vpc_id': vpc_id,
                                        'subnet_id': subnet_id,
                                        'original_source': 'tencent',
                                        'physical_host_id': host_id
                                    })
                                )

                                db.session.add(history)
                                stats['added'] += 1
                        except Exception as e:
                            stats['errors'] += 1
                            stats['error_details'].append(f"处理实例 {instance.InstanceId} 时出错: {str(e)}")
                            current_app.logger.error(f"处理实例 {instance.InstanceId} 时出错: {str(e)}")

                    # 更新偏移量
                    offset += len(instances)
                    if len(instances) < 100:
                        break

                # 处理已回收的主机（在数据库中存在但在API返回结果中不存在的主机）
                # 只处理来源为'tencent'且区域为当前区域的主机
                recycled_hosts = ServerResource.query.filter(
                    ServerResource.original_source == 'tencent',
                    ServerResource.region == region,
                    ServerResource.instance_id.notin_(current_instance_ids) if current_instance_ids else False,
                    ServerResource.server_status == 'online'  # 只处理在线状态的主机
                ).all()

                for host in recycled_hosts:
                    try:
                        # 记录变更前数据
                        before_data = {
                            'instance_id': host.instance_id,
                            'hostname': host.hostname,
                            'ipv4': host.ipv4,
                            'region': host.region,
                            'server_status': host.server_status,
                            'offline_time': host.offline_time.strftime('%Y-%m-%d %H:%M:%S') if host.offline_time else None
                        }

                        # 更新主机状态为下线
                        host.server_status = 'offline'
                        host.offline_time = datetime.now(beijing_tz).replace(tzinfo=None)

                        # 解除与应用环境的绑定关系
                        from app.models import ApplicationEnvServer
                        env_servers = ApplicationEnvServer.query.filter_by(server_id=host.id).all()
                        for env_server in env_servers:
                            db.session.delete(env_server)

                        # 记录变更历史
                        history = ResourceChangeHistory(
                            user_id=g.user.id if hasattr(g, 'user') else 1,
                            resource_type='server',
                            resource_id=host.id,
                            operation_type=OperationType.UPDATE.value,
                            before_data=json.dumps(before_data),
                            after_data=json.dumps({
                                'server_status': host.server_status,
                                'offline_time': host.offline_time.strftime('%Y-%m-%d %H:%M:%S')
                            })
                        )
                        db.session.add(history)

                        # 将回收的主机信息保存到recycled_host表
                        from sqlalchemy import text
                        recycled_time = datetime.now(beijing_tz).replace(tzinfo=None)

                        # 使用原生SQL插入回收主机记录
                        db.session.execute(
                            text("""
                                INSERT INTO recycled_host
                                (instance_id, hostname, ipv4, recycled_time)
                                VALUES (:instance_id, :hostname, :ipv4, :recycled_time)
                            """),
                            {
                                'instance_id': host.instance_id,
                                'hostname': host.hostname,
                                'ipv4': host.ipv4,
                                'recycled_time': recycled_time
                            }
                        )

                        stats['recycled'] += 1
                    except Exception as e:
                        stats['errors'] += 1
                        stats['error_details'].append(f"处理回收主机 {host.instance_id} 时出错: {str(e)}")
                        current_app.logger.error(f"处理回收主机 {host.instance_id} 时出错: {str(e)}")

            except Exception as e:
                stats['errors'] += 1
                stats['error_details'].append(f"处理区域 {region} 时出错: {str(e)}")
                current_app.logger.error(f"处理区域 {region} 时出错: {str(e)}")

        # 提交事务
        db.session.commit()

        # 记录同步结果
        current_app.logger.info(f"腾讯云服务器同步完成: client_ip={client_ip}, user_id={user_id}, " +
                               f"total={stats['total']}, added={stats['added']}, updated={stats['updated']}, " +
                               f"unchanged={stats['unchanged']}, recycled={stats['recycled']}, errors={stats['errors']}")

        return jsonify({
            'message': '腾讯云服务器同步完成',
            'stats': stats
        })
    except Exception as e:
        db.session.rollback()
        error_msg = f"同步腾讯云服务器时出错: {str(e)}"
        current_app.logger.error(f"{error_msg}, client_ip={client_ip}, user_id={user_id}")
        return jsonify({'error': error_msg}), 500

@server_resource.route('/servers/<int:server_id>', methods=['PUT', 'DELETE'])
@log_route
@log_operation('下线服务器')
def offline_server(server_id):
    """
    下线服务器（仅限手动录入的服务器）

    URL Parameters:
        server_id (int): 服务器ID

    Returns:
        JSON: 下线结果
    """
    # 检查请求体是否为JSON格式（如果有请求体）
    if request.data and request.content_type and 'application/json' in request.content_type:
        try:
            _ = request.get_json()
        except Exception as e:
            current_app.logger.error(f"JSON解析错误: {str(e)}")
            return jsonify({'error': f'请求体JSON格式错误: {str(e)}'}), 400

    # 查找服务器
    server = ServerResource.query.get_or_404(server_id)

    # 检查是否为手动录入的服务器
    if server.original_source != 'manual':
        return jsonify({'error': '只能下线手动录入的服务器'}), 403

    # 检查服务器是否已经下线
    if server.server_status == 'offline':
        return jsonify({'message': '服务器已经是下线状态'}), 400

    try:
        # 记录变更前数据
        before_data = {
            'instance_id': server.instance_id,
            'hostname': server.hostname,
            'ipv4': server.ipv4,
            'server_status': server.server_status,
            'offline_time': server.offline_time.strftime('%Y-%m-%d %H:%M:%S') if server.offline_time else None
        }

        # 更新服务器状态为下线
        server.server_status = 'offline'
        server.offline_time = datetime.now(beijing_tz).replace(tzinfo=None)

        # 解除与应用环境的绑定关系
        from app.models import ApplicationEnvServer
        env_servers = ApplicationEnvServer.query.filter_by(server_id=server.id).all()
        for env_server in env_servers:
            db.session.delete(env_server)

        # 将服务器信息保存到回收站
        recycled = RecycledHost(
            instance_id=server.instance_id,
            hostname=server.hostname,
            ipv4=server.ipv4,
            recycled_time=datetime.now(beijing_tz).replace(tzinfo=None)
        )
        db.session.add(recycled)

        # 记录变更历史
        history = ResourceChangeHistory(
            user_id=g.user.id if hasattr(g, 'user') else 1,
            resource_type='server',
            resource_id=server.id,
            operation_type=OperationType.UPDATE.value,
            before_data=json.dumps(before_data),
            after_data=json.dumps({
                'server_status': server.server_status,
                'offline_time': server.offline_time.strftime('%Y-%m-%d %H:%M:%S')
            })
        )
        db.session.add(history)

        db.session.commit()

        return jsonify({'message': '服务器已成功下线'})

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"下线服务器时出错: {str(e)}")
        return jsonify({'error': f'下线服务器时出错: {str(e)}'}), 500

