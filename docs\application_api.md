# 应用系统管理 API 文档

本文档描述了应用系统管理模块（`application.py`）中的所有 API 接口。

## 目录

- [获取应用系统列表](#获取应用系统列表)
- [创建应用系统](#创建应用系统)
- [更新应用系统信息](#更新应用系统信息)
- [下线应用系统](#下线应用系统)

## 获取应用系统列表

获取系统中所有应用系统的信息，支持按系统英文名、系统中文名、管理员进行模糊查询。

**请求方式**：GET

**URL**：`/api/applications`

**查询参数**：

| 参数名 | 类型 | 必填 | 描述 |
|-------|------|------|------|
| app_en_name | string | 否 | 系统英文名关键字，支持模糊查询 |
| app_cn_name | string | 否 | 系统中文名关键字，支持模糊查询 |
| app_admin | string | 否 | 系统管理员关键字，支持模糊查询 |
| page | integer | 否 | 页码，默认为1 |
| per_page | integer | 否 | 每页记录数，默认为20 |

**响应示例**：

```json
{
  "total": 2,
  "page": 1,
  "per_page": 20,
  "pages": 1,
  "applications": [
    {
      "id": 2,
      "app_en_name": "icms",
      "app_cn_name": "IT资源管理系统",
      "app_admin": "张三",
      "app_status": "online",
      "create_time": "2023-05-22 10:15:30",
      "update_time": "2023-05-22 10:15:30",
      "offline_time": null,
      "remark": "IT资源管理平台"
    },
    {
      "id": 1,
      "app_en_name": "oa",
      "app_cn_name": "办公自动化系统",
      "app_admin": "李四",
      "app_status": "online",
      "create_time": "2023-05-20 09:30:00",
      "update_time": "2023-05-21 14:20:15",
      "offline_time": null,
      "remark": "内部办公系统"
    }
  ]
}
```

## 创建应用系统

创建新的应用系统记录。

**请求方式**：POST

**URL**：`/api/applications`

**请求体**：

| 参数名 | 类型 | 必填 | 描述 |
|-------|------|------|------|
| app_en_name | string | 是 | 系统英文名，唯一标识 |
| app_cn_name | string | 是 | 系统中文名 |
| app_admin | string | 是 | 系统管理员 |
| remark | string | 否 | 备注信息 |

**请求示例**：

```json
{
  "app_en_name": "crm",
  "app_cn_name": "客户关系管理系统",
  "app_admin": "王五",
  "remark": "销售部门使用的客户管理系统"
}
```

**响应示例**：

```json
{
  "message": "应用系统创建成功",
  "id": 3
}
```

**错误响应**：

```json
{
  "error": "缺少必填字段: app_en_name"
}
```

或

```json
{
  "error": "系统英文名 crm 已存在"
}
```

## 更新应用系统信息

更新现有应用系统的信息，仅支持修改系统管理员和备注字段。

**请求方式**：PUT

**URL**：`/api/applications/{app_id}`

**路径参数**：

| 参数名 | 类型 | 描述 |
|-------|------|------|
| app_id | integer | 应用系统ID |

**请求体**：

| 参数名 | 类型 | 必填 | 描述 |
|-------|------|------|------|
| app_admin | string | 否 | 系统管理员 |
| remark | string | 否 | 备注信息 |

**请求示例**：

```json
{
  "app_admin": "赵六",
  "remark": "更新后的备注信息"
}
```

**响应示例**：

```json
{
  "message": "应用系统更新成功"
}
```

**错误响应**：

```json
{
  "message": "没有变更需要保存"
}
```

## 下线应用系统

将应用系统状态设置为下线，并记录下线时间。

**请求方式**：PUT

**URL**：`/api/applications/{app_id}/offline`

**路径参数**：

| 参数名 | 类型 | 描述 |
|-------|------|------|
| app_id | integer | 应用系统ID |

**响应示例**：

```json
{
  "message": "应用系统已成功下线"
}
```

**错误响应**：

```json
{
  "message": "应用系统已经是下线状态"
}
```

或

```json
{
  "error": "无法下线应用系统，该系统还有在线的应用环境",
  "env_count": 2,
  "environments": [
    {
      "id": 1,
      "env_name": "生产环境(prod)"
    },
    {
      "id": 3,
      "env_name": "测试环境(test)"
    }
  ],
  "has_more": false
}
```

**说明**：
- 将应用系统状态设置为"offline"，并记录下线时间
- 在下线应用系统前，会检查该系统是否有在线状态的应用环境
- 如果存在在线的应用环境，将返回400错误，并提供在线环境的列表
- 如果应用系统已经是下线状态，将返回400错误
