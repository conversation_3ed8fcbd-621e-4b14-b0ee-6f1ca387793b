from flask import Flask
from flask_sqlalchemy import SQLAlchemy
from flask_cors import CORS
import pytz
from config import Config

# 定义北京时区
beijing_tz = pytz.timezone('Asia/Shanghai')

db = SQLAlchemy()

# 在模块级别导入模型
from .models import (
    User, Role, Module, Menu, UserRole, RoleModule, Ldap, LocalUser,
    CloudApiKey, TencentRegion, ServerResource, ResourceChangeHistory, OperationType,
    RecycledHost, LoadBalancerResource, Application, DatabaseResource, EnvironmentBase,
    DeployMethod, ApplicationEnvironment, ApplicationEnvLb, ApplicationEnvServer, ApplicationEnvDatabase,
    NetAreaBase, EnvNetConf, CreatCvmHostname, CvmCreationRecord
)

def create_app(config_class=Config):
    app = Flask(__name__)
    app.config.from_object(config_class)

    CORS(app, supports_credentials=app.config.get('CORS_SUPPORTS_CREDENTIALS', True))

    # 初始化数据库
    db.init_app(app)

    # 调用配置类的初始化方法（包括日志配置）
    config_class.init_app(app)

    # 设置认证中间件
    from .utils.auth_middleware import setup_auth_middleware
    setup_auth_middleware(app)

    # 注册路由
    from .routes import init_routes
    init_routes(app)

    # 初始化定时任务调度器（仅在非测试环境下启用）
    if not app.config.get('TESTING', False):
        from .scheduler import init_scheduler, add_scheduled_jobs
        scheduler = init_scheduler(app)
        add_scheduled_jobs(app)
        app.logger.info("定时任务调度器已初始化")

    # 记录应用启动信息
    app.logger.info(f"ICMS2应用已启动，环境：{app.config.get('ENV', 'development')}")

    return app
