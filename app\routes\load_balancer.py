"""
负载均衡资源管理模块
"""
from flask import Blueprint, request, jsonify, current_app, g
from .. import db, beijing_tz
from ..models import (
    LoadBalancerResource, ResourceChangeHistory, OperationType,
    TencentRegion
)
from ..utils.cloud_api import get_tencent_credentials, get_tencent_regions
from ..utils.decorators import log_route, log_operation
from datetime import datetime
import json
from sqlalchemy import or_

# 创建蓝图
load_balancer = Blueprint('load_balancer', __name__)

@load_balancer.route('/load-balancers', methods=['GET'])
@log_route
def get_load_balancers():
    """
    查询负载均衡列表，支持按名称、VIP地址进行模糊查询
    """
    try:
        # 获取查询参数
        name = request.args.get('name', '')
        vip = request.args.get('vip', '')
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 20, type=int)

        # 记录请求日志
        client_ip = request.remote_addr
        current_app.logger.info(f"查询负载均衡列表: client_ip={client_ip}, name={name}, vip={vip}, page={page}, per_page={per_page}")

        # 构建查询
        query = LoadBalancerResource.query

        # 应用过滤条件
        if name:
            query = query.filter(LoadBalancerResource.lb_name.like(f'%{name}%'))
        if vip:
            query = query.filter(LoadBalancerResource.vip.like(f'%{vip}%'))

        # 按创建时间降序排序
        query = query.order_by(LoadBalancerResource.create_time.desc())

        # 分页
        pagination = query.paginate(page=page, per_page=per_page, error_out=False)
        items = pagination.items

        # 构建响应
        result = {
            'items': [item.to_dict() for item in items],
            'pagination': {
                'total': pagination.total,
                'page': page,
                'per_page': per_page,
                'pages': pagination.pages
            }
        }

        # 记录响应日志
        current_app.logger.info(f"查询负载均衡列表成功: client_ip={client_ip}, total={pagination.total}")

        return jsonify(result)
    except Exception as e:
        # 记录错误日志
        client_ip = request.remote_addr
        current_app.logger.error(f"查询负载均衡列表失败: client_ip={client_ip}, error={str(e)}")
        return jsonify({'error': f'查询负载均衡列表失败: {str(e)}'}), 500

@load_balancer.route('/load-balancers/sync-tencent', methods=['POST'])
@log_route
@log_operation('同步腾讯云负载均衡')
def sync_tencent_load_balancers():
    """
    同步腾讯云负载均衡数据
    """
    try:
        # 检查请求体是否为JSON格式（如果有请求体）
        if request.data and request.content_type and 'application/json' in request.content_type:
            try:
                _ = request.get_json()
            except Exception as e:
                current_app.logger.error(f"JSON解析错误: {str(e)}")
                return jsonify({'error': f'请求体JSON格式错误: {str(e)}'}), 400

        # 记录请求日志
        client_ip = request.remote_addr
        user_id = g.user.id if hasattr(g, 'user') else 'unknown'
        current_app.logger.info(f"开始同步腾讯云负载均衡数据: client_ip={client_ip}, user_id={user_id}")

        # 获取腾讯云API密钥
        secret_id, secret_key = get_tencent_credentials()
        if not secret_id or not secret_key:
            current_app.logger.error(f"同步腾讯云负载均衡失败: client_ip={client_ip}, user_id={user_id}, error=未配置腾讯云API密钥")
            return jsonify({'error': '未配置腾讯云API密钥'}), 400

        # 获取区域列表
        regions = get_tencent_regions()
        if not regions:
            current_app.logger.error(f"同步腾讯云负载均衡失败: client_ip={client_ip}, user_id={user_id}, error=未配置腾讯云区域信息")
            return jsonify({'error': '未配置腾讯云区域信息'}), 400

        # 导入腾讯云SDK
        try:
            from tencentcloud.common import credential
            from tencentcloud.common.profile.client_profile import ClientProfile
            from tencentcloud.common.profile.http_profile import HttpProfile
            from tencentcloud.clb.v20180317 import clb_client, models
        except ImportError:
            current_app.logger.error(f"同步腾讯云负载均衡失败: client_ip={client_ip}, user_id={user_id}, error=未安装腾讯云SDK")
            return jsonify({'error': '未安装腾讯云SDK，请执行 pip install tencentcloud-sdk-python'}), 500

        # 创建认证对象
        cred = credential.Credential(secret_id, secret_key)

        # 统计信息
        stats = {
            'total': 0,
            'added': 0,
            'updated': 0,
            'unchanged': 0,
            'errors': 0,
            'error_details': []
        }

        # 遍历所有区域
        for region in regions:
            try:
                current_app.logger.info(f"开始同步区域 {region} 的负载均衡数据")

                # 创建客户端配置
                http_profile = HttpProfile()
                http_profile.endpoint = "clb.tencentcloudapi.com"
                client_profile = ClientProfile()
                client_profile.httpProfile = http_profile

                # 创建CLB客户端
                client = clb_client.ClbClient(cred, region, client_profile)

                # 分页获取所有负载均衡实例
                offset = 0
                limit = 100  # 每次请求的最大数量

                while True:
                    # 创建请求对象
                    req = models.DescribeLoadBalancersRequest()
                    req.Limit = limit
                    req.Offset = offset

                    # 发送请求
                    resp = client.DescribeLoadBalancers(req)

                    # 如果没有数据，退出循环
                    if not hasattr(resp, 'LoadBalancerSet') or not resp.LoadBalancerSet:
                        if offset == 0:
                            current_app.logger.info(f"区域 {region} 中没有负载均衡实例")
                        break

                    current_app.logger.info(f"区域 {region} 获取到 {len(resp.LoadBalancerSet)} 个负载均衡实例，偏移量 {offset}")

                    # 处理每个负载均衡实例
                    for lb in resp.LoadBalancerSet:
                        try:
                            stats['total'] += 1

                            # 检查是否已存在
                            existing = LoadBalancerResource.query.filter_by(loadbalancer_id=lb.LoadBalancerId).first()

                            # 提取VIP信息
                            vip = ""
                            if hasattr(lb, 'LoadBalancerVips') and lb.LoadBalancerVips:
                                vip = lb.LoadBalancerVips[0]
                            elif hasattr(lb, 'AddressIPv6') and lb.AddressIPv6:
                                vip = lb.AddressIPv6

                            # 提取IP版本
                            ip_version = None
                            if hasattr(lb, 'AddressIPVersion'):
                                ip_version = lb.AddressIPVersion.lower()

                            # 提取区域信息
                            zone_region = region
                            if hasattr(lb, 'MasterZone') and hasattr(lb.MasterZone, 'ZoneRegion'):
                                zone_region = lb.MasterZone.ZoneRegion
                            elif hasattr(lb, 'TargetRegionInfo') and hasattr(lb.TargetRegionInfo, 'Region'):
                                zone_region = lb.TargetRegionInfo.Region

                            # 提取VPC ID
                            vpc_id = ""
                            if hasattr(lb, 'VpcId'):
                                vpc_id = lb.VpcId

                            # 提取负载均衡类型
                            lb_type = None
                            if hasattr(lb, 'Forward'):
                                lb_type = lb.Forward

                            # 提取创建时间
                            create_time = datetime.now(beijing_tz).replace(tzinfo=None)
                            if hasattr(lb, 'CreateTime'):
                                try:
                                    create_time = datetime.strptime(lb.CreateTime, '%Y-%m-%d %H:%M:%S')
                                except:
                                    current_app.logger.warning(f"无法解析创建时间: {lb.CreateTime}")

                            if existing:
                                # 更新现有记录
                                before_data = {
                                    'lb_name': existing.lb_name,
                                    'vip': existing.vip,
                                    'ip_version': existing.ip_version,
                                    'belong_network': existing.belong_network,
                                    'lb_type': existing.lb_type,
                                    'zone_region': existing.zone_region
                                }

                                # 检查是否有变化
                                changed = False
                                if (existing.lb_name != lb.LoadBalancerName or
                                    existing.vip != vip or
                                    existing.ip_version != ip_version or
                                    existing.belong_network != vpc_id or
                                    existing.lb_type != lb_type or
                                    existing.zone_region != zone_region):
                                    changed = True

                                # 确保状态字段不为NULL，如果为NULL则设置为online
                                if existing.lb_status is None:
                                    existing.lb_status = 'online'
                                    existing.offline_time = None  # online状态时offline_time应该为NULL
                                    changed = True

                                if changed:
                                    # 更新记录
                                    existing.lb_name = lb.LoadBalancerName
                                    existing.vip = vip
                                    existing.ip_version = ip_version
                                    existing.belong_network = vpc_id
                                    existing.lb_type = lb_type
                                    existing.zone_region = zone_region

                                    # 记录变更历史
                                    after_data = {
                                        'lb_name': lb.LoadBalancerName,
                                        'vip': vip,
                                        'ip_version': ip_version,
                                        'belong_network': vpc_id,
                                        'lb_type': lb_type,
                                        'zone_region': zone_region
                                    }

                                    history = ResourceChangeHistory(
                                        user_id=g.user.id if hasattr(g, 'user') else 1,
                                        resource_type='load_balancer',
                                        resource_id=existing.id,
                                        operation_type=OperationType.UPDATE.value,
                                        before_data=json.dumps(before_data),
                                        after_data=json.dumps(after_data)
                                    )

                                    db.session.add(history)
                                    stats['updated'] += 1
                                else:
                                    stats['unchanged'] += 1
                            else:
                                # 创建新记录
                                new_lb = LoadBalancerResource(
                                    loadbalancer_id=lb.LoadBalancerId,
                                    lb_name=lb.LoadBalancerName,
                                    vip=vip,
                                    ip_version=ip_version,
                                    belong_network=vpc_id,
                                    lb_type=lb_type,
                                    zone_region=zone_region,
                                    create_time=create_time,
                                    original_source='tencent',
                                    lb_status='online'  # 腾讯云同步的负载均衡默认状态为online
                                )

                                db.session.add(new_lb)
                                db.session.flush()  # 获取ID

                                # 记录变更历史
                                history = ResourceChangeHistory(
                                    user_id=g.user.id if hasattr(g, 'user') else 1,
                                    resource_type='load_balancer',
                                    resource_id=new_lb.id,
                                    operation_type=OperationType.CREATE.value,
                                    after_data=json.dumps({
                                        'loadbalancer_id': lb.LoadBalancerId,
                                        'lb_name': lb.LoadBalancerName,
                                        'vip': vip,
                                        'ip_version': ip_version,
                                        'belong_network': vpc_id,
                                        'lb_type': lb_type,
                                        'zone_region': zone_region,
                                        'original_source': 'tencent'
                                    })
                                )

                                db.session.add(history)
                                stats['added'] += 1
                        except Exception as e:
                            stats['errors'] += 1
                            error_msg = f"处理负载均衡 {lb.LoadBalancerId} 时出错: {str(e)}"
                            stats['error_details'].append(error_msg)
                            current_app.logger.error(error_msg)

                    # 更新偏移量，继续获取下一页数据
                    offset += len(resp.LoadBalancerSet)

                    # 如果返回的数据量小于请求的限制，说明已经获取完所有数据
                    if len(resp.LoadBalancerSet) < limit:
                        break

                    # 每处理完一页数据就提交事务，避免长时间占用数据库连接
                    db.session.commit()

                # 处理已下线的负载均衡（在数据库中存在但在API返回结果中不存在的负载均衡）
                # 收集当前区域中所有API返回的负载均衡ID
                current_lb_ids = set()

                # 重新分页获取所有负载均衡实例ID，用于离线状态判断
                offset = 0
                limit = 100

                while True:
                    # 创建请求对象
                    req = models.DescribeLoadBalancersRequest()
                    req.Limit = limit
                    req.Offset = offset

                    # 发送请求
                    resp = client.DescribeLoadBalancers(req)

                    # 如果没有数据，退出循环
                    if not hasattr(resp, 'LoadBalancerSet') or not resp.LoadBalancerSet:
                        break

                    # 收集负载均衡ID
                    for lb in resp.LoadBalancerSet:
                        current_lb_ids.add(lb.LoadBalancerId)

                    # 更新偏移量，继续获取下一页数据
                    offset += len(resp.LoadBalancerSet)

                    # 如果返回的数据量小于请求的限制，说明已经获取完所有数据
                    if len(resp.LoadBalancerSet) < limit:
                        break

                # 查询数据库中来源为'tencent'且区域为当前区域且状态为在线的负载均衡
                offline_lbs = LoadBalancerResource.query.filter(
                    LoadBalancerResource.original_source == 'tencent',
                    LoadBalancerResource.zone_region == region,
                    LoadBalancerResource.loadbalancer_id.notin_(current_lb_ids) if current_lb_ids else True,
                    LoadBalancerResource.lb_status == 'online'  # 只处理在线状态的负载均衡
                ).all()

                for lb in offline_lbs:
                    try:
                        # 记录变更前数据
                        before_data = {
                            'loadbalancer_id': lb.loadbalancer_id,
                            'lb_name': lb.lb_name,
                            'vip': lb.vip,
                            'lb_status': lb.lb_status,
                            'offline_time': lb.offline_time.strftime('%Y-%m-%d %H:%M:%S') if lb.offline_time else None
                        }

                        # 更新负载均衡状态为下线
                        lb.lb_status = 'offline'
                        lb.offline_time = datetime.now(beijing_tz).replace(tzinfo=None)

                        # 解除与应用环境的绑定关系
                        from app.models import ApplicationEnvLb
                        env_lbs = ApplicationEnvLb.query.filter_by(lb_id=lb.id).all()
                        for env_lb in env_lbs:
                            db.session.delete(env_lb)

                        # 记录变更历史
                        history = ResourceChangeHistory(
                            user_id=g.user.id if hasattr(g, 'user') else 1,
                            resource_type='load_balancer',
                            resource_id=lb.id,
                            operation_type=OperationType.UPDATE.value,
                            before_data=json.dumps(before_data),
                            after_data=json.dumps({
                                'lb_status': lb.lb_status,
                                'offline_time': lb.offline_time.strftime('%Y-%m-%d %H:%M:%S')
                            })
                        )
                        db.session.add(history)

                        stats['recycled'] = stats.get('recycled', 0) + 1
                    except Exception as e:
                        stats['errors'] += 1
                        error_msg = f"处理下线负载均衡 {lb.loadbalancer_id} 时出错: {str(e)}"
                        stats['error_details'].append(error_msg)
                        current_app.logger.error(error_msg)

                current_app.logger.info(f"区域 {region} 的负载均衡数据同步完成")

            except Exception as e:
                stats['errors'] += 1
                error_msg = f"处理区域 {region} 时出错: {str(e)}"
                stats['error_details'].append(error_msg)
                current_app.logger.error(error_msg)

        # 最终提交事务
        db.session.commit()

        # 记录同步结果
        current_app.logger.info(f"腾讯云负载均衡同步完成: client_ip={client_ip}, user_id={user_id}, " +
                               f"total={stats['total']}, added={stats['added']}, updated={stats['updated']}, " +
                               f"unchanged={stats['unchanged']}, errors={stats['errors']}")

        return jsonify({
            'message': '腾讯云负载均衡同步完成',
            'stats': stats
        })
    except Exception as e:
        db.session.rollback()
        client_ip = request.remote_addr
        user_id = g.user.id if hasattr(g, 'user') else 'unknown'
        current_app.logger.error(f"同步腾讯云负载均衡时出错: client_ip={client_ip}, user_id={user_id}, error={str(e)}")
        return jsonify({'error': f'同步腾讯云负载均衡时出错: {str(e)}'}), 500

@load_balancer.route('/load-balancers/<string:region>/<string:lb_id>/backends', methods=['GET'])
@log_route
def get_load_balancer_backends(region, lb_id):
    """
    获取负载均衡绑定的后端服务列表
    """
    try:
        # 记录请求日志
        client_ip = request.remote_addr
        user_id = g.user.id if hasattr(g, 'user') else 'unknown'
        current_app.logger.info(f"获取负载均衡后端服务: client_ip={client_ip}, user_id={user_id}, region={region}, lb_id={lb_id}")

        # 获取负载均衡信息
        lb = LoadBalancerResource.query.filter_by(loadbalancer_id=lb_id).first()
        if not lb:
            current_app.logger.warning(f"获取负载均衡后端服务失败: client_ip={client_ip}, user_id={user_id}, region={region}, lb_id={lb_id}, error=负载均衡不存在")
            return jsonify({'error': '负载均衡不存在'}), 404

        # 获取腾讯云API密钥
        secret_id, secret_key = get_tencent_credentials()
        if not secret_id or not secret_key:
            current_app.logger.error(f"获取负载均衡后端服务失败: client_ip={client_ip}, user_id={user_id}, region={region}, lb_id={lb_id}, error=未配置腾讯云API密钥")
            return jsonify({'error': '未配置腾讯云API密钥'}), 400

        # 导入腾讯云SDK
        try:
            from tencentcloud.common import credential
            from tencentcloud.common.profile.client_profile import ClientProfile
            from tencentcloud.common.profile.http_profile import HttpProfile
            from tencentcloud.clb.v20180317 import clb_client, models
        except ImportError:
            current_app.logger.error(f"获取负载均衡后端服务失败: client_ip={client_ip}, user_id={user_id}, region={region}, lb_id={lb_id}, error=未安装腾讯云SDK")
            return jsonify({'error': '未安装腾讯云SDK，请执行 pip install tencentcloud-sdk-python'}), 500

        # 创建认证对象
        cred = credential.Credential(secret_id, secret_key)

        # 创建客户端配置
        http_profile = HttpProfile()
        http_profile.endpoint = "clb.tencentcloudapi.com"
        client_profile = ClientProfile()
        client_profile.httpProfile = http_profile

        # 创建CLB客户端
        client = clb_client.ClbClient(cred, region, client_profile)

        # 根据负载均衡类型选择不同的API
        backends = []
        if lb.lb_type == 1:  # 通用负载均衡
            # 创建请求对象
            req = models.DescribeTargetsRequest()
            req.LoadBalancerId = lb_id

            # 发送请求
            resp = client.DescribeTargets(req)

            # 处理响应
            if not hasattr(resp, 'Listeners') or not resp.Listeners:
                current_app.logger.info(f"负载均衡 {lb_id} 没有绑定后端服务")
                return jsonify({'backends': []})

            # 遍历所有监听器
            for listener in resp.Listeners:
                # 获取监听器协议和端口
                protocol = listener.Protocol if hasattr(listener, 'Protocol') else ''
                listener_port = listener.Port if hasattr(listener, 'Port') else ''

                # 根据协议类型处理
                if protocol == 'TCP':
                    # TCP协议直接处理Targets
                    if hasattr(listener, 'Targets') and listener.Targets:
                        for target in listener.Targets:
                            backends.append({
                                'protocol': protocol,
                                'listener_port': listener_port,
                                'domain': '',  # TCP协议没有域名，保持一致的数据结构
                                'instance_id': target.InstanceId if hasattr(target, 'InstanceId') else '',
                                'ip': target.PrivateIpAddresses[0] if hasattr(target, 'PrivateIpAddresses') and target.PrivateIpAddresses else '',
                                'port': target.Port if hasattr(target, 'Port') else '',
                                'weight': target.Weight if hasattr(target, 'Weight') else ''
                            })
                elif protocol in ['HTTP', 'HTTPS']:
                    # HTTP/HTTPS协议处理Rules
                    if hasattr(listener, 'Rules') and listener.Rules:
                        for rule in listener.Rules:
                            # 获取域名
                            domain = rule.Domain if hasattr(rule, 'Domain') else ''

                            # 处理每个规则下的目标
                            if hasattr(rule, 'Targets') and rule.Targets:
                                for target in rule.Targets:
                                    backends.append({
                                        'protocol': protocol,
                                        'listener_port': listener_port,
                                        'domain': domain,
                                        'instance_id': target.InstanceId if hasattr(target, 'InstanceId') else '',
                                        'ip': target.PrivateIpAddresses[0] if hasattr(target, 'PrivateIpAddresses') and target.PrivateIpAddresses else '',
                                        'port': target.Port if hasattr(target, 'Port') else '',
                                        'weight': target.Weight if hasattr(target, 'Weight') else ''
                                    })
                else:
                    # 其他协议类型，保持一致的数据结构
                    if hasattr(listener, 'Rules') and listener.Rules:
                        for rule in listener.Rules:
                            if hasattr(rule, 'Targets') and rule.Targets:
                                for target in rule.Targets:
                                    backends.append({
                                        'protocol': protocol,
                                        'listener_port': listener_port,
                                        'domain': '',
                                        'instance_id': target.InstanceId if hasattr(target, 'InstanceId') else '',
                                        'ip': target.PrivateIpAddresses[0] if hasattr(target, 'PrivateIpAddresses') and target.PrivateIpAddresses else '',
                                        'port': target.Port if hasattr(target, 'Port') else '',
                                        'weight': target.Weight if hasattr(target, 'Weight') else ''
                                    })
        else:  # 传统负载均衡
            # 步骤1: 获取监听器信息
            listeners_req = models.DescribeClassicalLBListenersRequest()
            listeners_req.LoadBalancerId = lb_id

            try:
                # 发送请求获取监听器信息
                listeners_resp = client.DescribeClassicalLBListeners(listeners_req)

                # 检查是否有监听器
                if not hasattr(listeners_resp, 'Listeners') or not listeners_resp.Listeners:
                    current_app.logger.info(f"负载均衡 {lb_id} 没有监听器")
                    return jsonify({'backends': []})

                # 步骤2: 获取后端目标信息
                targets_req = models.DescribeClassicalLBTargetsRequest()
                targets_req.LoadBalancerId = lb_id

                # 发送请求获取后端目标信息
                targets_resp = client.DescribeClassicalLBTargets(targets_req)

                # 检查是否有后端目标
                if not hasattr(targets_resp, 'Targets') or not targets_resp.Targets:
                    current_app.logger.info(f"负载均衡 {lb_id} 没有绑定后端服务")
                    return jsonify({'backends': []})

                # 步骤3: 合并监听器和后端目标信息
                # 遍历所有监听器
                for listener in listeners_resp.Listeners:
                    # 提取监听器信息
                    protocol = listener.Protocol if hasattr(listener, 'Protocol') else ''
                    listener_port = listener.ListenerPort if hasattr(listener, 'ListenerPort') else ''
                    instance_port = listener.InstancePort if hasattr(listener, 'InstancePort') else ''

                    # 遍历所有后端目标，为每个目标添加监听器信息
                    for target in targets_resp.Targets:
                        backends.append({
                            'protocol': protocol,
                            'listener_port': listener_port,
                            'domain': '',  # 传统负载均衡没有域名信息
                            'instance_id': target.InstanceId if hasattr(target, 'InstanceId') else '',
                            'ip': target.PrivateIpAddresses[0] if hasattr(target, 'PrivateIpAddresses') and target.PrivateIpAddresses else '',
                            'port': instance_port,  # 使用监听器的实例端口
                            'weight': target.Weight if hasattr(target, 'Weight') else ''
                        })

            except Exception as e:
                current_app.logger.error(f"获取传统负载均衡信息时出错: {str(e)}")
                # 如果获取监听器信息失败，回退到原来的方式
                targets_req = models.DescribeClassicalLBTargetsRequest()
                targets_req.LoadBalancerId = lb_id

                # 发送请求
                targets_resp = client.DescribeClassicalLBTargets(targets_req)

                # 处理响应
                if not hasattr(targets_resp, 'Targets') or not targets_resp.Targets:
                    current_app.logger.info(f"负载均衡 {lb_id} 没有绑定后端服务")
                    return jsonify({'backends': []})

                # 传统负载均衡没有协议和域名信息，保持一致的数据结构
                for target in targets_resp.Targets:
                    backends.append({
                        'protocol': '',
                        'listener_port': '',
                        'domain': '',
                        'instance_id': target.InstanceId if hasattr(target, 'InstanceId') else '',
                        'ip': target.PrivateIpAddresses[0] if hasattr(target, 'PrivateIpAddresses') and target.PrivateIpAddresses else '',
                        'port': '',  # 传统负载均衡可能没有端口信息
                        'weight': target.Weight if hasattr(target, 'Weight') else ''
                    })

        # 记录响应日志
        current_app.logger.info(f"获取负载均衡后端服务成功: client_ip={client_ip}, user_id={user_id}, region={region}, lb_id={lb_id}, count={len(backends)}")

        return jsonify({'backends': backends})
    except Exception as e:
        client_ip = request.remote_addr
        user_id = g.user.id if hasattr(g, 'user') else 'unknown'
        current_app.logger.error(f"获取负载均衡后端服务时出错: client_ip={client_ip}, user_id={user_id}, region={region}, lb_id={lb_id}, error={str(e)}")
        return jsonify({'error': f'获取负载均衡后端服务时出错: {str(e)}'}), 500

@load_balancer.route('/load-balancers/by-backend-ip', methods=['GET'])
def get_load_balancers_by_backend_ip():
    """
    通过后端绑定的IP查询负载均衡信息
    """
    try:
        # 获取查询参数
        ip = request.args.get('ip', '')

        # 记录请求日志
        client_ip = request.remote_addr
        user_id = g.user.id if hasattr(g, 'user') else 'unknown'
        current_app.logger.info(f"通过后端IP查询负载均衡: client_ip={client_ip}, user_id={user_id}, backend_ip={ip}")

        if not ip:
            current_app.logger.warning(f"通过后端IP查询负载均衡失败: client_ip={client_ip}, user_id={user_id}, error=缺少IP参数")
            return jsonify({'error': '缺少IP参数'}), 400

        # 获取腾讯云API密钥
        secret_id, secret_key = get_tencent_credentials()
        if not secret_id or not secret_key:
            current_app.logger.error(f"通过后端IP查询负载均衡失败: client_ip={client_ip}, user_id={user_id}, backend_ip={ip}, error=未配置腾讯云API密钥")
            return jsonify({'error': '未配置腾讯云API密钥'}), 400

        # 获取区域列表
        regions = get_tencent_regions()
        if not regions:
            current_app.logger.error(f"通过后端IP查询负载均衡失败: client_ip={client_ip}, user_id={user_id}, backend_ip={ip}, error=未配置腾讯云区域信息")
            return jsonify({'error': '未配置腾讯云区域信息'}), 400

        # 导入腾讯云SDK
        try:
            from tencentcloud.common import credential
            from tencentcloud.common.profile.client_profile import ClientProfile
            from tencentcloud.common.profile.http_profile import HttpProfile
            from tencentcloud.clb.v20180317 import clb_client, models
        except ImportError:
            current_app.logger.error(f"通过后端IP查询负载均衡失败: client_ip={client_ip}, user_id={user_id}, backend_ip={ip}, error=未安装腾讯云SDK")
            return jsonify({'error': '未安装腾讯云SDK，请执行 pip install tencentcloud-sdk-python'}), 500

        # 创建认证对象
        cred = credential.Credential(secret_id, secret_key)

        # 遍历所有区域查询
        result_lbs = []

        for region in regions:
            try:
                current_app.logger.info(f"在区域 {region} 中查询绑定IP {ip} 的负载均衡")

                # 创建客户端配置
                http_profile = HttpProfile()
                http_profile.endpoint = "clb.tencentcloudapi.com"
                client_profile = ClientProfile()
                client_profile.httpProfile = http_profile

                # 创建CLB客户端
                client = clb_client.ClbClient(cred, region, client_profile)

                # 创建请求对象
                req = models.DescribeLoadBalancersRequest()
                req.BackendPrivateIps = [ip]

                # 发送请求
                resp = client.DescribeLoadBalancers(req)

                # 处理响应
                if hasattr(resp, 'LoadBalancerSet') and resp.LoadBalancerSet and resp.TotalCount > 0:
                    current_app.logger.info(f"在区域 {region} 中找到 {resp.TotalCount} 个绑定IP {ip} 的负载均衡")

                    for lb in resp.LoadBalancerSet:
                        lb_info = {
                            'loadbalancer_id': lb.LoadBalancerId,
                            'lb_name': lb.LoadBalancerName,
                            'region': region
                        }

                        # 根据IP版本提取VIP
                        if hasattr(lb, 'LoadBalancerVips') and lb.LoadBalancerVips:
                            lb_info['vip'] = lb.LoadBalancerVips[0]
                        elif hasattr(lb, 'AddressIPv6') and lb.AddressIPv6:
                            lb_info['vip'] = lb.AddressIPv6

                        result_lbs.append(lb_info)

                    # 如果找到了结果，就不再继续查询其他区域
                    if result_lbs:
                        break
            except Exception as e:
                current_app.logger.error(f"在区域 {region} 查询IP {ip} 的负载均衡时出错: {str(e)}")
                continue

        # 记录响应日志
        current_app.logger.info(f"通过后端IP查询负载均衡成功: client_ip={client_ip}, user_id={user_id}, backend_ip={ip}, count={len(result_lbs)}")

        return jsonify({'load_balancers': result_lbs})
    except Exception as e:
        client_ip = request.remote_addr
        user_id = g.user.id if hasattr(g, 'user') else 'unknown'
        current_app.logger.error(f"通过后端IP查询负载均衡时出错: client_ip={client_ip}, user_id={user_id}, backend_ip={ip}, error={str(e)}")
        return jsonify({'error': f'通过后端IP查询负载均衡时出错: {str(e)}'}), 500


@load_balancer.route('/load-balancers/<int:lb_id>/offline', methods=['PUT'])
@log_route
@log_operation('下线负载均衡')
def offline_load_balancer(lb_id):
    """
    下线负载均衡资源

    Path Parameters:
        lb_id (int): 负载均衡资源ID

    Returns:
        JSON: 下线结果
    """
    # 检查请求体是否为JSON格式（如果有请求体）
    if request.data and request.content_type and 'application/json' in request.content_type:
        try:
            _ = request.get_json()
        except Exception as e:
            current_app.logger.error(f"JSON解析错误: {str(e)}")
            return jsonify({'error': f'请求体JSON格式错误: {str(e)}'}), 400

    # 查找负载均衡
    lb = LoadBalancerResource.query.get_or_404(lb_id)

    # 检查负载均衡是否已经下线
    if lb.lb_status == 'offline':
        return jsonify({'message': '负载均衡已经是下线状态'}), 400

    try:
        # 记录变更前数据
        before_data = {
            'loadbalancer_id': lb.loadbalancer_id,
            'lb_name': lb.lb_name,
            'lb_status': lb.lb_status,
            'offline_time': lb.offline_time.strftime('%Y-%m-%d %H:%M:%S') if lb.offline_time else None
        }

        # 更新负载均衡状态为下线
        lb.lb_status = 'offline'
        lb.offline_time = datetime.now(beijing_tz).replace(tzinfo=None)

        # 解除与应用环境的绑定关系
        from app.models import ApplicationEnvLb
        env_lbs = ApplicationEnvLb.query.filter_by(lb_id=lb.id).all()
        for env_lb in env_lbs:
            db.session.delete(env_lb)

        # 记录变更历史
        history = ResourceChangeHistory(
            user_id=g.user.id if hasattr(g, 'user') else 1,
            resource_type='load_balancer',
            resource_id=lb.id,
            operation_type=OperationType.UPDATE.value,
            before_data=json.dumps(before_data),
            after_data=json.dumps({
                'lb_status': lb.lb_status,
                'offline_time': lb.offline_time.strftime('%Y-%m-%d %H:%M:%S')
            })
        )
        db.session.add(history)

        db.session.commit()

        return jsonify({'message': '负载均衡已成功下线'})

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"下线负载均衡时出错: {str(e)}")
        return jsonify({'error': f'下线负载均衡时出错: {str(e)}'}), 500

