# 数据库模型

本文档描述了ICMS2应用程序中使用的数据库模型。

## 概述

ICMS2使用SQLAlchemy作为ORM（对象关系映射）与MySQL数据库交互。模型在`app/models.py`中定义。

## 实体关系图

```
┌─────────┐       ┌───────────┐       ┌────────┐
│  User   │       │ UserRole  │       │  Role  │
├─────────┤       ├───────────┤       ├────────┤
│ id      │───┐   │ user_id   │◄──────│ id     │
│ username│   └──►│ user_type │       │ name   │
│ display_│       │ role_id   │◄─┐    │ desc   │
│ email   │       └───────────┘  │    └────────┘
└─────────┘                      │         │
                                 └─────────┘
┌──────────┐                           │
│LocalUser │                           │
├──────────┤      ┌────────────┐       │
│ id       │      │RoleModule  │       │
│ username │      ├────────────┤       │
│ password │      │ role_id    │◄──────┘
│ display_ │      │ module_id  │◄─┐
│ email    │      │ menu_id    │  │
└──────────┘      └────────────┘  │
                                   │
┌─────────┐                        │    ┌──────┐
│  Ldap   │       ┌────────┐       │    │ Menu │
├─────────┤       │ Module │       │    ├──────┤
│ id      │       ├────────┤       │    │ id   │
│ server  │       │ id     │◄──────┘    │ name │
│ port    │       │ name   │            │ disp │
│ bind_dn │       │ display│            │ mod_id│
│ password│       │ desc   │◄───────────┘
│ ou_path │       └────────┘
└─────────┘
```

## 模型定义

### User

表示系统中的LDAP用户。

| 字段         | 类型         | 描述                       |
|--------------|--------------|----------------------------|
| id           | BigInteger   | 主键                       |
| username     | String(64)   | 唯一用户名                 |
| display_name | String(128)  | 用户显示名称               |
| email        | String(128)  | 用户电子邮件地址           |
| is_active    | Boolean      | 用户是否激活               |
| create_time  | DateTime     | 用户创建时间               |

### LocalUser

表示本地认证的用户。

| 字段          | 类型         | 描述                       |
|---------------|--------------|----------------------------|
| id            | BigInteger   | 主键                       |
| username      | String(64)   | 唯一用户名                 |
| password_hash | String(256)  | 哈希密码                   |
| display_name  | String(128)  | 用户显示名称               |
| email         | String(128)  | 用户电子邮件地址           |
| is_active     | Boolean      | 用户是否激活               |
| create_time   | DateTime     | 用户创建时间               |
| update_time   | DateTime     | 用户最后更新时间           |

### Role

表示用于权限管理的用户角色。

| 字段        | 类型         | 描述                       |
|-------------|--------------|----------------------------|
| id          | BigInteger   | 主键                       |
| role_name   | String(64)   | 唯一角色名称               |
| description | String(255)  | 角色描述                   |

### Module

表示应用程序模块。

| 字段         | 类型         | 描述                       |
|--------------|--------------|----------------------------|
| id           | BigInteger   | 主键                       |
| module_name  | String(64)   | 唯一模块名称               |
| display_name | String(64)   | 模块显示名称               |
| description  | String(255)  | 模块描述                   |

### Menu

表示模块内的菜单项。

| 字段              | 类型         | 描述                       |
|-------------------|--------------|----------------------------|
| id                | BigInteger   | 主键                       |
| menu_name         | String(100)  | 菜单名称                   |
| menu_display_name | String(100)  | 菜单显示名称               |
| module_id         | BigInteger   | 外键，关联到Module.id      |

### UserRole

表示用户和角色之间的多对多关系。

| 字段      | 类型         | 描述                                |
|-----------|--------------|-------------------------------------|
| user_id   | BigInteger   | 主键，外键，关联到User.id           |
| user_type | Enum         | 用户类型（'ldap'或'local'）         |
| role_id   | BigInteger   | 主键，外键，关联到Role.id           |

### RoleModule

表示角色和模块/菜单之间的多对多关系。

| 字段      | 类型         | 描述                                |
|-----------|--------------|-------------------------------------|
| role_id   | BigInteger   | 主键，外键，关联到Role.id           |
| module_id | String(256)  | 逗号分隔的模块ID                    |
| menu_id   | String(256)  | 逗号分隔的菜单ID                    |

### Ldap

表示LDAP服务器配置。

| 字段        | 类型         | 描述                       |
|-------------|--------------|----------------------------|
| id          | BigInteger   | 主键                       |
| server      | String(255)  | LDAP服务器地址             |
| port        | Integer      | LDAP服务器端口             |
| bind_dn     | String(255)  | LDAP绑定DN                 |
| password    | String(255)  | LDAP绑定密码               |
| ou_path     | String(255)  | LDAP组织单位路径           |
| is_active   | Boolean      | 配置是否激活               |
| create_time | DateTime     | 配置创建时间               |
| update_time | DateTime     | 配置更新时间               |

## 关系

- **User到Role**：通过UserRole表的多对多关系
- **LocalUser到Role**：通过UserRole表的多对多关系
- **Role到Module**：通过RoleModule表的多对多关系
- **Module到Menu**：一对多关系

## 辅助方法

### RoleModule

- `list_to_str(id_list)`：将ID列表转换为逗号分隔的字符串
- `str_to_list(id_str)`：将逗号分隔的字符串转换为ID列表

## 实用函数

- `get_beijing_time()`：返回北京时区的当前时间
