"""
日志检查脚本，用于检查日志记录是否正确
"""
import os
import re
import sys
import argparse
from datetime import datetime, timed<PERSON><PERSON>

def parse_log_line(line):
    """
    解析日志行，提取时间戳、日志级别、IP地址、用户信息和消息内容
    
    Args:
        line (str): 日志行
        
    Returns:
        dict: 解析结果，包含时间戳、日志级别、IP地址、用户信息和消息内容
    """
    # 匹配日志格式：2023-07-01 12:34:56,789 [INFO] [*************] [用户:username(123)] 消息内容 [in 文件路径:行号]
    pattern = r'(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2},\d{3}) \[(\w+)\] \[([^\]]+)\] \[用户:([^\]]+)\] (.*?) \[in ([^:]+):(\d+)\]'
    match = re.match(pattern, line)
    
    if match:
        timestamp_str, level, ip, user_info, message, file_path, line_number = match.groups()
        
        # 解析用户信息
        user_pattern = r'([^(]+)\(([^)]+)\)'
        user_match = re.match(user_pattern, user_info)
        
        if user_match:
            username, user_id = user_match.groups()
        else:
            username = user_info
            user_id = 'unknown'
        
        return {
            'timestamp': datetime.strptime(timestamp_str, '%Y-%m-%d %H:%M:%S,%f'),
            'level': level,
            'ip': ip,
            'username': username,
            'user_id': user_id,
            'message': message,
            'file_path': file_path,
            'line_number': int(line_number)
        }
    
    return None

def check_logs(log_file, days=1, check_unknown=True):
    """
    检查日志文件，统计日志记录情况
    
    Args:
        log_file (str): 日志文件路径
        days (int): 检查最近几天的日志
        check_unknown (bool): 是否检查未知用户的日志
        
    Returns:
        dict: 统计结果
    """
    if not os.path.exists(log_file):
        print(f"错误：日志文件 {log_file} 不存在")
        return None
    
    # 计算起始时间
    start_time = datetime.now() - timedelta(days=days)
    
    # 统计结果
    stats = {
        'total': 0,
        'by_level': {},
        'by_user': {},
        'unknown_users': 0,
        'unknown_user_logs': []
    }
    
    with open(log_file, 'r', encoding='utf-8') as f:
        for line in f:
            line = line.strip()
            if not line:
                continue
            
            log_data = parse_log_line(line)
            if not log_data:
                continue
            
            # 检查日志时间
            if log_data['timestamp'] < start_time:
                continue
            
            stats['total'] += 1
            
            # 按日志级别统计
            level = log_data['level']
            stats['by_level'][level] = stats['by_level'].get(level, 0) + 1
            
            # 按用户统计
            username = log_data['username']
            user_id = log_data['user_id']
            user_key = f"{username}({user_id})"
            stats['by_user'][user_key] = stats['by_user'].get(user_key, 0) + 1
            
            # 检查未知用户
            if check_unknown and (username.lower() in ['unknown', 'n/a'] or user_id.lower() in ['unknown', 'n/a']):
                stats['unknown_users'] += 1
                stats['unknown_user_logs'].append({
                    'timestamp': log_data['timestamp'].strftime('%Y-%m-%d %H:%M:%S'),
                    'level': level,
                    'ip': log_data['ip'],
                    'message': log_data['message'],
                    'file_path': log_data['file_path'],
                    'line_number': log_data['line_number']
                })
    
    return stats

def print_stats(stats):
    """
    打印统计结果
    
    Args:
        stats (dict): 统计结果
    """
    if not stats:
        return
    
    print(f"总日志数：{stats['total']}")
    print("\n按日志级别统计：")
    for level, count in sorted(stats['by_level'].items()):
        print(f"  {level}: {count} ({count / stats['total'] * 100:.2f}%)")
    
    print("\n按用户统计（前10名）：")
    sorted_users = sorted(stats['by_user'].items(), key=lambda x: x[1], reverse=True)
    for user, count in sorted_users[:10]:
        print(f"  {user}: {count} ({count / stats['total'] * 100:.2f}%)")
    
    print(f"\n未知用户日志数：{stats['unknown_users']} ({stats['unknown_users'] / stats['total'] * 100:.2f}%)")
    
    if stats['unknown_users'] > 0:
        print("\n未知用户日志示例（最多10条）：")
        for i, log in enumerate(stats['unknown_user_logs'][:10]):
            print(f"  {i+1}. {log['timestamp']} [{log['level']}] {log['message']} [in {log['file_path']}:{log['line_number']}]")

def main():
    """
    主函数
    """
    parser = argparse.ArgumentParser(description='检查日志记录是否正确')
    parser.add_argument('log_file', help='日志文件路径')
    parser.add_argument('--days', type=int, default=1, help='检查最近几天的日志，默认为1天')
    parser.add_argument('--no-check-unknown', action='store_true', help='不检查未知用户的日志')
    
    args = parser.parse_args()
    
    stats = check_logs(args.log_file, args.days, not args.no_check_unknown)
    if stats:
        print_stats(stats)

if __name__ == '__main__':
    main()
