"""
更新系统管理菜单
"""
from app import create_app, db
from app.models import Module, <PERSON><PERSON>

def update_system_management_menu():
    """
    更新系统管理菜单，确保系统管理功能在基础数据管理模块下可用
    """
    app = create_app()
    with app.app_context():
        # 查找基础数据管理模块
        basic_data_module = Module.query.filter_by(module_name='basic_data').first()
        if not basic_data_module:
            print("未找到基础数据管理模块，无法更新菜单")
            return
        
        # 查找系统管理菜单
        system_management_menu = Menu.query.filter_by(
            menu_name='system_management',
            module_id=basic_data_module.id
        ).first()
        
        if system_management_menu:
            # 菜单已存在，更新显示名称
            system_management_menu.menu_display_name = '系统管理'
            db.session.commit()
            print("系统管理菜单已更新")
        else:
            # 创建新菜单
            new_menu = Menu(
                menu_name='system_management',
                menu_display_name='系统管理',
                module_id=basic_data_module.id
            )
            db.session.add(new_menu)
            db.session.commit()
            print("系统管理菜单已创建")

if __name__ == "__main__":
    update_system_management_menu()
