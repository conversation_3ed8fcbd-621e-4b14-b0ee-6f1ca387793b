# 下线接口HTTP方法说明

本文档说明了系统中各种资源下线接口的HTTP方法，确保前后端接口调用的一致性。

## 接口方法总览

| 资源类型 | 接口URL | HTTP方法 | 说明 |
|---------|---------|----------|------|
| 服务器 | `/api/servers/<server_id>` | PUT 或 DELETE | 支持两种方法，兼容前端不同的调用方式 |
| 负载均衡 | `/api/load-balancers/<lb_id>/offline` | PUT | 使用PUT方法 |
| 数据库 | `/api/databases/<db_id>/offline` | PUT | 使用PUT方法 |
| 应用环境 | `/api/application-environments/<app_env_id>/offline` | PUT | 使用PUT方法 |

## 详细说明

### 1. 服务器下线接口

**URL**: `/api/servers/<server_id>`
**方法**: PUT 或 DELETE
**特殊说明**:
- 为了兼容前端可能使用的不同HTTP方法，同时支持PUT和DELETE
- 只能下线手动录入的服务器（original_source='manual'）
- 腾讯云同步的服务器不能通过此接口下线

### 2. 负载均衡下线接口

**URL**: `/api/load-balancers/<lb_id>/offline`
**方法**: PUT
**特殊说明**:
- 使用PUT方法，符合RESTful API设计规范
- 可以下线任何来源的负载均衡资源

### 3. 数据库下线接口

**URL**: `/api/databases/<db_id>/offline`
**方法**: PUT
**特殊说明**:
- 使用PUT方法，符合RESTful API设计规范
- 可以下线任何来源的数据库资源

### 4. 应用环境下线接口

**URL**: `/api/application-environments/<app_env_id>/offline`
**方法**: PUT
**特殊说明**:
- 使用PUT方法，符合RESTful API设计规范
- 下线时会自动解除与所有资源的绑定关系

## 前端调用建议

### 推荐做法
1. **统一使用PUT方法**：所有下线操作建议使用PUT方法，保持接口调用的一致性
2. **错误处理**：确保前端能正确处理各种错误响应（400、403、404、500等）
3. **确认机制**：重要的下线操作建议添加用户确认机制

### 示例代码

```javascript
// 推荐的前端调用方式
async function offlineResource(resourceType, resourceId) {
    const urls = {
        'server': `/api/servers/${resourceId}`,
        'load_balancer': `/api/load-balancers/${resourceId}/offline`,
        'database': `/api/databases/${resourceId}/offline`,
        'application_environment': `/api/application-environments/${resourceId}/offline`
    };

    try {
        const response = await fetch(urls[resourceType], {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({})  // 发送空JSON对象
        });

        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.error || errorData.message || `HTTP error! status: ${response.status}`);
        }

        const result = await response.json();
        console.log('下线成功:', result.message);
        return result;
    } catch (error) {
        console.error('下线失败:', error);
        throw error;
    }
}

// 简化版本（不发送请求体）
async function offlineResourceSimple(resourceType, resourceId) {
    const urls = {
        'server': `/api/servers/${resourceId}`,
        'load_balancer': `/api/load-balancers/${resourceId}/offline`,
        'database': `/api/databases/${resourceId}/offline`,
        'application_environment': `/api/application-environments/${resourceId}/offline`
    };

    try {
        const response = await fetch(urls[resourceType], {
            method: 'PUT'
            // 不发送请求体和Content-Type头
        });

        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.error || errorData.message || `HTTP error! status: ${response.status}`);
        }

        const result = await response.json();
        console.log('下线成功:', result.message);
        return result;
    } catch (error) {
        console.error('下线失败:', error);
        throw error;
    }
}
```

## 注意事项

1. **请求体要求**：
   - 如果发送请求体，必须是有效的JSON格式
   - 可以发送空对象`{}`或者不发送请求体
   - 接口会自动检查JSON格式的有效性
2. **Content-Type**：如果发送请求体，建议设置`Content-Type: application/json`请求头
3. **权限检查**：确保用户有相应的操作权限
4. **状态检查**：接口会检查资源当前状态，避免重复下线
5. **关联处理**：下线操作会自动处理相关的绑定关系
6. **JSON解析错误处理**：如果请求体JSON格式错误，会返回400错误并提示具体错误信息

## 错误处理

### 常见错误码
- **400**: 资源已经是下线状态、请求参数错误、JSON格式错误
- **403**: 权限不足（如尝试下线腾讯云同步的服务器）
- **404**: 资源不存在
- **500**: 服务器内部错误

### 错误响应示例

**权限错误**：
```json
{
  "error": "只能下线手动录入的服务器"
}
```

**状态错误**：
```json
{
  "message": "服务器已经是下线状态"
}
```

**JSON格式错误**：
```json
{
  "error": "请求体JSON格式错误: Expecting value: line 1 column 1 (char 0)"
}
```

## 更新历史

- **2025-05-23**: 修复JSON解析错误问题，增加请求体格式检查
- **2025-05-23**: 修复服务器下线接口，同时支持PUT和DELETE方法
- **2025-05-23**: 统一其他资源下线接口使用PUT方法
- **2025-05-23**: 更新API文档，明确各接口的HTTP方法要求和JSON格式要求
