# 服务器资源管理 API 文档

本文档描述了服务器资源管理模块（`server_resource.py`）中的所有 API 接口。

## 目录

- [获取服务器列表](#获取服务器列表)
- [创建服务器](#创建服务器)
- [同步腾讯云服务器](#同步腾讯云服务器)
- [下线服务器](#下线服务器)

## 获取服务器列表

获取系统中的服务器资源列表，支持多种过滤条件和分页。

**请求方式**：GET

**URL**：`/api/servers`

**查询参数**：

| 参数名    | 类型    | 必填 | 描述                         |
|-----------|---------|------|------------------------------|
| hostname  | string  | 否   | 主机名关键字，用于模糊查询   |
| ip        | string  | 否   | IP地址关键字，用于模糊查询   |
| remark    | string  | 否   | 备注关键字，用于模糊查询     |
| page      | integer | 否   | 页码，默认为1                |
| per_page  | integer | 否   | 每页记录数，默认为20         |

**响应示例**：
```json
{
  "total": 125,
  "page": 1,
  "per_page": 20,
  "pages": 7,
  "servers": [
    {
      "id": 1,
      "instance_id": "ins-a1b2c3d4",
      "hostname": "web-server-01",
      "ipv4": "********",
      "public_ip": "***********",
      "os": "CentOS 7.9",
      "cpu": "4核",
      "memory": "8GB",
      "region": "ap-guangzhou",
      "availability_zone": "ap-guangzhou-3",
      "vpc_id": "vpc-a1b2c3d4",
      "subnet_id": "subnet-e5f6g7h8",
      "create_time": "2023-05-15 10:30:45",
      "original_source": "tencent",
      "remark": "生产环境Web服务器",
      "vm_creat_time": "2023-05-10 08:15:30",
      "physical_host_id": "host-i9j0k1l2",
      "server_status": "online",
      "offline_time": null
    },
    // 更多服务器记录...
  ]
}
```

**说明**：
- 返回服务器列表及分页信息
- 支持按主机名、IP地址（包括内网IP和公网IP）和备注进行模糊查询
- 结果按创建时间降序排列
- 分页信息包括总记录数、当前页码、每页记录数和总页数
- 返回完整的服务器字段信息，包括server_status和offline_time

## 创建服务器

手动录入服务器资源信息。

**请求方式**：POST

**URL**：`/api/servers`

**请求体**：
```json
{
  "hostname": "app-server-01",
  "ipv4": "********5",
  "public_ip": "************",
  "cpu": "4核",
  "memory": "8GB",
  "os": "CentOS 7.9",
  "region": "ap-guangzhou",
  "availability_zone": "ap-guangzhou-3",
  "vpc_id": "vpc-a1b2c3d4",
  "subnet_id": "subnet-e5f6g7h8",
  "original_source": "manual",
  "remark": "测试环境应用服务器",
  "physical_host_id": "host-i9j0k1l2"
}
```

**请求参数**：

| 参数名            | 类型   | 必填 | 描述                     |
|-------------------|--------|------|--------------------------|
| hostname          | string | 是   | 主机名                   |
| ipv4              | string | 是   | IPv4地址                 |
| cpu               | string | 是   | CPU信息                  |
| memory            | string | 是   | 内存信息                 |
| os                | string | 是   | 操作系统                 |
| region            | string | 是   | 地域                     |
| public_ip         | string | 否   | 公网IP                   |
| availability_zone | string | 否   | 可用区                   |
| vpc_id            | string | 否   | VPC ID                   |
| subnet_id         | string | 否   | 子网ID                   |
| original_source   | string | 否   | 来源，默认为"manual"     |
| remark            | string | 否   | 备注                     |
| physical_host_id  | string | 否   | 物理主机ID               |

**响应示例**：
```json
{
  "message": "服务器创建成功",
  "id": 126
}
```

**错误响应**：
```json
{
  "error": "缺少必填字段: hostname"
}
```

**说明**：
- 创建服务器记录并返回创建结果
- 必填字段包括：hostname、ipv4、cpu、memory、os、region
- 如果缺少必填字段，将返回400错误
- 创建成功后会自动记录变更历史
- 默认来源为"manual"，表示手动录入
- 手动录入的主机默认状态为online
- 如果未提供vm_creat_time，将使用当前时间

## 同步腾讯云服务器

从腾讯云API同步服务器资源信息，采用增量同步方式。

**请求方式**：POST

**URL**：`/api/servers/sync-tencent`

**请求头**：
```
Content-Type: application/json
```

**请求体**：
```json
{}
```

**重要说明**：
- 即使不需要传递数据，也必须发送一个空的JSON对象 `{}`
- 必须设置正确的 `Content-Type: application/json` 请求头，否则会出现 400 Bad Request 错误

**响应示例**：
```json
{
  "message": "腾讯云服务器同步完成",
  "stats": {
    "total": 45,
    "added": 5,
    "updated": 8,
    "unchanged": 32,
    "recycled": 3,
    "errors": 0,
    "error_details": []
  }
}
```

**说明**：
- `recycled` 字段表示在腾讯云中已不存在，被设置为下线状态的服务器数量
```

**错误响应**：
```json
{
  "error": "未配置腾讯云API密钥"
}
```

**说明**：
- 从腾讯云API同步服务器资源信息，采用增量同步方式
- 腾讯云同步的主机默认状态为online
- 通过分页机制获取所有服务器数据，确保不遗漏任何资源
- 对于在数据库中存在但在腾讯云API返回结果中不存在的主机，会被标记为offline状态
- 离线状态判断机制会考虑腾讯云返回的分页数据，确保检查所有页面的数据后再做判断
- 下线的主机会自动解除与应用环境的绑定关系，并保存到回收站
- 需要预先配置腾讯云API密钥和区域信息
- 同步过程会自动处理新增、更新、保持不变和已回收的记录
- 手动录入的服务器（original_source不为'tencent'）不受同步影响
- 返回同步统计信息，包括总记录数、新增记录数、更新记录数、未变更记录数、回收记录数和错误数
- 如果发生错误，会记录详细的错误信息
- 同步过程会自动记录资源变更历史
- 需要安装腾讯云SDK（tencentcloud-sdk-python）

## 下线服务器

将手动录入的服务器资源设置为下线状态。

**请求方式**：PUT 或 DELETE

**URL**：`/api/servers/<server_id>`

**URL参数**：

| 参数名    | 类型    | 必填 | 描述       |
|-----------|---------|------|------------|
| server_id | integer | 是   | 服务器ID   |

**请求头**：
```
Content-Type: application/json
```

**请求体**：
```json
{}
```

**重要说明**：
- 即使不需要传递数据，也必须发送一个空的JSON对象 `{}`
- 必须设置正确的 `Content-Type: application/json` 请求头，否则会出现 400 Bad Request 错误

**响应示例**：
```json
{
  "message": "服务器已成功下线"
}
```

**错误响应**：
```json
{
  "error": "只能下线手动录入的服务器"
}
```

或

```json
{
  "message": "服务器已经是下线状态"
}
```

或

```json
{
  "error": "下线服务器时出错: [错误详情]"
}
```

**说明**：
- 只允许下线来源为"manual"的服务器（手动录入的服务器）
- 将服务器状态设置为"offline"，并记录下线时间
- 下线操作会自动解除该服务器与所有应用环境的绑定关系
- 下线的服务器信息会被保存到回收站（recycled_host表）
- 下线操作会被记录到资源变更历史（resource_change_history表）
- 如果尝试下线非手动录入的服务器，将返回403错误
- 如果服务器已经是下线状态，将返回400错误
- 如果服务器不存在，将返回404错误
- 如果下线过程中出现异常，将返回500错误


