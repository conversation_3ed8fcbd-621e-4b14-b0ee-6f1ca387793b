# 认证系统

本文档描述了ICMS2中使用的认证系统。

## 概述

ICMS2支持两种认证方法：

1. **LDAP认证**：适用于具有现有LDAP目录的企业环境
2. **本地认证**：适用于独立部署或管理员账户

两种方法都使用JWT（JSON Web令牌）来维护会话状态。

## 认证流程

### LDAP认证流程

```
┌─────────┐                  ┌─────────┐                  ┌─────────┐
│ 客户端  │                  │  ICMS2  │                  │  LDAP   │
│         │                  │ 服务器  │                  │ 服务器  │
└────┬────┘                  └────┬────┘                  └────┬────┘
     │                            │                            │
     │ 登录请求                   │                            │
     │ (用户名, 密码)             │                            │
     │ -------------------------► │                            │
     │                            │                            │
     │                            │ LDAP绑定请求               │
     │                            │ (用户名, 密码)             │
     │                            │ -------------------------► │
     │                            │                            │
     │                            │ 认证结果                   │
     │                            │ ◄------------------------- │
     │                            │                            │
     │                            │ 如果成功：                 │
     │                            │ 1. 创建/更新用户           │
     │                            │ 2. 生成JWT令牌             │
     │                            │                            │
     │ 登录响应                   │                            │
     │ (JWT令牌, 用户信息)        │                            │
     │ ◄------------------------- │                            │
     │                            │                            │
     │ 后续请求                   │                            │
     │ (带JWT令牌)                │                            │
     │ -------------------------► │                            │
     │                            │ 验证令牌                   │
     │                            │                            │
     │ 响应                       │                            │
     │ ◄------------------------- │                            │
     │                            │                            │
```

### 本地认证流程

```
┌─────────┐                  ┌─────────┐
│ 客户端  │                  │  ICMS2  │
│         │                  │ 服务器  │
└────┬────┘                  └────┬────┘
     │                            │
     │ 登录请求                   │
     │ (用户名, 密码)             │
     │ -------------------------► │
     │                            │
     │                            │ 1. 验证密码哈希
     │                            │ 2. 生成JWT令牌
     │                            │
     │ 登录响应                   │
     │ (JWT令牌, 用户信息)        │
     │ ◄------------------------- │
     │                            │
     │ 后续请求                   │
     │ (带JWT令牌)                │
     │ -------------------------► │
     │                            │ 验证令牌
     │                            │
     │ 响应                       │
     │ ◄------------------------- │
     │                            │
```

## JWT令牌

### 结构

JWT令牌由三部分组成：

1. **头部**：包含令牌类型和签名算法
2. **载荷**：包含关于用户的声明
3. **签名**：确保令牌未被篡改

### 载荷内容

- `user_id`：已认证用户的ID
- `user_type`：用户类型（'ldap'或'local'）
- `exp`：过期时间戳（创建后24小时）

### 令牌处理

- 令牌通过以下方式包含在请求中：
  - HTTP授权头：`Authorization: Bearer <token>`
  - Cookie：`token=<token>`
- 每个请求都会验证令牌
- 即将过期的令牌（剩余时间不到30分钟）会自动刷新

## LDAP配置

### 多个LDAP服务器

ICMS2支持配置多个LDAP服务器。在认证过程中：

1. 系统按顺序尝试每个活动的LDAP配置
2. 如果某个配置成功认证用户，则过程停止
3. 如果所有配置都失败，则认证失败

### LDAP用户同步

当用户通过LDAP成功认证时：

1. 系统检查用户是否存在于本地数据库中
2. 如果不存在，则创建新的用户记录
3. 如果用户存在，则在必要时更新其信息

## 本地用户管理

本地用户存储在数据库中，包含：

- 用户名
- 密码哈希（使用Werkzeug的密码哈希功能）
- 显示名称
- 电子邮件
- 活动状态

## 安全考虑

### 密码存储

- 本地用户密码永远不会以明文形式存储
- 密码使用Werkzeug的安全函数进行哈希处理
- LDAP密码不存储，仅针对LDAP服务器进行验证

### 令牌安全

- 令牌使用密钥签名
- 在生产环境中，使用强随机密钥
- 令牌在24小时后过期
- 通过更改密钥可以使令牌失效

### LDAP安全

- LDAP服务器凭据存储在数据库中
- LDAP连接使用配置的绑定DN和密码
- 用户认证通过尝试使用用户凭据进行绑定来执行

## API接口

- `/api/login`：认证用户并返回JWT令牌
- `/api/validate-token`：验证JWT令牌并返回用户信息
- `/api/ldap-config`：管理LDAP服务器配置
