#!/usr/bin/env python3
"""
CVM创建模块测试脚本
"""
import requests
import json
import time
import sys

# 配置
BASE_URL = "http://localhost:5000/api"
TOKEN = ""  # 需要设置有效的JWT Token

def get_headers():
    """获取请求头"""
    return {
        'Content-Type': 'application/json',
        'Authorization': f'Bearer {TOKEN}' if TOKEN else ''
    }

def test_get_net_areas():
    """测试获取网络安全区列表"""
    print("=== 测试获取网络安全区列表 ===")
    
    url = f"{BASE_URL}/cvm/net-areas"
    response = requests.get(url, headers=get_headers())
    
    print(f"状态码: {response.status_code}")
    print(f"响应: {json.dumps(response.json(), ensure_ascii=False, indent=2)}")
    
    if response.status_code == 200:
        data = response.json().get('data', [])
        if data:
            return data[0]['id']  # 返回第一个网络安全区ID用于后续测试
    
    return None

def test_create_cvm(net_area_id):
    """测试创建CVM"""
    print("\n=== 测试创建CVM ===")
    
    url = f"{BASE_URL}/cvm/create"
    data = {
        "application_id": 1,
        "environment_id": 1,
        "app_en_name": "testapp",
        "env_en_name": "test",
        "net_area_id": net_area_id,
        "server_port": "8080,8081"
    }
    
    response = requests.post(url, headers=get_headers(), json=data)
    
    print(f"状态码: {response.status_code}")
    print(f"响应: {json.dumps(response.json(), ensure_ascii=False, indent=2)}")
    
    if response.status_code == 201:
        return response.json().get('data', {}).get('creation_record_id')
    
    return None

def test_get_status(record_id):
    """测试获取创建状态"""
    print(f"\n=== 测试获取创建状态 (记录ID: {record_id}) ===")
    
    url = f"{BASE_URL}/cvm/status/{record_id}"
    response = requests.get(url, headers=get_headers())
    
    print(f"状态码: {response.status_code}")
    print(f"响应: {json.dumps(response.json(), ensure_ascii=False, indent=2)}")
    
    if response.status_code == 200:
        return response.json().get('data', {}).get('creation_status')
    
    return None

def test_check_status(record_id):
    """测试检查CVM状态"""
    print(f"\n=== 测试检查CVM状态 (记录ID: {record_id}) ===")
    
    url = f"{BASE_URL}/cvm/check-status/{record_id}"
    response = requests.post(url, headers=get_headers())
    
    print(f"状态码: {response.status_code}")
    print(f"响应: {json.dumps(response.json(), ensure_ascii=False, indent=2)}")
    
    if response.status_code == 200:
        return response.json().get('data', {}).get('creation_status')
    
    return None

def test_finalize_cvm(record_id):
    """测试完成CVM创建流程"""
    print(f"\n=== 测试完成CVM创建流程 (记录ID: {record_id}) ===")
    
    url = f"{BASE_URL}/cvm/finalize/{record_id}"
    response = requests.post(url, headers=get_headers())
    
    print(f"状态码: {response.status_code}")
    print(f"响应: {json.dumps(response.json(), ensure_ascii=False, indent=2)}")
    
    return response.status_code == 200

def test_get_records():
    """测试获取创建记录列表"""
    print("\n=== 测试获取创建记录列表 ===")
    
    url = f"{BASE_URL}/cvm/records"
    params = {
        'page': 1,
        'per_page': 10
    }
    
    response = requests.get(url, headers=get_headers(), params=params)
    
    print(f"状态码: {response.status_code}")
    print(f"响应: {json.dumps(response.json(), ensure_ascii=False, indent=2)}")

def main():
    """主测试函数"""
    print("CVM创建模块测试开始")
    print("=" * 50)
    
    if not TOKEN:
        print("警告: 未设置TOKEN，可能会遇到认证错误")
    
    # 1. 测试获取网络安全区列表
    net_area_id = test_get_net_areas()
    if not net_area_id:
        print("获取网络安全区失败，无法继续测试")
        return
    
    # 2. 测试创建CVM
    record_id = test_create_cvm(net_area_id)
    if not record_id:
        print("创建CVM失败，无法继续测试")
        return
    
    # 3. 等待一段时间后检查状态
    print("\n等待5秒后检查状态...")
    time.sleep(5)
    
    # 4. 测试获取状态
    status = test_get_status(record_id)
    print(f"当前状态: {status}")
    
    # 5. 测试检查状态（主动更新）
    status = test_check_status(record_id)
    print(f"检查后状态: {status}")
    
    # 6. 如果状态为success，测试完成流程
    if status == 'success':
        test_finalize_cvm(record_id)
    else:
        print(f"状态为 {status}，跳过完成流程测试")
    
    # 7. 测试获取记录列表
    test_get_records()
    
    print("\n" + "=" * 50)
    print("CVM创建模块测试完成")

if __name__ == "__main__":
    if len(sys.argv) > 1:
        TOKEN = sys.argv[1]
        print(f"使用提供的TOKEN: {TOKEN[:20]}...")
    
    main()
