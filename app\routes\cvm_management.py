"""
腾讯云虚拟机管理模块
"""
from flask import Blueprint, request, jsonify, current_app, g
from sqlalchemy import or_, and_, func
from datetime import datetime
import json
import time
import re
from .. import db, beijing_tz
from ..models import (
    NetAreaBase, EnvNetConf, CreatCvmHostname, CvmCreationRecord,
    Application, EnvironmentBase, ApplicationEnvironment, ApplicationEnvServer,
    ServerResource, ResourceChangeHistory, OperationType
)
from ..utils.decorators import log_route, log_operation
from ..utils.user_utils import get_current_user_id
from ..utils.cloud_api import get_tencent_credentials
from ..utils.tencent_cvm import create_cvm_client

# 创建蓝图
cvm_management = Blueprint('cvm_management', __name__)

def get_region_from_placement(placement):
    """
    根据placement判断Region参数
    """
    if 'ap-shanghai-fsi' in placement:
        return 'ap-shanghai-fsi'
    elif 'ap-shenzhen-fsi' in placement:
        return 'ap-shenzhen-fsi'
    else:
        # 默认返回上海金融区
        return 'ap-shanghai-fsi'

def get_next_hostname(env_en_name):
    """
    获取下一个主机名并更新数据库
    """
    try:
        # 根据环境英文名确定nscode
        if env_en_name in ['dr', 'cow']:
            nscode = 'htsz'
        else:
            nscode = 'htsh'

        # 查询当前主机名
        hostname_record = CreatCvmHostname.query.filter_by(nscode=nscode).first()
        if not hostname_record:
            current_app.logger.error(f"未找到nscode为{nscode}的主机名记录")
            return None

        current_hostname = hostname_record.current_hostname
        if not current_hostname:
            current_app.logger.error(f"nscode为{nscode}的当前主机名为空")
            return None

        # 提取数字部分并加1
        match = re.search(r'(\d+)$', current_hostname)
        if not match:
            current_app.logger.error(f"无法从主机名{current_hostname}中提取数字")
            return None

        current_number = int(match.group(1))
        new_number = current_number + 1

        # 构造新的主机名
        prefix = current_hostname[:match.start()]
        new_hostname = f"{prefix}{new_number:06d}"  # 保持6位数字格式

        # 更新数据库
        hostname_record.current_hostname = new_hostname
        db.session.commit()

        return current_hostname  # 返回当前使用的主机名

    except Exception as e:
        current_app.logger.error(f"获取下一个主机名时出错: {str(e)}")
        db.session.rollback()
        return None

@cvm_management.route('/net-areas', methods=['GET'])
@log_route
def get_net_areas():
    """
    获取网络安全区列表
    """
    try:
        net_areas = NetAreaBase.query.all()
        result = []
        for area in net_areas:
            result.append({
                'id': area.id,
                'net_area_name': area.net_area_name,
                'net_area_des': area.net_area_des
            })

        return jsonify({
            'message': '获取网络安全区列表成功',
            'data': result
        }), 200

    except Exception as e:
        current_app.logger.error(f"获取网络安全区列表时出错: {str(e)}")
        return jsonify({'error': '获取网络安全区列表失败'}), 500

@cvm_management.route('/create', methods=['POST'])
@log_route
@log_operation('cvm_creation', '创建腾讯云虚拟机')
def create_cvm():
    """
    创建腾讯云虚拟机
    """
    try:
        data = request.get_json()

        # 验证必需参数
        required_fields = ['application_id', 'environment_id', 'app_en_name', 'env_en_name', 'net_area_id']
        for field in required_fields:
            if field not in data:
                return jsonify({'error': f'缺少必需参数: {field}'}), 400

        application_id = data['application_id']
        environment_id = data['environment_id']
        app_en_name = data['app_en_name']
        env_en_name = data['env_en_name']
        net_area_id = data['net_area_id']
        server_port = data.get('server_port', '')

        # 验证应用和环境是否存在
        application = Application.query.get(application_id)
        if not application:
            return jsonify({'error': '应用不存在'}), 404

        environment = EnvironmentBase.query.get(environment_id)
        if not environment:
            return jsonify({'error': '环境不存在'}), 404

        # 获取网络配置
        env_config = EnvNetConf.query.filter_by(
            env_id=environment_id,
            net_area_id=net_area_id
        ).first()

        if not env_config:
            return jsonify({'error': '未找到对应的环境网络配置'}), 404

        # 获取主机名
        hostname = get_next_hostname(env_en_name)
        if not hostname:
            return jsonify({'error': '获取主机名失败'}), 500

        # 确定Region
        region = get_region_from_placement(env_config.placement)

        # 获取腾讯云API密钥
        secret_id, secret_key = get_tencent_credentials()
        if not secret_id or not secret_key:
            return jsonify({'error': '获取腾讯云API密钥失败'}), 500

        # 创建CVM创建记录
        creation_record = CvmCreationRecord(
            application_id=application_id,
            environment_id=environment_id,
            net_area_id=net_area_id,
            hostname=hostname,
            instance_type=env_config.instance_type,
            placement=env_config.placement,
            vpc_id=env_config.vpcid,
            subnet_id=env_config.subnetid,
            security_group_id=env_config.securitygroupid,
            image_id=env_config.imageid,
            server_port=server_port,
            creation_status='creating',
            created_by=get_current_user_id()
        )

        db.session.add(creation_record)
        db.session.flush()  # 获取ID

        # 构造腾讯云API请求参数
        params = {
            "InstanceChargeType": "PREPAID",
            "InstanceChargePrepaid": {
                "Period": 1,
                "RenewFlag": "NOTIFY_AND_AUTO_RENEW"
            },
            "Placement": {
                "Zone": env_config.placement
            },
            "InstanceType": env_config.instance_type,
            "ImageId": env_config.imageid,
            "SystemDisk": {
                "DiskType": "CLOUD_PREMIUM",
                "DiskSize": 50,
                "DiskName": f"{hostname}_sysdisk"
            },
            "VirtualPrivateCloud": {
                "VpcId": env_config.vpcid,
                "SubnetId": env_config.subnetid
            },
            "InstanceName": hostname,
            "LoginSettings": {
                "KeepImageLogin": "true"
            },
            "SecurityGroupIds": [env_config.securitygroupid],
            "HostName": hostname
        }

        current_app.logger.info(f"准备创建CVM，参数: {json.dumps(params, ensure_ascii=False)}")

        # 创建腾讯云CVM客户端
        cvm_client = create_cvm_client(region)

        # 调用腾讯云API创建实例
        create_result = cvm_client.create_instance(params)

        if not create_result['success']:
            # 创建失败，更新记录状态
            creation_record.creation_status = 'failed'
            creation_record.error_message = create_result.get('error', '创建失败')
            creation_record.complete_time = datetime.now(beijing_tz).replace(tzinfo=None)
            db.session.commit()

            return jsonify({
                'error': f"CVM创建失败: {create_result.get('error', '未知错误')}",
                'creation_record_id': creation_record.id
            }), 500

        # 创建成功，更新记录
        instance_ids = create_result.get('instance_ids', [])
        if instance_ids:
            creation_record.instance_id = instance_ids[0]

        # 注意：实例创建成功不代表实例已经运行，需要等待
        # 这里先标记为creating状态，后续通过状态查询接口监控

        db.session.commit()

        return jsonify({
            'message': 'CVM创建请求已提交',
            'data': {
                'creation_record_id': creation_record.id,
                'hostname': hostname,
                'instance_id': creation_record.instance_id,
                'status': creation_record.creation_status
            }
        }), 201

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"创建CVM时出错: {str(e)}")
        return jsonify({'error': f'创建CVM失败: {str(e)}'}), 500

@cvm_management.route('/status/<int:record_id>', methods=['GET'])
@log_route
def get_creation_status(record_id):
    """
    获取CVM创建状态
    """
    try:
        record = CvmCreationRecord.query.get(record_id)
        if not record:
            return jsonify({'error': '创建记录不存在'}), 404

        return jsonify({
            'message': '获取创建状态成功',
            'data': record.to_dict()
        }), 200

    except Exception as e:
        current_app.logger.error(f"获取创建状态时出错: {str(e)}")
        return jsonify({'error': '获取创建状态失败'}), 500

@cvm_management.route('/records', methods=['GET'])
@log_route
def get_creation_records():
    """
    获取CVM创建记录列表
    """
    try:
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 20, type=int)

        # 构建查询
        query = CvmCreationRecord.query

        # 添加过滤条件
        if request.args.get('application_id'):
            query = query.filter(CvmCreationRecord.application_id == request.args.get('application_id'))

        if request.args.get('environment_id'):
            query = query.filter(CvmCreationRecord.environment_id == request.args.get('environment_id'))

        if request.args.get('status'):
            query = query.filter(CvmCreationRecord.creation_status == request.args.get('status'))

        # 按创建时间倒序排列
        query = query.order_by(CvmCreationRecord.create_time.desc())

        # 分页
        pagination = query.paginate(
            page=page, per_page=per_page, error_out=False
        )

        records = []
        for record in pagination.items:
            record_dict = record.to_dict()
            # 添加关联信息
            if record.application:
                record_dict['application_name'] = record.application.app_cn_name
            if record.environment:
                record_dict['environment_name'] = record.environment.env_cn_name
            if record.net_area:
                record_dict['net_area_name'] = record.net_area.net_area_name
            records.append(record_dict)

        return jsonify({
            'message': '获取创建记录成功',
            'data': records,
            'pagination': {
                'page': page,
                'per_page': per_page,
                'total': pagination.total,
                'pages': pagination.pages,
                'has_prev': pagination.has_prev,
                'has_next': pagination.has_next
            }
        }), 200

    except Exception as e:
        current_app.logger.error(f"获取创建记录时出错: {str(e)}")
        return jsonify({'error': '获取创建记录失败'}), 500

def create_server_resource_from_instance(instance_info, creation_record):
    """
    根据腾讯云实例信息创建服务器资源记录
    """
    try:
        # 检查是否已存在
        existing_server = ServerResource.query.filter_by(
            instance_id=instance_info.get('InstanceId')
        ).first()

        if existing_server:
            current_app.logger.warning(f"服务器资源已存在: {instance_info.get('InstanceId')}")
            return existing_server

        # 创建新的服务器资源记录
        server = ServerResource(
            instance_id=instance_info.get('InstanceId'),
            hostname=creation_record.hostname,
            ipv4=instance_info.get('PrivateIpAddresses', [''])[0] if instance_info.get('PrivateIpAddresses') else '',
            public_ip=instance_info.get('PublicIpAddresses', [''])[0] if instance_info.get('PublicIpAddresses') else None,
            os=instance_info.get('OsName', ''),
            cpu=f"{instance_info.get('CPU', 0)}核",
            memory=f"{instance_info.get('Memory', 0)}GB",
            region=get_region_from_placement(creation_record.placement),
            availability_zone=creation_record.placement,
            vpc_id=creation_record.vpc_id,
            subnet_id=creation_record.subnet_id,
            original_source='tencent',
            vm_creat_time=datetime.now(beijing_tz).replace(tzinfo=None),
            server_status='online'
        )

        db.session.add(server)
        db.session.flush()

        # 创建应用环境与服务器的绑定关系
        app_env = ApplicationEnvironment.query.filter_by(
            application_id=creation_record.application_id,
            env_id=creation_record.environment_id
        ).first()

        if app_env:
            # 检查绑定关系是否已存在
            existing_binding = ApplicationEnvServer.query.filter_by(
                application_environment_id=app_env.id,
                server_id=server.id
            ).first()

            if not existing_binding:
                binding = ApplicationEnvServer(
                    application_environment_id=app_env.id,
                    server_id=server.id,
                    server_port=creation_record.server_port
                )
                db.session.add(binding)

        # 记录变更历史
        history = ResourceChangeHistory(
            user_id=creation_record.created_by,
            resource_type='server_resource',
            resource_id=server.id,
            operation_type=OperationType.CREATE.value,
            after_data=json.dumps({
                'instance_id': server.instance_id,
                'hostname': server.hostname,
                'ipv4': server.ipv4,
                'source': 'cvm_creation'
            })
        )
        db.session.add(history)

        return server

    except Exception as e:
        current_app.logger.error(f"创建服务器资源记录时出错: {str(e)}")
        raise e

@cvm_management.route('/finalize/<int:record_id>', methods=['POST'])
@log_route
@log_operation('cvm_finalization', '完成CVM创建流程')
def finalize_cvm_creation(record_id):
    """
    完成CVM创建流程，获取实例信息并写入数据库
    """
    try:
        record = CvmCreationRecord.query.get(record_id)
        if not record:
            return jsonify({'error': '创建记录不存在'}), 404

        if record.creation_status != 'success':
            return jsonify({'error': '实例创建未成功，无法完成流程'}), 400

        # 获取实例详细信息
        region = get_region_from_placement(record.placement)
        cvm_client = create_cvm_client(region)

        # 等待实例进入运行状态
        wait_result = cvm_client.wait_for_instance_running(record.instance_id)

        if not wait_result['success']:
            # 等待失败，更新记录状态
            record.creation_status = 'failed'
            record.error_message = wait_result.get('error', '实例启动失败')
            record.complete_time = datetime.now(beijing_tz).replace(tzinfo=None)
            db.session.commit()

            return jsonify({
                'error': f"实例启动失败: {wait_result.get('error', '未知错误')}"
            }), 500

        instance_info = wait_result['instance']

        # 更新创建记录状态
        record.creation_status = 'success'
        record.complete_time = datetime.now(beijing_tz).replace(tzinfo=None)

        # 创建服务器资源记录
        server = create_server_resource_from_instance(instance_info, record)

        db.session.commit()

        return jsonify({
            'message': 'CVM创建流程完成',
            'data': {
                'server_id': server.id,
                'hostname': server.hostname,
                'ipv4': server.ipv4,
                'instance_id': server.instance_id
            }
        }), 200

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"完成CVM创建流程时出错: {str(e)}")
        return jsonify({'error': f'完成CVM创建流程失败: {str(e)}'}), 500

@cvm_management.route('/check-status/<int:record_id>', methods=['POST'])
@log_route
@log_operation('cvm_status_check', '检查CVM状态')
def check_cvm_status(record_id):
    """
    检查CVM实例状态并更新记录
    """
    try:
        record = CvmCreationRecord.query.get(record_id)
        if not record:
            return jsonify({'error': '创建记录不存在'}), 404

        if record.creation_status != 'creating':
            return jsonify({
                'message': '记录状态无需检查',
                'data': record.to_dict()
            }), 200

        if not record.instance_id:
            return jsonify({'error': '实例ID为空，无法检查状态'}), 400

        # 获取实例状态
        region = get_region_from_placement(record.placement)
        cvm_client = create_cvm_client(region)

        describe_result = cvm_client.describe_instances([record.instance_id])

        if not describe_result['success']:
            return jsonify({
                'error': f"查询实例状态失败: {describe_result.get('error', '未知错误')}"
            }), 500

        instances = describe_result['instances']
        if not instances:
            # 实例不存在，标记为失败
            record.creation_status = 'failed'
            record.error_message = '实例不存在'
            record.complete_time = datetime.now(beijing_tz).replace(tzinfo=None)
            db.session.commit()

            return jsonify({
                'message': '实例不存在，已标记为失败',
                'data': record.to_dict()
            }), 200

        instance = instances[0]
        instance_state = instance.get('InstanceState', '')

        if instance_state == 'RUNNING':
            # 实例运行中，标记为成功
            record.creation_status = 'success'
            record.complete_time = datetime.now(beijing_tz).replace(tzinfo=None)
            db.session.commit()

            return jsonify({
                'message': '实例已运行，状态已更新',
                'data': record.to_dict(),
                'instance_state': instance_state
            }), 200
        elif instance_state in ['LAUNCH_FAILED', 'SHUTDOWN']:
            # 实例启动失败，标记为失败
            record.creation_status = 'failed'
            record.error_message = f'实例状态异常: {instance_state}'
            record.complete_time = datetime.now(beijing_tz).replace(tzinfo=None)
            db.session.commit()

            return jsonify({
                'message': '实例启动失败，状态已更新',
                'data': record.to_dict(),
                'instance_state': instance_state
            }), 200
        else:
            # 实例仍在创建中
            return jsonify({
                'message': '实例仍在创建中',
                'data': record.to_dict(),
                'instance_state': instance_state
            }), 200

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"检查CVM状态时出错: {str(e)}")
        return jsonify({'error': f'检查CVM状态失败: {str(e)}'}), 500
