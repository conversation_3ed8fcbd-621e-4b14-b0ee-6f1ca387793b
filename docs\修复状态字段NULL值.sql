-- 修复状态字段NULL值的SQL脚本
-- 执行此脚本来修复数据库中现有记录的状态字段NULL值问题

-- 1. 修复服务器资源表中的NULL状态
UPDATE server_resource
SET server_status = 'online'
WHERE server_status IS NULL;

-- 2. 修复负载均衡资源表中的NULL状态
UPDATE load_balancer_resource
SET lb_status = 'online'
WHERE lb_status IS NULL;

-- 3. 修复数据库资源表中的NULL状态
UPDATE database_resource
SET db_status = 'online'
WHERE db_status IS NULL;

-- 4. 修复应用系统表中的NULL状态
UPDATE application
SET app_status = 'online'
WHERE app_status IS NULL;

-- 5. 修复应用环境表中的NULL状态
UPDATE application_environment
SET app_env_status = 'online'
WHERE app_env_status IS NULL;

-- 6. 修复offline_time字段的错误默认值
-- 对于状态为online的记录，offline_time应该为NULL
UPDATE server_resource
SET offline_time = NULL
WHERE server_status = 'online' AND offline_time IS NOT NULL;

UPDATE load_balancer_resource
SET offline_time = NULL
WHERE lb_status = 'online' AND offline_time IS NOT NULL;

UPDATE database_resource
SET offline_time = NULL
WHERE db_status = 'online' AND offline_time IS NOT NULL;

UPDATE application
SET offline_time = NULL
WHERE app_status = 'online' AND offline_time IS NOT NULL;

UPDATE application_environment
SET offline_time = NULL
WHERE app_env_status = 'online' AND offline_time IS NOT NULL;

-- 查询修复结果
SELECT
    'server_resource' as table_name,
    COUNT(*) as total_records,
    SUM(CASE WHEN server_status IS NULL THEN 1 ELSE 0 END) as null_status_count,
    SUM(CASE WHEN server_status = 'online' THEN 1 ELSE 0 END) as online_count,
    SUM(CASE WHEN server_status = 'offline' THEN 1 ELSE 0 END) as offline_count
FROM server_resource

UNION ALL

SELECT
    'load_balancer_resource' as table_name,
    COUNT(*) as total_records,
    SUM(CASE WHEN lb_status IS NULL THEN 1 ELSE 0 END) as null_status_count,
    SUM(CASE WHEN lb_status = 'online' THEN 1 ELSE 0 END) as online_count,
    SUM(CASE WHEN lb_status = 'offline' THEN 1 ELSE 0 END) as offline_count
FROM load_balancer_resource

UNION ALL

SELECT
    'database_resource' as table_name,
    COUNT(*) as total_records,
    SUM(CASE WHEN db_status IS NULL THEN 1 ELSE 0 END) as null_status_count,
    SUM(CASE WHEN db_status = 'online' THEN 1 ELSE 0 END) as online_count,
    SUM(CASE WHEN db_status = 'offline' THEN 1 ELSE 0 END) as offline_count
FROM database_resource

UNION ALL

SELECT
    'application' as table_name,
    COUNT(*) as total_records,
    SUM(CASE WHEN app_status IS NULL THEN 1 ELSE 0 END) as null_status_count,
    SUM(CASE WHEN app_status = 'online' THEN 1 ELSE 0 END) as online_count,
    SUM(CASE WHEN app_status = 'offline' THEN 1 ELSE 0 END) as offline_count
FROM application

UNION ALL

SELECT
    'application_environment' as table_name,
    COUNT(*) as total_records,
    SUM(CASE WHEN app_env_status IS NULL THEN 1 ELSE 0 END) as null_status_count,
    SUM(CASE WHEN app_env_status = 'online' THEN 1 ELSE 0 END) as online_count,
    SUM(CASE WHEN app_env_status = 'offline' THEN 1 ELSE 0 END) as offline_count
FROM application_environment;
