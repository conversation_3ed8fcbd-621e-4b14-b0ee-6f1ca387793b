"""
应用系统管理模块
"""
from flask import Blueprint, request, jsonify, current_app, g
from sqlalchemy import or_
from datetime import datetime
import json
from .. import db, beijing_tz
from ..models import Application, ResourceChangeHistory, OperationType
from ..utils.decorators import log_route, log_operation
from ..utils.user_utils import get_current_user_id

# 创建蓝图
application = Blueprint('application', __name__)

@application.route('/applications', methods=['GET'])
@log_route
def get_applications():
    """
    查询应用系统列表，支持按系统英文名、系统中文名、管理员进行模糊查询

    Query Parameters:
        app_en_name (str, optional): 系统英文名关键字
        app_cn_name (str, optional): 系统中文名关键字
        app_admin (str, optional): 系统管理员关键字
        page (int, optional): 页码，默认为1
        per_page (int, optional): 每页记录数，默认为20

    Returns:
        JSON: 应用系统列表和分页信息
    """
    # 获取查询参数
    app_en_name = request.args.get('app_en_name', '')
    app_cn_name = request.args.get('app_cn_name', '')
    app_admin = request.args.get('app_admin', '')
    page = int(request.args.get('page', 1))
    per_page = int(request.args.get('per_page', 20))

    # 构建查询
    query = Application.query

    # 应用过滤条件
    if app_en_name:
        query = query.filter(Application.app_en_name.like(f'%{app_en_name}%'))
    if app_cn_name:
        query = query.filter(Application.app_cn_name.like(f'%{app_cn_name}%'))
    if app_admin:
        query = query.filter(Application.app_admin.like(f'%{app_admin}%'))

    # 分页
    total = query.count()
    applications = query.order_by(Application.create_time.desc()).paginate(page=page, per_page=per_page)

    # 构建响应
    result = {
        'total': total,
        'page': page,
        'per_page': per_page,
        'pages': (total + per_page - 1) // per_page,
        'applications': [app.to_dict() for app in applications.items]
    }

    return jsonify(result)

@application.route('/applications', methods=['POST'])
@log_route
@log_operation('创建应用系统')
def create_application():
    """
    创建新应用系统

    Request Body:
        app_en_name (str): 系统英文名
        app_cn_name (str): 系统中文名
        app_admin (str): 系统管理员
        remark (str, optional): 备注

    Returns:
        JSON: 创建结果
    """
    data = request.get_json()

    # 验证必填字段
    required_fields = ['app_en_name', 'app_cn_name', 'app_admin']
    for field in required_fields:
        if field not in data or not data[field]:
            return jsonify({'error': f'缺少必填字段: {field}'}), 400

    # 检查系统英文名是否已存在
    existing = Application.query.filter_by(app_en_name=data['app_en_name']).first()
    if existing:
        return jsonify({'error': f'系统英文名 {data["app_en_name"]} 已存在'}), 400

    # 创建新应用系统
    app = Application(
        app_en_name=data['app_en_name'],
        app_cn_name=data['app_cn_name'],
        app_admin=data['app_admin'],
        app_status='online',  # 默认为在线状态
        remark=data.get('remark', '')
    )

    db.session.add(app)
    db.session.flush()  # 获取ID

    # 记录变更历史
    history = ResourceChangeHistory(
        user_id=get_current_user_id(),  # 使用工具函数获取当前用户ID
        resource_type='application',
        resource_id=app.id,
        operation_type=OperationType.CREATE.value,
        after_data=json.dumps({
            'app_en_name': app.app_en_name,
            'app_cn_name': app.app_cn_name,
            'app_admin': app.app_admin,
            'app_status': app.app_status,
            'remark': app.remark
        })
    )

    db.session.add(history)
    db.session.commit()

    return jsonify({
        'message': '应用系统创建成功',
        'id': app.id
    }), 201

@application.route('/applications/<int:app_id>', methods=['PUT'])
@log_route
@log_operation('更新应用系统')
def update_application(app_id):
    """
    更新应用系统信息

    Path Parameters:
        app_id (int): 应用系统ID

    Request Body:
        app_admin (str, optional): 系统管理员
        remark (str, optional): 备注

    Returns:
        JSON: 更新结果
    """
    app = Application.query.get_or_404(app_id)
    data = request.get_json()

    # 只允许更新系统管理员和备注
    before_data = {
        'app_admin': app.app_admin,
        'remark': app.remark
    }

    changes_made = False

    if 'app_admin' in data and data['app_admin'] != app.app_admin:
        app.app_admin = data['app_admin']
        changes_made = True

    if 'remark' in data and data['remark'] != app.remark:
        app.remark = data['remark']
        changes_made = True

    if not changes_made:
        return jsonify({'message': '没有变更需要保存'}), 200

    # 记录变更历史
    after_data = {
        'app_admin': app.app_admin,
        'remark': app.remark
    }

    history = ResourceChangeHistory(
        user_id=g.user.id if hasattr(g, 'user') else 1,
        resource_type='application',
        resource_id=app.id,
        operation_type=OperationType.UPDATE.value,
        before_data=json.dumps(before_data),
        after_data=json.dumps(after_data)
    )

    db.session.add(history)
    db.session.commit()

    return jsonify({'message': '应用系统更新成功'})

@application.route('/applications/<int:app_id>/offline', methods=['PUT'])
@log_route
@log_operation('下线应用系统')
def offline_application(app_id):
    """
    下线应用系统

    Path Parameters:
        app_id (int): 应用系统ID

    Returns:
        JSON: 操作结果
    """
    app = Application.query.get_or_404(app_id)

    # 检查当前状态
    if app.app_status == 'offline':
        return jsonify({'message': '应用系统已经是下线状态'}), 400

    # 检查是否有在线的应用环境
    from app.models import ApplicationEnvironment
    online_envs = ApplicationEnvironment.query.filter_by(
        application_id=app_id,
        app_env_status='online'
    ).all()

    if online_envs:
        # 构建在线环境列表
        env_list = []
        for env in online_envs[:10]:  # 只返回前10个环境
            env_base = env.environment
            env_list.append({
                'id': env.id,
                'env_name': f"{env_base.env_cn_name}({env_base.env_en_name})"
            })

        return jsonify({
            'error': '无法下线应用系统，该系统还有在线的应用环境',
            'env_count': len(online_envs),
            'environments': env_list,
            'has_more': len(online_envs) > 10
        }), 400

    try:
        # 记录变更前数据
        before_data = {
            'app_status': app.app_status,
            'offline_time': app.offline_time.strftime('%Y-%m-%d %H:%M:%S') if app.offline_time else None
        }

        # 更新状态和下线时间
        app.app_status = 'offline'
        app.offline_time = datetime.now(beijing_tz).replace(tzinfo=None)

        # 记录变更后数据
        after_data = {
            'app_status': app.app_status,
            'offline_time': app.offline_time.strftime('%Y-%m-%d %H:%M:%S')
        }

        # 记录变更历史
        history = ResourceChangeHistory(
            user_id=g.user.id if hasattr(g, 'user') else 1,
            resource_type='application',
            resource_id=app.id,
            operation_type=OperationType.UPDATE.value,
            before_data=json.dumps(before_data),
            after_data=json.dumps(after_data)
        )

        db.session.add(history)
        db.session.commit()

        return jsonify({'message': '应用系统已成功下线'})

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"下线应用系统时出错: {str(e)}")
        return jsonify({'error': f'下线应用系统时出错: {str(e)}'}), 500
