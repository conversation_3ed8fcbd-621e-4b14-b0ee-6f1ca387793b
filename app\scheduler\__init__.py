"""
定时任务调度器模块
"""
from apscheduler.schedulers.background import BackgroundScheduler
from apscheduler.jobstores.sqlalchemy import SQLAlchemyJobStore
from apscheduler.executors.pool import ThreadPoolExecutor
from flask import current_app
import atexit
import pytz

# 全局调度器实例
scheduler = None

def init_scheduler(app):
    """
    初始化定时任务调度器
    
    Args:
        app: Flask应用实例
    """
    global scheduler
    
    if scheduler is not None:
        return scheduler
    
    # 配置调度器
    jobstores = {
        'default': SQLAlchemyJobStore(url=app.config['SQLALCHEMY_DATABASE_URI'])
    }
    
    executors = {
        'default': ThreadPoolExecutor(20)
    }
    
    job_defaults = {
        'coalesce': False,
        'max_instances': 3
    }
    
    # 创建调度器
    scheduler = BackgroundScheduler(
        jobstores=jobstores,
        executors=executors,
        job_defaults=job_defaults,
        timezone=pytz.timezone('Asia/Shanghai')
    )
    
    # 启动调度器
    scheduler.start()
    
    # 注册应用关闭时停止调度器
    atexit.register(lambda: scheduler.shutdown())
    
    app.logger.info("定时任务调度器已启动")
    
    return scheduler

def get_scheduler():
    """
    获取调度器实例
    
    Returns:
        BackgroundScheduler: 调度器实例
    """
    global scheduler
    return scheduler

def add_scheduled_jobs(app):
    """
    添加定时任务
    
    Args:
        app: Flask应用实例
    """
    from .tasks import (
        sync_tencent_servers_task,
        sync_tencent_load_balancers_task,
        sync_ldap_users_task
    )
    
    global scheduler
    if scheduler is None:
        app.logger.error("调度器未初始化，无法添加定时任务")
        return
    
    try:
        # 添加腾讯云主机同步任务 - 每天晚上22点执行
        scheduler.add_job(
            func=sync_tencent_servers_task,
            trigger='cron',
            hour=22,
            minute=0,
            id='sync_tencent_servers',
            name='同步腾讯云主机',
            replace_existing=True,
            args=[app]
        )
        
        # 添加腾讯云负载均衡同步任务 - 每天晚上22点执行
        scheduler.add_job(
            func=sync_tencent_load_balancers_task,
            trigger='cron',
            hour=22,
            minute=5,  # 比主机同步晚5分钟执行
            id='sync_tencent_load_balancers',
            name='同步腾讯云负载均衡',
            replace_existing=True,
            args=[app]
        )
        
        # 添加LDAP用户同步任务 - 每天晚上21点执行
        scheduler.add_job(
            func=sync_ldap_users_task,
            trigger='cron',
            hour=21,
            minute=0,
            id='sync_ldap_users',
            name='同步LDAP用户',
            replace_existing=True,
            args=[app]
        )
        
        app.logger.info("定时任务已添加：")
        app.logger.info("- 腾讯云主机同步：每天22:00")
        app.logger.info("- 腾讯云负载均衡同步：每天22:05")
        app.logger.info("- LDAP用户同步：每天21:00")
        
    except Exception as e:
        app.logger.error(f"添加定时任务失败: {str(e)}")

def list_jobs():
    """
    列出所有定时任务
    
    Returns:
        list: 任务列表
    """
    global scheduler
    if scheduler is None:
        return []
    
    jobs = []
    for job in scheduler.get_jobs():
        jobs.append({
            'id': job.id,
            'name': job.name,
            'next_run_time': job.next_run_time.strftime('%Y-%m-%d %H:%M:%S') if job.next_run_time else None,
            'trigger': str(job.trigger)
        })
    
    return jobs

def pause_job(job_id):
    """
    暂停任务
    
    Args:
        job_id (str): 任务ID
    """
    global scheduler
    if scheduler is None:
        return False
    
    try:
        scheduler.pause_job(job_id)
        return True
    except Exception:
        return False

def resume_job(job_id):
    """
    恢复任务
    
    Args:
        job_id (str): 任务ID
    """
    global scheduler
    if scheduler is None:
        return False
    
    try:
        scheduler.resume_job(job_id)
        return True
    except Exception:
        return False

def remove_job(job_id):
    """
    删除任务
    
    Args:
        job_id (str): 任务ID
    """
    global scheduler
    if scheduler is None:
        return False
    
    try:
        scheduler.remove_job(job_id)
        return True
    except Exception:
        return False
