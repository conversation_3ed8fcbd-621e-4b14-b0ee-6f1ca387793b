# CVM管理模块部署指南

## 概述

本文档描述了如何部署和配置腾讯云虚拟机管理模块。该模块提供了完整的CVM创建、监控和管理功能。

## 前置条件

### 1. 系统要求
- Python 3.8+
- MySQL 8.0+
- 已部署的ICMS2基础系统

### 2. 腾讯云要求
- 有效的腾讯云账号
- 已创建的API密钥（SecretId和SecretKey）
- 已配置的VPC、子网、安全组等网络资源

## 安装步骤

### 1. 安装腾讯云SDK

```bash
pip install tencentcloud-sdk-python
```

### 2. 数据库迁移

执行数据库迁移脚本创建必要的表：

```bash
mysql -u username -p database_name < scripts/create_cvm_tables.sql
```

### 3. 配置腾讯云API密钥

在数据库中添加腾讯云API密钥：

```sql
INSERT INTO cloud_api_key (secret_id, secret_key, cloudname) 
VALUES ('your_secret_id', 'your_secret_key', 'tencent');
```

### 4. 配置网络安全区

根据实际环境配置网络安全区：

```sql
-- 示例配置
INSERT INTO net_area_base (net_area_name, net_area_des) VALUES
('生产区', '生产环境网络安全区'),
('测试区', '测试环境网络安全区');
```

### 5. 配置环境网络参数

为每个环境和网络安全区配置网络参数：

```sql
-- 示例配置（请根据实际环境修改）
INSERT INTO env_net_conf (env_id, net_area_id, subnetid, placement, vpcid, imageid, instance_type, securitygroupid) VALUES
(1, 1, 'subnet-prod001', 'ap-shanghai-fsi-1', 'vpc-prod001', 'img-centos76', 'S3.MEDIUM4', 'sg-prod001');
```

### 6. 配置主机名管理

初始化主机名管理表：

```sql
INSERT INTO creat_cvm_hostname (nscode, current_hostname) VALUES
('htsh', 'htsh101268'),
('htsz', 'htsz101268');
```

## 配置说明

### 1. 网络配置参数

| 参数 | 说明 | 示例 |
|------|------|------|
| subnetid | 子网ID | subnet-prod001 |
| placement | 可用区 | ap-shanghai-fsi-1 |
| vpcid | VPC ID | vpc-prod001 |
| imageid | 镜像ID | img-centos76 |
| instance_type | 实例机型 | S3.MEDIUM4 |
| securitygroupid | 安全组ID | sg-prod001 |

### 2. 主机名规则

- **htsh前缀**: 用于上海地区的主机
- **htsz前缀**: 用于深圳地区的主机（dr和cow环境）
- **数字格式**: 6位数字，自动递增

### 3. Region映射

- `ap-shanghai-fsi-*` → `ap-shanghai-fsi`
- `ap-shenzhen-fsi-*` → `ap-shenzhen-fsi`

## 测试验证

### 1. 运行测试脚本

```bash
python test_cvm_creation.py [JWT_TOKEN]
```

### 2. 手动测试步骤

1. **获取网络安全区列表**:
   ```bash
   curl -X GET "http://localhost:5000/api/cvm/net-areas" \
        -H "Authorization: Bearer YOUR_TOKEN"
   ```

2. **创建CVM实例**:
   ```bash
   curl -X POST "http://localhost:5000/api/cvm/create" \
        -H "Content-Type: application/json" \
        -H "Authorization: Bearer YOUR_TOKEN" \
        -d '{
          "application_id": 1,
          "environment_id": 1,
          "app_en_name": "testapp",
          "env_en_name": "test",
          "net_area_id": 1,
          "server_port": "8080"
        }'
   ```

3. **检查创建状态**:
   ```bash
   curl -X POST "http://localhost:5000/api/cvm/check-status/1" \
        -H "Authorization: Bearer YOUR_TOKEN"
   ```

## 故障排除

### 1. 常见错误

**错误**: "获取腾讯云API密钥失败"
- **解决**: 检查cloud_api_key表中是否正确配置了腾讯云密钥

**错误**: "未找到对应的环境网络配置"
- **解决**: 检查env_net_conf表中是否配置了对应的环境和网络安全区

**错误**: "获取主机名失败"
- **解决**: 检查creat_cvm_hostname表中是否有对应的nscode记录

### 2. 日志查看

查看应用日志以获取详细错误信息：

```bash
tail -f logs/icms2-$(date +%Y-%m-%d).log
```

### 3. 模拟模式

如果腾讯云SDK不可用，系统会自动进入模拟模式：
- 创建的实例ID格式为：`ins-{hostname}`
- 实例信息使用模拟数据
- 适用于开发和测试环境

## 安全注意事项

1. **API密钥安全**:
   - 定期轮换腾讯云API密钥
   - 限制API密钥的权限范围
   - 不要在代码中硬编码密钥

2. **网络安全**:
   - 确保安全组配置正确
   - 限制不必要的端口开放
   - 使用VPC隔离网络

3. **访问控制**:
   - 确保只有授权用户可以创建CVM
   - 记录所有创建操作的审计日志

## 监控和维护

### 1. 定期检查

- 监控CVM创建成功率
- 检查主机名分配是否正常
- 验证网络配置的有效性

### 2. 数据清理

定期清理过期的创建记录：

```sql
-- 清理30天前的失败记录
DELETE FROM cvm_creation_record 
WHERE creation_status = 'failed' 
AND create_time < DATE_SUB(NOW(), INTERVAL 30 DAY);
```

### 3. 性能优化

- 为常用查询字段添加索引
- 定期分析慢查询
- 监控数据库性能

## 扩展功能

### 1. 批量创建

可以扩展API支持批量创建多个CVM实例。

### 2. 模板管理

可以添加CVM创建模板功能，预定义常用配置。

### 3. 自动化运维

集成自动化运维工具，实现CVM的自动配置和部署。

## 联系支持

如果在部署过程中遇到问题，请：

1. 查看日志文件获取详细错误信息
2. 检查配置是否正确
3. 参考API文档确认接口调用方式
4. 联系系统管理员获取支持
