from flask import Blueprint, request, jsonify, current_app
from .. import db, beijing_tz
from ..models import Role, Module, Menu, RoleModule, User, UserRole, LocalUser
from ..utils.decorators import log_route, log_operation

role = Blueprint('role', __name__)

@role.route('/roles', methods=['GET'])
@log_route
def get_roles():
    """获取所有角色及其权限信息"""
    roles = Role.query.all()
    result = []

    for role_item in roles:
        # 获取角色关联的模块和菜单
        module_dict = {}

        # 获取角色的权限记录
        role_module = RoleModule.query.filter_by(role_id=role_item.id).first()

        if role_module:
            # 将逗号分隔的ID字符串转换为列表
            module_ids = RoleModule.str_to_list(role_module.module_id)
            menu_ids = RoleModule.str_to_list(role_module.menu_id)

            # 获取模块信息
            for module_id in module_ids:
                module = Module.query.get(module_id)
                if module:
                    module_dict[module_id] = {
                        'id': module.id,
                        'name': module.display_name,
                        'menus': []
                    }

            # 获取菜单信息并关联到对应模块
            for menu_id in menu_ids:
                menu = Menu.query.get(menu_id)
                if menu and menu.module_id in module_ids:
                    # 检查菜单是否已添加
                    menu_exists = False
                    for m in module_dict[menu.module_id]['menus']:
                        if m['id'] == menu.id:
                            menu_exists = True
                            break

                    if not menu_exists:
                        module_dict[menu.module_id]['menus'].append({
                            'id': menu.id,
                            'name': menu.menu_display_name
                        })

        # 构建角色信息
        role_data = {
            'id': role_item.id,
            'role_name': role_item.role_name,
            'description': role_item.description,
            'modules': list(module_dict.values())
        }

        result.append(role_data)

    return jsonify(result)

@role.route('/roles/<int:role_id>', methods=['GET'])
@log_route
def get_role_detail(role_id):
    """获取特定角色的详细信息，包括权限结构"""
    role_item = Role.query.get_or_404(role_id)

    # 获取角色关联的模块和菜单
    module_dict = {}
    module_ids = []
    menu_ids = []

    # 获取角色的权限记录
    role_module = RoleModule.query.filter_by(role_id=role_id).first()

    if role_module:
        # 将逗号分隔的ID字符串转换为列表
        module_ids = RoleModule.str_to_list(role_module.module_id)
        menu_ids = RoleModule.str_to_list(role_module.menu_id)

        # 获取模块信息
        for module_id in module_ids:
            module = Module.query.get(module_id)
            if module:
                module_dict[module_id] = {
                    'id': module.id,
                    'name': module.display_name,
                    'menus': []
                }

        # 获取菜单信息并关联到对应模块
        for menu_id in menu_ids:
            menu = Menu.query.get(menu_id)
            if menu and menu.module_id in module_ids:
                # 检查菜单是否已添加
                menu_exists = False
                for m in module_dict[menu.module_id]['menus']:
                    if m['id'] == menu.id:
                        menu_exists = True
                        break

                if not menu_exists:
                    module_dict[menu.module_id]['menus'].append({
                        'id': menu.id,
                        'name': menu.menu_display_name
                    })

    # 构建角色信息
    return jsonify({
        'id': role_item.id,
        'role_name': role_item.role_name,
        'description': role_item.description,
        'modules': list(module_dict.values()),
        'module_ids': module_ids,
        'menu_ids': menu_ids
    })

@role.route('/roles/<int:role_id>/users', methods=['GET'])
@log_route
def get_role_users(role_id):
    # 检查角色是否存在
    role_item = Role.query.get_or_404(role_id)

    # 获取查询参数
    user_type = request.args.get('user_type', 'ldap')  # 默认查询LDAP用户

    # 获取拥有此角色的用户
    users = User.query.select_from(User).join(
        UserRole,
        UserRole.user_id == User.id
    ).filter(
        UserRole.role_id == role_id,
        UserRole.user_type == user_type
    ).all()

    # 如果查询的是本地用户，需要从LocalUser表获取
    if user_type == 'local':
        users = LocalUser.query.select_from(LocalUser).join(
            UserRole,
            UserRole.user_id == LocalUser.id
        ).filter(
            UserRole.role_id == role_id,
            UserRole.user_type == 'local'
        ).all()

    return jsonify([
        {
            'id': user.id,
            'username': user.username,
            'display_name': user.display_name,
            'email': getattr(user, 'email', ''),  # LocalUser可能没有email字段
            'is_active': getattr(user, 'is_active', True)  # LocalUser可能没有is_active字段
        } for user in users
    ])

@role.route('/roles', methods=['POST'])
@log_route
@log_operation('创建角色')
def create_role():
    """创建新角色"""
    data = request.get_json()

    # 验证必填字段
    if not data or 'role_name' not in data:
        return jsonify({'error': '角色名称不能为空'}), 400

    # 检查角色名是否已存在
    existing_role = Role.query.filter_by(role_name=data['role_name']).first()
    if existing_role:
        return jsonify({'error': '角色名称已存在'}), 400

    # 创建新角色
    new_role = Role(
        role_name=data['role_name'],
        description=data.get('description', '')
    )
    db.session.add(new_role)
    db.session.flush()  # 获取新角色ID

    # 处理权限分配
    module_ids = data.get('module_ids', [])
    menu_ids = data.get('menu_ids', [])

    if module_ids or menu_ids:
        # 创建角色模块关联记录，使用逗号分隔的字符串存储多个ID
        role_module = RoleModule(
            role_id=new_role.id,
            module_id=RoleModule.list_to_str(module_ids),
            menu_id=RoleModule.list_to_str(menu_ids)
        )
        db.session.add(role_module)

    db.session.commit()

    return jsonify({
        'message': '角色创建成功',
        'id': new_role.id
    })

@role.route('/roles/<int:role_id>', methods=['PUT'])
@log_route
@log_operation('更新角色')
def update_role(role_id):
    """更新角色信息和权限"""
    role_item = Role.query.get_or_404(role_id)
    data = request.get_json()

    # 更新角色基本信息
    if 'role_name' in data:
        # 检查新角色名是否与其他角色冲突
        existing_role = Role.query.filter(Role.role_name == data['role_name'], Role.id != role_id).first()
        if existing_role:
            return jsonify({'error': '角色名称已存在'}), 400
        role_item.role_name = data['role_name']

    if 'description' in data:
        role_item.description = data['description']

    # 更新权限
    module_ids = data.get('module_ids')
    menu_ids = data.get('menu_ids')

    if module_ids is not None or menu_ids is not None:
        # 获取现有权限记录
        role_module = RoleModule.query.filter_by(role_id=role_id).first()

        if role_module:
            # 如果提供了新的模块ID列表，则更新
            if module_ids is not None:
                role_module.module_id = RoleModule.list_to_str(module_ids)

            # 如果提供了新的菜单ID列表，则更新
            if menu_ids is not None:
                role_module.menu_id = RoleModule.list_to_str(menu_ids)
        else:
            # 如果没有现有记录，则创建新记录
            new_module_ids = module_ids if module_ids is not None else []
            new_menu_ids = menu_ids if menu_ids is not None else []

            role_module = RoleModule(
                role_id=role_id,
                module_id=RoleModule.list_to_str(new_module_ids),
                menu_id=RoleModule.list_to_str(new_menu_ids)
            )
            db.session.add(role_module)

    db.session.commit()

    return jsonify({'message': '角色更新成功'})

@role.route('/roles/<int:role_id>', methods=['DELETE'])
@log_route
@log_operation('删除角色')
def delete_role(role_id):
    """删除角色，如果角色已分配给用户则返回错误"""
    # 检查角色是否存在
    role_item = Role.query.get_or_404(role_id)

    # 检查角色是否已分配给用户
    user_count = UserRole.query.filter_by(role_id=role_id).count()
    if user_count > 0:
        # 获取使用此角色的用户列表
        users = User.query.join(UserRole).filter(UserRole.role_id == role_id).all()
        user_list = [{'id': user.id, 'username': user.username, 'display_name': user.display_name}
                    for user in users[:10]]  # 只返回前10个用户

        return jsonify({
            'error': '无法删除角色，该角色已分配给用户',
            'user_count': user_count,
            'users': user_list,
            'has_more': user_count > 10
        }), 400

    # 删除角色关联的模块权限
    RoleModule.query.filter_by(role_id=role_id).delete()

    # 删除角色
    db.session.delete(role_item)
    db.session.commit()

    return jsonify({'message': '角色删除成功'})
