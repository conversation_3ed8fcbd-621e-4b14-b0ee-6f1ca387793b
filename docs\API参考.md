# API参考

本文档提供了ICMS2 API接口的全面参考。

## 基础URL

所有API接口都以`/api`为前缀。

## 认证

大多数接口需要使用JWT令牌进行认证。使用以下方法之一在请求中包含令牌：

- **HTTP头**：`Authorization: Bearer <token>`
- **Cookie**：`token=<token>`

## 接口

### 认证

#### 登录

认证用户并返回JWT令牌。

- **URL**：`/api/login`
- **方法**：`POST`
- **需要认证**：否
- **请求体**：
  ```json
  {
    "username": "字符串",
    "password": "字符串"
  }
  ```
- **成功响应**：
  - **代码**：200
  - **内容**：
    ```json
    {
      "id": "整数",
      "username": "字符串",
      "display_name": "字符串",
      "is_local_admin": "布尔值",
      "permissions": "对象",
      "token": "字符串"
    }
    ```
- **错误响应**：
  - **代码**：401
  - **内容**：`{ "error": "用户不存在或密码错误" }`

#### 验证令牌

验证JWT令牌并返回用户信息。

- **URL**：`/api/validate-token`
- **方法**：`GET`
- **需要认证**：是
- **成功响应**：
  - **代码**：200
  - **内容**：
    ```json
    {
      "valid": true,
      "user": {
        "id": "整数",
        "username": "字符串",
        "display_name": "字符串",
        "is_local_admin": "布尔值"
      },
      "permissions": "对象",
      "new_token": "字符串（可选）"
    }
    ```
- **错误响应**：
  - **代码**：401
  - **内容**：`{ "valid": false, "error": "认证令牌已过期" }`

### 用户管理

#### 获取用户

检索用户列表。

- **URL**：`/api/users`
- **方法**：`GET`
- **需要认证**：是
- **查询参数**：
  - `username`（可选）：按用户名过滤用户
- **成功响应**：
  - **代码**：200
  - **内容**：
    ```json
    [
      {
        "id": "整数",
        "username": "字符串",
        "display_name": "字符串",
        "roles": ["字符串"]
      }
    ]
    ```

#### 为用户分配角色

为用户分配角色。

- **URL**：`/api/users/<user_id>/roles`
- **方法**：`PUT`
- **需要认证**：是
- **请求体**：
  ```json
  {
    "roles": ["整数"],
    "user_type": "字符串"（可选，默认："ldap"）
  }
  ```
- **成功响应**：
  - **代码**：200
  - **内容**：`{ "message": "角色分配成功" }`
- **错误响应**：
  - **代码**：404
  - **内容**：`{ "error": "未找到用户" }`

### LDAP配置

#### 获取LDAP配置

检索所有LDAP配置。

- **URL**：`/api/ldap-configs`
- **方法**：`GET`
- **需要认证**：是
- **成功响应**：
  - **代码**：200
  - **内容**：
    ```json
    [
      {
        "id": "整数",
        "server": "字符串",
        "port": "整数",
        "bind_dn": "字符串",
        "ou_path": "字符串",
        "is_active": "布尔值",
        "create_time": "字符串",
        "update_time": "字符串"
      }
    ]
    ```

#### 创建LDAP配置

创建新的LDAP配置。

- **URL**：`/api/ldap-config`
- **方法**：`POST`
- **需要认证**：是
- **请求体**：
  ```json
  {
    "server": "字符串",
    "port": "整数",
    "bind_dn": "字符串",
    "password": "字符串",
    "ou_path": "字符串",
    "is_active": "布尔值"（可选）
  }
  ```
- **成功响应**：
  - **代码**：200
  - **内容**：`{ "id": "整数", "message": "LDAP 配置保存成功" }`
- **错误响应**：
  - **代码**：400
  - **内容**：`{ "error": "参数缺失" }`

#### 更新LDAP配置

更新现有的LDAP配置。

- **URL**：`/api/ldap-config/<config_id>`
- **方法**：`PUT`
- **需要认证**：是
- **请求体**：
  ```json
  {
    "server": "字符串"（可选）,
    "port": "整数"（可选）,
    "bind_dn": "字符串"（可选）,
    "password": "字符串"（可选）,
    "ou_path": "字符串"（可选）,
    "is_active": "布尔值"（可选）
  }
  ```
- **成功响应**：
  - **代码**：200
  - **内容**：`{ "message": "LDAP 配置更新成功" }`
- **错误响应**：
  - **代码**：404
  - **内容**：`{ "error": "未找到" }`

#### 删除LDAP配置

删除LDAP配置。

- **URL**：`/api/ldap-config/<config_id>`
- **方法**：`DELETE`
- **需要认证**：是
- **成功响应**：
  - **代码**：200
  - **内容**：`{ "message": "LDAP 配置删除成功" }`
- **错误响应**：
  - **代码**：404
  - **内容**：`{ "error": "未找到" }`

### 角色管理

有关角色管理接口，请参阅role.py实现。

### 模块和菜单管理

有关模块和菜单管理接口，请参阅module_menu.py实现。
