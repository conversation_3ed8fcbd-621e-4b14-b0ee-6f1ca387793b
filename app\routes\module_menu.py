from flask import Blueprint, request, jsonify, current_app
from .. import db, beijing_tz
from ..models import Module, Menu
from ..utils.decorators import log_route, log_operation

module_menu = Blueprint('module_menu', __name__)

@module_menu.route('/modules', methods=['GET'])
@log_route
def get_modules():
    modules = Module.query.all()
    return jsonify([
        {
            'id': m.id,
            'module_name': m.module_name,
            'display_name': m.display_name,
            'description': m.description
        } for m in modules
    ])

@module_menu.route('/menus', methods=['GET'])
@log_route
def get_menus():
    # 可选的模块ID过滤
    module_id = request.args.get('module_id')

    query = Menu.query
    if module_id:
        query = query.filter_by(module_id=module_id)

    menus = query.all()
    return jsonify([
        {
            'id': m.id,
            'menu_name': m.menu_name,
            'menu_display_name': m.menu_display_name,
            'module_id': m.module_id
        } for m in menus
    ])

@module_menu.route('/module-menus', methods=['GET'])
@log_route
def get_module_with_menus():
    """获取所有模块及其菜单，以树形结构返回"""
    modules = Module.query.all()
    result = []

    for module in modules:
        module_data = {
            'id': module.id,
            'module_name': module.module_name,
            'display_name': module.display_name,
            'description': module.description,
            'menus': []
        }

        # 获取该模块下的所有菜单
        menus = Menu.query.filter_by(module_id=module.id).all()
        for menu in menus:
            module_data['menus'].append({
                'id': menu.id,
                'menu_name': menu.menu_name,
                'menu_display_name': menu.menu_display_name
            })

        result.append(module_data)

    return jsonify(result)
