# 认证模块 API 文档

本文档描述了认证模块（`auth.py`）中的所有 API 接口。

## 目录

- [权限存储说明](#权限存储说明)
- [登录接口](#登录接口)
- [Token 验证](#token-验证)
- [LDAP 配置管理](#ldap-配置管理)
- [用户管理](#用户管理)
- [LDAP 用户导入](#ldap-用户导入)
- [用户权限查询](#用户权限查询)

## 权限存储说明

本系统采用优化的角色授权方式，每个角色的授权在 `role_module` 表中只存储一行记录，多个模块ID或菜单ID以英文逗号分隔。

**数据库表结构**：
```sql
CREATE TABLE `role_module` (
  `role_id` bigint NOT NULL COMMENT 'role_id',
  `module_id` varchar(256) NOT NULL COMMENT 'module_id',
  `menu_id` varchar(256) NOT NULL COMMENT '菜单ID',
  KEY `role_id` (`role_id`),
  CONSTRAINT `role_module_ibfk_1` FOREIGN KEY (`role_id`) REFERENCES `role` (`id`)
)
```

**用户角色表结构**：
```sql
CREATE TABLE `user_role` (
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `user_type` ENUM('ldap', 'local') NOT NULL COMMENT '用户类型：ldap或local',
  `role_id` bigint NOT NULL COMMENT '角色ID',
  PRIMARY KEY (`user_id`, `role_id`, `user_type`),
  KEY `role_id` (`role_id`),
  CONSTRAINT `user_role_ibfk_1` FOREIGN KEY (`role_id`) REFERENCES `role` (`id`)
)
```

**权限控制说明**：
- 权限控制粒度在菜单级别，用户登录后只能看到被授权的菜单
- 系统会根据用户所属角色的权限，动态构建导航菜单
- 用户权限是通过角色关联获取的，一个用户可以拥有多个角色
- 系统会自动将存储在数据库中的逗号分隔ID字符串转换为对应的模块和菜单对象
- 系统支持两种类型的用户：LDAP用户和本地用户，通过 `user_type` 字段区分

## 登录接口

### 用户登录

**请求方式**：POST

**URL**：`/api/login`

**请求参数**：

| 参数名   | 类型   | 必填 | 描述     |
|----------|--------|------|----------|
| username | string | 是   | 用户名   |
| password | string | 是   | 密码     |

**响应示例**：

成功响应（本地用户）：
```json
{
  "id": 1,
  "username": "admin",
  "display_name": "系统管理员",
  "is_local_admin": true,
  "permissions": {
    "roles": [
      {
        "id": 1,
        "role_name": "超级管理员"
      }
    ],
    "modules": [
      {
        "id": 1,
        "name": "资源查询",
        "module_name": "resource_query",
        "menus": [
          {
            "id": 1,
            "name": "应用查询",
            "menu_name": "app_query"
          },
          {
            "id": 2,
            "name": "主机查询",
            "menu_name": "host_query"
          }
        ]
      }
    ]
  }
}
```

成功响应（LDAP用户）：
```json
{
  "id": 2,
  "username": "user1",
  "display_name": "用户1",
  "is_local_admin": false,
  "permissions": {
    "roles": [
      {
        "id": 2,
        "role_name": "普通用户"
      }
    ],
    "modules": [
      {
        "id": 1,
        "name": "资源查询",
        "module_name": "resource_query",
        "menus": [
          {
            "id": 1,
            "name": "应用查询",
            "menu_name": "app_query"
          }
        ]
      }
    ]
  }
}
```

失败响应：
```json
{
  "error": "用户不存在或密码错误"
}
```

**说明**：
- 登录流程首先检查本地用户，如果存在则验证密码
- 如果不是本地用户，则尝试通过LDAP进行认证
- LDAP认证会尝试所有活跃的LDAP配置，直到找到用户或尝试完所有配置
- 登录成功后返回用户信息，包括用户ID、用户名、显示名称和是否为本地管理员
- 同时返回用户的权限信息，包括角色、模块和菜单，用于前端构建导航菜单和权限控制
- 权限信息中的模块和菜单是从角色关联的逗号分隔ID字符串转换而来
- 系统会根据用户类型（本地用户或LDAP用户）获取相应的权限信息
- 登录成功后会返回JWT令牌，有效期为24小时
- 同时会设置HTTP-only cookie，包含相同的令牌

## Token 验证

### 验证令牌

**请求方式**：GET

**URL**：`/api/validate-token`

**请求头**：
```
Authorization: Bearer <token>
```

或者使用 Cookie 中的 token

**响应示例**：

成功响应：
```json
{
  "valid": true,
  "user": {
    "id": 1,
    "username": "admin",
    "display_name": "系统管理员",
    "is_local_admin": true
  },
  "permissions": {
    "roles": [
      {
        "id": 1,
        "name": "超级管理员"
      }
    ],
    "modules": [
      {
        "id": 1,
        "name": "resource_query",
        "display_name": "资源查询",
        "menus": [
          {
            "id": 1,
            "name": "app_query",
            "display_name": "应用查询"
          }
        ]
      }
    ]
  },
  "new_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

失败响应：
```json
{
  "valid": false,
  "error": "认证令牌已过期"
}
```

**说明**：
- 此接口用于验证JWT令牌的有效性
- 可以通过Authorization头或Cookie提供令牌
- 成功响应包含用户信息和权限数据
- 如果令牌即将过期（少于30分钟），会返回新令牌
- 如果令牌无效或已过期，返回相应的错误信息

## LDAP 配置管理

### 获取所有 LDAP 配置

**请求方式**：GET

**URL**：`/api/ldap-config`

**响应示例**：
```json
[
  {
    "id": 1,
    "server": "ldap.example.com",
    "port": 389,
    "bind_dn": "cn=admin,dc=example,dc=com",
    "ou_path": "ou=users,dc=example,dc=com",
    "is_active": true
  }
]
```

### 创建 LDAP 配置

**请求方式**：POST

**URL**：`/api/ldap-config`

**请求参数**：

| 参数名    | 类型    | 必填 | 描述         |
|-----------|---------|------|--------------|
| server    | string  | 是   | LDAP 服务器地址 |
| port      | integer | 是   | 端口号       |
| bind_dn   | string  | 是   | 绑定 DN      |
| password  | string  | 是   | 绑定密码     |
| ou_path   | string  | 是   | OU 路径      |
| is_active | boolean | 否   | 是否激活     |

**响应示例**：

成功响应：
```json
{
  "id": 1,
  "message": "LDAP 配置保存成功"
}
```

失败响应：
```json
{
  "error": "LDAP配置已存在，服务器、端口和绑定DN的组合必须唯一"
}
```

### 获取所有 LDAP 配置（详细信息）

**请求方式**：GET

**URL**：`/api/ldap-configs`

**响应示例**：
```json
[
  {
    "id": 1,
    "server": "ldap.example.com",
    "port": 389,
    "bind_dn": "cn=admin,dc=example,dc=com",
    "ou_path": "ou=users,dc=example,dc=com",
    "is_active": true,
    "create_time": "2023-01-01T12:00:00",
    "update_time": "2023-01-02T14:30:00"
  }
]
```

### 更新 LDAP 配置

**请求方式**：PUT

**URL**：`/api/ldap-config/<config_id>`

**请求参数**：

| 参数名    | 类型    | 必填 | 描述         |
|-----------|---------|------|--------------|
| server    | string  | 否   | LDAP 服务器地址 |
| port      | integer | 否   | 端口号       |
| bind_dn   | string  | 否   | 绑定 DN      |
| password  | string  | 否   | 绑定密码     |
| ou_path   | string  | 否   | OU 路径      |
| is_active | boolean | 否   | 是否激活     |

**响应示例**：
```json
{
  "message": "LDAP 配置更新成功"
}
```

### 删除 LDAP 配置

**请求方式**：DELETE

**URL**：`/api/ldap-config/<config_id>`

**响应示例**：
```json
{
  "message": "LDAP 配置删除成功"
}
```

## 用户管理

### 获取用户列表

**请求方式**：GET

**URL**：`/api/users`

**请求参数**：

| 参数名   | 类型   | 必填 | 描述     |
|----------|--------|------|----------|
| username | string | 否   | 用户名过滤 |

**响应示例**：
```json
[
  {
    "id": 1,
    "username": "user1",
    "display_name": "用户1",
    "roles": ["管理员", "运维人员"]
  }
]
```

### 获取用户角色

**请求方式**：GET

**URL**：`/api/users/<user_id>/roles`

**URL 参数**：

| 参数名  | 类型    | 必填 | 描述   |
|---------|---------|------|--------|
| user_id | integer | 是   | 用户ID |

**响应示例**：
```json
{
  "user": {
    "id": 1,
    "username": "user1",
    "display_name": "用户1"
  },
  "assigned_roles": [1, 2],
  "all_roles": [
    {
      "id": 1,
      "role_name": "管理员",
      "description": "系统管理员"
    },
    {
      "id": 2,
      "role_name": "运维人员",
      "description": "负责系统运维的人员"
    },
    {
      "id": 3,
      "role_name": "普通用户",
      "description": "普通系统用户"
    }
  ]
}
```

**说明**：
- 返回用户当前分配的角色ID列表和系统中所有可用的角色
- `assigned_roles` 字段包含用户当前拥有的角色ID
- `all_roles` 字段包含系统中所有角色的详细信息
- 此接口适用于用户角色分配页面

### 分配角色

**请求方式**：PUT

**URL**：`/api/users/<user_id>/roles`

**URL 参数**：

| 参数名  | 类型    | 必填 | 描述   |
|---------|---------|------|--------|
| user_id | integer | 是   | 用户ID |

**请求参数**：

| 参数名    | 类型   | 必填 | 描述         |
|-----------|--------|------|--------------|
| roles     | array  | 是   | 角色 ID 列表 |
| user_type | string | 否   | 用户类型，'ldap'或'local'，默认为'ldap' |

**请求示例**：
```json
{
  "roles": [1, 2],
  "user_type": "local"
}
```

**响应示例**：
```json
{
  "message": "角色分配成功"
}
```

**说明**：
- 此接口用于为指定用户分配角色
- 会先清除用户现有的所有角色，然后分配新角色
- 用户的权限是通过角色关联获取的，分配角色后用户将获得这些角色的所有权限
- 需要指定用户类型（'ldap'或'local'），以区分不同来源的用户

## LDAP 用户导入

### 从 LDAP 导入用户

**请求方式**：POST

**URL**：`/api/ldap-fetch-users`

**请求参数**：

| 参数名    | 类型    | 必填 | 描述         |
|-----------|---------|------|--------------|
| config_id | integer | 是   | LDAP 配置 ID  |

**响应示例**：

成功响应：
```json
{
  "message": "用户导入成功",
  "added": 10,
  "updated": 5,
  "total": 15
}
```

失败响应：
```json
{
  "error": "LDAP服务器连接失败"
}
```

**说明**：
- 此接口会连接到指定的 LDAP 服务器
- 搜索并导入用户信息到系统
- 返回新增和更新的用户数量
- 导入的用户需要单独分配角色才能获得权限
- 导入的用户类型为 'ldap'

### LDAP 用户搜索测试

**请求方式**：POST

**URL**：`/api/ldap-test-search`

**请求参数**：

| 参数名    | 类型    | 必填 | 描述         |
|-----------|---------|------|--------------|
| config_id | integer | 是   | LDAP 配置 ID  |
| username  | string  | 是   | 要搜索的用户名 |

**响应示例**：

成功找到用户：
```json
{
  "found": true,
  "count": 1,
  "users": [
    {
      "cn": "张三",
      "sAMAccountName": "zhangsan",
      "displayName": "张三",
      "mail": "<EMAIL>"
    }
  ]
}
```

未找到用户：
```json
{
  "found": false,
  "message": "在LDAP中未找到用户: zhangsan"
}
```

## 用户权限查询

### 获取特定用户的权限详情

**请求方式**：GET

**URL**：`/api/users/<user_id>/permissions`

**URL 参数**：

| 参数名  | 类型    | 必填 | 描述   |
|---------|---------|------|--------|
| user_id | integer | 是   | 用户ID |

**响应示例**：
```json
{
  "user": {
    "id": 1,
    "username": "user1",
    "display_name": "用户1"
  },
  "roles": [
    {
      "id": 1,
      "role_name": "管理员"
    }
  ],
  "modules": [
    {
      "id": 1,
      "name": "资源查询",
      "module_name": "resource_query",
      "menus": [
        {
          "id": 1,
          "name": "应用查询",
          "menu_name": "app_query"
        },
        {
          "id": 2,
          "name": "主机查询",
          "menu_name": "host_query"
        }
      ]
    }
  ]
}
```

**说明**：
- 返回用户拥有的所有权限，以树形结构组织
- `modules` 字段包含用户有权访问的所有模块
- 每个模块下的 `menus` 字段包含该模块下用户有权访问的所有菜单
- 此接口适用于前端构建用户的导航菜单和权限控制
- 系统会自动将用户所有角色中的权限（存储为逗号分隔的ID字符串）合并并转换为树形结构

### 获取所有用户的权限汇总

**请求方式**：GET

**URL**：`/api/users/permissions`

**响应示例**：
```json
[
  {
    "id": 1,
    "username": "user1",
    "display_name": "用户1",
    "roles": ["管理员"],
    "modules": ["资源查询", "基础数据管理"],
    "menus": ["应用查询", "主机查询", "系统管理"]
  },
  {
    "id": 2,
    "username": "admin",
    "display_name": "系统管理员",
    "roles": ["超级管理员"],
    "modules": ["资源查询", "基础数据管理", "系统管理"],
    "menus": ["应用查询", "主机查询", "用户管理", "角色管理", "LDAP配置"]
  }
]
```

**说明**：
- 返回所有用户的权限汇总信息
- 每个用户包含其拥有的角色、模块和菜单的列表
- 此接口适用于管理员查看系统中所有用户的权限分配情况
- 系统会自动将用户所有角色中的权限（存储为逗号分隔的ID字符串）合并并提取模块和菜单名称



