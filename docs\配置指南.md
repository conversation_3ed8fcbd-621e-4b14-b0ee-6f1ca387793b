# 配置指南

本文档描述了ICMS2中可用的配置选项。

## 配置文件

主要配置文件是项目根目录中的`config.py`。它为不同的环境定义了几个配置类。

## 基础配置

`Config`类定义了所有环境通用的基础配置选项：

```python
class Config:
    # 基本配置
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'hard-to-guess-string'
    
    # 数据库配置
    SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URL') or 'mysql+pymysql://用户名:密码@主机名/数据库名'
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    
    # JWT配置
    JWT_ACCESS_TOKEN_EXPIRES = timedelta(hours=24)  # 令牌有效期24小时
    
    # 日志配置
    LOG_FILE = 'icms2.log'
    LOG_MAX_BYTES = 1000000
    LOG_BACKUP_COUNT = 5
```

## 特定环境配置

### 开发环境配置

在开发期间使用，启用调试：

```python
class DevelopmentConfig(Config):
    DEBUG = True
```

### 测试环境配置

用于运行测试：

```python
class TestingConfig(Config):
    TESTING = True
    SQLALCHEMY_DATABASE_URI = os.environ.get('TEST_DATABASE_URL') or 'mysql+pymysql://用户名:密码@主机名/测试数据库名'
```

### 生产环境配置

针对生产使用进行优化：

```python
class ProductionConfig(Config):
    # 在生产环境中使用更强的密钥
    SECRET_KEY = os.environ.get('SECRET_KEY') or os.urandom(24)
    
    # 生产环境特定日志设置
    @staticmethod
    def init_app(app):
        Config.init_app(app)
        
        # 设置文件日志
        import logging
        from logging.handlers import RotatingFileHandler
        
        file_handler = RotatingFileHandler(
            Config.LOG_FILE, 
            maxBytes=Config.LOG_MAX_BYTES, 
            backupCount=Config.LOG_BACKUP_COUNT
        )
        file_handler.setFormatter(logging.Formatter(
            '%(asctime)s %(levelname)s: %(message)s [in %(pathname)s:%(lineno)d]'
        ))
        file_handler.setLevel(logging.INFO)
        
        app.logger.addHandler(file_handler)
```

## 配置选择

在`run.py`中选择要使用的配置：

```python
config_name = os.environ.get('FLASK_CONFIG') or 'default'
app = create_app(config[config_name])
```

`config`字典将环境名称映射到配置类：

```python
config = {
    'development': DevelopmentConfig,
    'testing': TestingConfig,
    'production': ProductionConfig,
    
    'default': DevelopmentConfig
}
```

## 环境变量

ICMS2使用以下环境变量进行配置：

| 变量          | 描述                                | 默认值                                |
|---------------|-------------------------------------|--------------------------------------|
| FLASK_CONFIG  | 要使用的配置环境                    | 'default'（DevelopmentConfig）        |
| SECRET_KEY    | 用于签名cookie和令牌的密钥          | 'hard-to-guess-string'或随机字节      |
| DATABASE_URL  | SQLAlchemy数据库连接URL             | MySQL连接字符串                       |
| TEST_DATABASE_URL | 测试环境的数据库URL              | MySQL测试连接字符串                   |

## 设置环境变量

### Windows

```
set FLASK_CONFIG=production
set SECRET_KEY=your-secret-key
```

### macOS/Linux

```
export FLASK_CONFIG=production
export SECRET_KEY=your-secret-key
```

## 数据库配置

数据库连接通过`SQLALCHEMY_DATABASE_URI`设置进行配置，该设置遵循SQLAlchemy连接字符串格式：

```
mysql+pymysql://用户名:密码@主机名/数据库名
```

替换：
- `用户名`：数据库用户名
- `密码`：数据库密码
- `主机名`：数据库服务器主机名或IP
- `数据库名`：数据库名称

## 日志配置

日志配置使用以下设置：

- `LOG_FILE`：日志文件名
- `LOG_MAX_BYTES`：每个日志文件的最大大小
- `LOG_BACKUP_COUNT`：要保留的备份日志文件数量

在生产模式下，设置了旋转文件处理程序来管理日志文件。

## JWT配置

JWT（JSON Web令牌）设置：

- `JWT_ACCESS_TOKEN_EXPIRES`：令牌过期时间（默认：24小时）

## 应用程序初始化

在应用程序初始化期间调用每个配置类中的`init_app`方法，以应用特定于环境的设置。
