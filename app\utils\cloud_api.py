"""
云API工具模块，提供与各云服务商API交互的通用方法
"""
from flask import current_app
from ..models import CloudApiKey, TencentRegion
from .. import db

def get_tencent_credentials():
    """
    获取腾讯云API密钥
    
    Returns:
        tuple: (secret_id, secret_key) 腾讯云API密钥对
    """
    try:
        # 查询腾讯云API密钥
        from .. import db
        from ..models import CloudApiKey
        
        # 使用ORM查询
        api_key = CloudApiKey.query.filter_by(cloudname='tencent').first()
        
        if not api_key:
            from flask import current_app
            current_app.logger.error("未找到腾讯云API密钥配置")
            return None, None
        
        return api_key.secret_id, api_key.secret_key
    except Exception as e:
        from flask import current_app
        current_app.logger.error(f"获取腾讯云API密钥时出错: {str(e)}")
        # 如果ORM查询失败，尝试使用原生SQL
        try:
            from sqlalchemy import text
            from flask import current_app
            from .. import db
            
            result = db.session.execute(
                text("SELECT secret_id, secret_key FROM cloud_api_key WHERE cloudname = 'tencent' LIMIT 1")
            ).fetchone()
            
            if not result:
                current_app.logger.error("未找到腾讯云API密钥配置")
                return None, None
            
            return result[0], result[1]
        except Exception as e2:
            current_app.logger.error(f"使用原生SQL获取腾讯云API密钥时出错: {str(e2)}")
            return None, None

def get_tencent_regions():
    """
    获取腾讯云区域列表
    
    Returns:
        list: 区域代码列表
    """
    regions = TencentRegion.query.all()
    return [region.region_en for region in regions if region.region_en]


