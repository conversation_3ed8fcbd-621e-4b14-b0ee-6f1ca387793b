# 应用环境管理 API 文档

本文档描述了应用环境管理模块（`application_environment.py`）中的所有 API 接口。

## 目录

- [获取应用环境列表](#获取应用环境列表)
- [获取应用下拉列表](#获取应用下拉列表)
- [获取环境下拉列表](#获取环境下拉列表)
- [获取部署方式下拉列表](#获取部署方式下拉列表)
- [创建应用环境](#创建应用环境)
- [更新应用环境信息](#更新应用环境信息)
- [下线应用环境](#下线应用环境)
- [获取应用环境资源列表](#获取应用环境资源列表)
- [搜索负载均衡资源](#搜索负载均衡资源)
- [搜索服务器资源](#搜索服务器资源)
- [搜索数据库资源](#搜索数据库资源)
- [为应用环境分配负载均衡资源](#为应用环境分配负载均衡资源)
- [为应用环境分配服务器资源](#为应用环境分配服务器资源)
- [为应用环境分配数据库资源](#为应用环境分配数据库资源)
- [从应用环境中移除负载均衡资源](#从应用环境中移除负载均衡资源)
- [从应用环境中移除服务器资源](#从应用环境中移除服务器资源)
- [从应用环境中移除数据库资源](#从应用环境中移除数据库资源)
- [通过系统名称查询应用环境](#通过系统名称查询应用环境)
- [通过资源查询应用环境](#通过资源查询应用环境)

## 获取应用环境列表

获取系统中所有应用环境的信息，支持按应用名称进行模糊查询。

**请求方式**：GET

**URL**：`/api/application-environments`

**查询参数**：

| 参数名 | 类型 | 必填 | 描述 |
|-------|------|------|------|
| app_en_name | string | 否 | 应用英文名关键字，支持模糊查询 |
| app_cn_name | string | 否 | 应用中文名关键字，支持模糊查询 |
| page | integer | 否 | 页码，默认为1 |
| per_page | integer | 否 | 每页记录数，默认为20 |

**响应示例**：

```json
{
  "total": 2,
  "page": 1,
  "per_page": 20,
  "pages": 1,
  "application_environments": [
    {
      "id": 1,
      "application_id": 1,
      "app_en_name": "icms",
      "app_cn_name": "IT资源管理系统",
      "env_id": 1,
      "env_en_name": "prod",
      "env_cn_name": "生产环境",
      "app_deploy_method": "1,2",
      "deploy_method_names": ["容器化", "虚拟机"],
      "app_env_url": "https://icms.example.com",
      "app_env_port": 443,
      "app_env_status": "online",
      "create_time": "2023-05-22 10:15:30",
      "update_time": "2023-05-22 10:15:30",
      "offline_time": null,
      "app_env_remark": "IT资源管理平台生产环境"
    },
    {
      "id": 2,
      "application_id": 2,
      "app_en_name": "oa",
      "app_cn_name": "办公自动化系统",
      "env_id": 2,
      "env_en_name": "test",
      "env_cn_name": "测试环境",
      "app_deploy_method": "2",
      "deploy_method_names": ["虚拟机"],
      "app_env_url": "https://oa-test.example.com",
      "app_env_port": 8080,
      "app_env_status": "online",
      "create_time": "2023-05-20 09:30:00",
      "update_time": "2023-05-21 14:20:15",
      "offline_time": null,
      "app_env_remark": "办公自动化系统测试环境"
    }
  ]
}
```

## 获取应用下拉列表

获取所有在线状态的应用系统，用于下拉选择。

**请求方式**：GET

**URL**：`/api/applications-dropdown`

**响应示例**：

```json
[
  {
    "id": 1,
    "app_en_name": "icms",
    "app_cn_name": "IT资源管理系统"
  },
  {
    "id": 2,
    "app_en_name": "oa",
    "app_cn_name": "办公自动化系统"
  }
]
```

## 获取环境下拉列表

获取所有环境，用于下拉选择。

**请求方式**：GET

**URL**：`/api/environments-dropdown`

**响应示例**：

```json
[
  {
    "id": 1,
    "env_en_name": "prod",
    "env_cn_name": "生产环境"
  },
  {
    "id": 2,
    "env_en_name": "test",
    "env_cn_name": "测试环境"
  },
  {
    "id": 3,
    "env_en_name": "dev",
    "env_cn_name": "开发环境"
  }
]
```

## 获取部署方式下拉列表

获取所有部署方式，用于下拉选择。

**请求方式**：GET

**URL**：`/api/deploy-methods-dropdown`

**响应示例**：

```json
[
  {
    "id": 1,
    "deploy_code": "container",
    "deploy_name": "容器化"
  },
  {
    "id": 2,
    "deploy_code": "vm",
    "deploy_name": "虚拟机"
  },
  {
    "id": 3,
    "deploy_code": "physical",
    "deploy_name": "物理机"
  }
]
```

## 创建应用环境

创建新的应用环境记录。

**请求方式**：POST

**URL**：`/api/application-environments`

**请求体**：

| 参数名 | 类型 | 必填 | 描述 |
|-------|------|------|------|
| application_id | integer | 是 | 应用ID |
| env_id | integer | 是 | 环境ID |
| app_deploy_method | string | 是 | 部署方式ID列表，逗号分隔 |
| app_env_url | string | 否 | 应用URL |
| app_env_port | integer | 否 | 应用端口 |
| app_env_remark | string | 否 | 备注信息 |

**请求示例**：

```json
{
  "application_id": 1,
  "env_id": 2,
  "app_deploy_method": "1,2",
  "app_env_url": "https://icms-test.example.com",
  "app_env_port": 8443,
  "app_env_remark": "IT资源管理平台测试环境"
}
```

**响应示例**：

```json
{
  "message": "应用环境创建成功",
  "id": 3
}
```

**错误响应**：

```json
{
  "error": "缺少必填字段: application_id"
}
```

或

```json
{
  "error": "该应用在该环境下已存在"
}
```

## 更新应用环境信息

更新现有应用环境的信息，仅支持修改部署方式、应用URL、应用端口和备注字段。

**请求方式**：PUT

**URL**：`/api/application-environments/{app_env_id}`

**路径参数**：

| 参数名 | 类型 | 描述 |
|-------|------|------|
| app_env_id | integer | 应用环境ID |

**请求体**：

| 参数名 | 类型 | 必填 | 描述 |
|-------|------|------|------|
| app_deploy_method | string | 否 | 部署方式ID列表，逗号分隔 |
| app_env_url | string | 否 | 应用URL |
| app_env_port | integer | 否 | 应用端口 |
| app_env_remark | string | 否 | 备注信息 |

**请求示例**：

```json
{
  "app_deploy_method": "1,3",
  "app_env_url": "https://icms-new.example.com",
  "app_env_port": 9443,
  "app_env_remark": "更新后的备注信息"
}
```

**响应示例**：

```json
{
  "message": "应用环境更新成功"
}
```

**错误响应**：

```json
{
  "message": "没有变更需要保存"
}
```

## 下线应用环境

将应用环境状态设置为下线，并记录下线时间。

**请求方式**：PUT

**URL**：`/api/application-environments/{app_env_id}/offline`

**路径参数**：

| 参数名 | 类型 | 描述 |
|-------|------|------|
| app_env_id | integer | 应用环境ID |

**响应示例**：

```json
{
  "message": "应用环境已成功下线，并已解除所有资源绑定"
}
```

**错误响应**：

```json
{
  "message": "应用环境已经是下线状态"
}
```

**说明**：
- 将应用环境状态设置为"offline"，并记录下线时间
- 下线操作会自动解除该应用环境与所有资源（负载均衡、服务器、数据库）的绑定关系
- 如果应用环境已经是下线状态，将返回400错误

## 获取应用环境资源列表

获取指定应用环境关联的所有资源（负载均衡、服务器、数据库）。

**请求方式**：GET

**URL**：`/api/application-environments/{app_env_id}/resources`

**路径参数**：

| 参数名 | 类型 | 描述 |
|-------|------|------|
| app_env_id | integer | 应用环境ID |

**响应示例**：

```json
{
  "load_balancers": [
    {
      "id": 1,
      "loadbalancer_id": "lb-12345678",
      "lb_name": "前端负载均衡",
      "vip": "********",
      "ip_version": "ipv4",
      "belong_network": "内网",
      "lb_type": 1,
      "zone_region": "上海",
      "create_time": "2023-05-10 08:30:00",
      "original_source": "tencent"
    }
  ],
  "servers": [
    {
      "id": 1,
      "instance_id": "ins-12345678",
      "hostname": "web-server-01",
      "ipv4": "********",
      "os": "CentOS 7.9",
      "cpu": "4核",
      "memory": "8GB",
      "region": "上海",
      "server_port": "8080"
    }
  ],
  "databases": [
    {
      "id": 1,
      "db_name": "icms_db",
      "db_ip": "********",
      "db_type": "MySQL",
      "purpose": "主数据库",
      "remark": "IT资源管理系统主数据库"
    }
  ]
}
```

## 搜索负载均衡资源

搜索负载均衡资源，支持通过VIP、名称或实例ID进行模糊查询。

**请求方式**：GET

**URL**：`/api/load-balancers/search`

**查询参数**：

| 参数名 | 类型 | 必填 | 描述 |
|-------|------|------|------|
| keyword | string | 否 | 关键字，用于匹配VIP、名称或实例ID |
| page | integer | 否 | 页码，默认为1 |
| per_page | integer | 否 | 每页记录数，默认为20 |

**响应示例**：

```json
{
  "total": 2,
  "page": 1,
  "per_page": 20,
  "pages": 1,
  "load_balancers": [
    {
      "id": 1,
      "loadbalancer_id": "lb-12345678",
      "lb_name": "前端负载均衡",
      "vip": "********",
      "ip_version": "ipv4",
      "belong_network": "内网",
      "lb_type": 1,
      "zone_region": "上海",
      "create_time": "2023-05-10 08:30:00",
      "original_source": "tencent",
      "lb_status": "online",
      "offline_time": null
    },
    {
      "id": 2,
      "loadbalancer_id": "lb-87654321",
      "lb_name": "后端负载均衡",
      "vip": "********",
      "ip_version": "ipv4",
      "belong_network": "内网",
      "lb_type": 1,
      "zone_region": "上海",
      "create_time": "2023-05-11 09:15:00",
      "original_source": "tencent",
      "lb_status": "online",
      "offline_time": null
    }
  ]
}
```

**说明**：
- 返回符合条件的负载均衡资源列表及分页信息
- 支持通过关键字对VIP、名称或实例ID进行模糊查询
- 只返回状态为"online"的负载均衡资源
- 返回完整的负载均衡字段信息，包括lb_status和offline_time

## 搜索服务器资源

搜索服务器资源，支持通过IP、主机名或实例ID进行模糊查询。

**请求方式**：GET

**URL**：`/api/servers/search`

**查询参数**：

| 参数名 | 类型 | 必填 | 描述 |
|-------|------|------|------|
| keyword | string | 否 | 关键字，用于匹配IP、主机名或实例ID |
| page | integer | 否 | 页码，默认为1 |
| per_page | integer | 否 | 每页记录数，默认为20 |

**响应示例**：

```json
{
  "total": 2,
  "page": 1,
  "per_page": 20,
  "pages": 1,
  "servers": [
    {
      "id": 1,
      "instance_id": "ins-12345678",
      "hostname": "web-server-01",
      "ipv4": "********",
      "os": "CentOS 7.9",
      "cpu": "4核",
      "memory": "8GB",
      "region": "上海",
      "server_status": "online",
      "offline_time": null
    },
    {
      "id": 2,
      "instance_id": "ins-87654321",
      "hostname": "app-server-01",
      "ipv4": "********",
      "os": "CentOS 7.9",
      "cpu": "8核",
      "memory": "16GB",
      "region": "上海",
      "server_status": "online",
      "offline_time": null
    }
  ]
}
```

**说明**：
- 返回符合条件的服务器资源列表及分页信息
- 支持通过关键字对IP、主机名或实例ID进行模糊查询
- 只返回状态为"online"的服务器资源
- 返回完整的服务器字段信息，包括server_status和offline_time

## 搜索数据库资源

搜索数据库资源，支持通过数据库名称、IP或类型进行模糊查询。

**请求方式**：GET

**URL**：`/api/databases/search`

**查询参数**：

| 参数名 | 类型 | 必填 | 描述 |
|-------|------|------|------|
| keyword | string | 否 | 关键字，用于匹配数据库名称、IP或类型 |
| page | integer | 否 | 页码，默认为1 |
| per_page | integer | 否 | 每页记录数，默认为20 |

**响应示例**：

```json
{
  "total": 2,
  "page": 1,
  "per_page": 20,
  "pages": 1,
  "databases": [
    {
      "id": 1,
      "db_name": "icms_db",
      "db_ip": "********",
      "db_type": "MySQL",
      "purpose": "主数据库",
      "remark": "IT资源管理系统主数据库",
      "creat_time": "2023-05-10 08:30:00",
      "db_status": "online",
      "offline_time": null
    },
    {
      "id": 2,
      "db_name": "icms_log_db",
      "db_ip": "********",
      "db_type": "MySQL",
      "purpose": "日志数据库",
      "remark": "IT资源管理系统日志数据库",
      "creat_time": "2023-05-11 09:15:00",
      "db_status": "online",
      "offline_time": null
    }
  ]
}
```

**说明**：
- 返回符合条件的数据库资源列表及分页信息
- 支持通过关键字对数据库名称、IP或类型进行模糊查询
- 只返回状态为"online"的数据库资源
- 返回完整的数据库字段信息，包括db_status和offline_time

## 为应用环境分配负载均衡资源

为指定应用环境分配负载均衡资源。

**请求方式**：POST

**URL**：`/api/application-environments/{app_env_id}/load-balancers`

**路径参数**：

| 参数名 | 类型 | 描述 |
|-------|------|------|
| app_env_id | integer | 应用环境ID |

**请求体**：

| 参数名 | 类型 | 必填 | 描述 |
|-------|------|------|------|
| lb_id | integer | 是 | 负载均衡资源ID |

**请求示例**：

```json
{
  "lb_id": 2
}
```

**响应示例**：

```json
{
  "message": "负载均衡资源分配成功"
}
```

**错误响应**：

```json
{
  "error": "该负载均衡资源已分配给此应用环境"
}
```

## 为应用环境分配服务器资源

为指定应用环境分配服务器资源。

**请求方式**：POST

**URL**：`/api/application-environments/{app_env_id}/servers`

**路径参数**：

| 参数名 | 类型 | 描述 |
|-------|------|------|
| app_env_id | integer | 应用环境ID |

**请求体**：

| 参数名 | 类型 | 必填 | 描述 |
|-------|------|------|------|
| server_id | integer | 是 | 服务器资源ID |
| server_port | string | 否 | 应用端口 |

**请求示例**：

```json
{
  "server_id": 2,
  "server_port": "8080,8443"
}
```

**响应示例**：

```json
{
  "message": "服务器资源分配成功"
}
```

**错误响应**：

```json
{
  "error": "该服务器资源已分配给此应用环境"
}
```

## 为应用环境分配数据库资源

为指定应用环境分配数据库资源。

**请求方式**：POST

**URL**：`/api/application-environments/{app_env_id}/databases`

**路径参数**：

| 参数名 | 类型 | 描述 |
|-------|------|------|
| app_env_id | integer | 应用环境ID |

**请求体**：

| 参数名 | 类型 | 必填 | 描述 |
|-------|------|------|------|
| db_id | integer | 是 | 数据库资源ID |

**请求示例**：

```json
{
  "db_id": 2
}
```

**响应示例**：

```json
{
  "message": "数据库资源分配成功"
}
```

**错误响应**：

```json
{
  "error": "该数据库资源已分配给此应用环境"
}
```

## 从应用环境中移除负载均衡资源

从指定应用环境中移除负载均衡资源。

**请求方式**：DELETE

**URL**：`/api/application-environments/{app_env_id}/load-balancers/{lb_id}`

**路径参数**：

| 参数名 | 类型 | 描述 |
|-------|------|------|
| app_env_id | integer | 应用环境ID |
| lb_id | integer | 负载均衡资源ID |

**响应示例**：

```json
{
  "message": "负载均衡资源移除成功"
}
```

## 从应用环境中移除服务器资源

从指定应用环境中移除服务器资源。

**请求方式**：DELETE

**URL**：`/api/application-environments/{app_env_id}/servers/{server_id}`

**路径参数**：

| 参数名 | 类型 | 描述 |
|-------|------|------|
| app_env_id | integer | 应用环境ID |
| server_id | integer | 服务器资源ID |

**响应示例**：

```json
{
  "message": "服务器资源移除成功"
}
```

## 从应用环境中移除数据库资源

从指定应用环境中移除数据库资源。

**请求方式**：DELETE

**URL**：`/api/application-environments/{app_env_id}/databases/{db_id}`

**路径参数**：

| 参数名 | 类型 | 描述 |
|-------|------|------|
| app_env_id | integer | 应用环境ID |
| db_id | integer | 数据库资源ID |

**响应示例**：

```json
{
  "message": "数据库资源移除成功"
}
```

## 通过系统名称查询应用环境

通过系统英文名或中文名进行模糊查询应用环境。

**请求方式**：GET

**URL**：`/api/application-environments/search`

**查询参数**：

| 参数名 | 类型 | 必填 | 描述 |
|-------|------|------|------|
| keyword | string | 是 | 关键字，用于匹配系统英文名或中文名 |
| page | integer | 否 | 页码，默认为1 |
| per_page | integer | 否 | 每页记录数，默认为20 |

**响应格式**：与[获取应用环境列表](#获取应用环境列表)相同

## 通过资源查询应用环境

通过资源IP查询关联的应用环境。

**请求方式**：GET

**URL**：`/api/application-environments/search-by-resource`

**查询参数**：

| 参数名 | 类型 | 必填 | 描述 |
|-------|------|------|------|
| resource_type | string | 是 | 资源类型，可选值：lb（负载均衡）、server（服务器）、db（数据库） |
| ip | string | 是 | 资源IP地址 |
| page | integer | 否 | 页码，默认为1 |
| per_page | integer | 否 | 每页记录数，默认为20 |

**响应格式**：与[获取应用环境列表](#获取应用环境列表)相同
