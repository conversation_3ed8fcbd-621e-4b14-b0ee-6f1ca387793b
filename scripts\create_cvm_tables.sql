-- 创建CVM创建记录表的SQL脚本
-- 注意：net_area_base、env_net_conf、creat_cvm_hostname表已存在于数据库中

-- 创建CVM创建记录表
CREATE TABLE IF NOT EXISTS `cvm_creation_record` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
  `application_id` bigint NOT NULL COMMENT '应用ID',
  `environment_id` bigint NOT NULL COMMENT '环境ID',
  `net_area_id` bigint NOT NULL COMMENT '网络安全区ID',
  `instance_id` varchar(128) DEFAULT NULL COMMENT '腾讯云实例ID',
  `hostname` varchar(128) NOT NULL COMMENT '主机名',
  `instance_type` varchar(128) DEFAULT NULL COMMENT '实例机型',
  `placement` varchar(128) DEFAULT NULL COMMENT '可用区',
  `vpc_id` varchar(128) DEFAULT NULL COMMENT 'VPC ID',
  `subnet_id` varchar(128) DEFAULT NULL COMMENT '子网ID',
  `security_group_id` varchar(128) DEFAULT NULL COMMENT '安全组ID',
  `image_id` varchar(128) DEFAULT NULL COMMENT '镜像ID',
  `server_port` varchar(100) DEFAULT NULL COMMENT '应用端口',
  `creation_status` enum('creating','success','failed') DEFAULT 'creating' COMMENT '创建状态',
  `error_message` text COMMENT '错误信息',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `complete_time` datetime DEFAULT NULL COMMENT '完成时间',
  `created_by` bigint NOT NULL COMMENT '创建用户',
  `remark` text COMMENT '备注',
  PRIMARY KEY (`id`),
  KEY `idx_application_id` (`application_id`),
  KEY `idx_environment_id` (`environment_id`),
  KEY `idx_net_area_id` (`net_area_id`),
  KEY `idx_created_by` (`created_by`),
  KEY `idx_instance_id` (`instance_id`),
  KEY `idx_creation_status` (`creation_status`),
  CONSTRAINT `cvm_creation_record_application_FK` FOREIGN KEY (`application_id`) REFERENCES `application` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `cvm_creation_record_environment_FK` FOREIGN KEY (`environment_id`) REFERENCES `environment_base` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `cvm_creation_record_net_area_FK` FOREIGN KEY (`net_area_id`) REFERENCES `net_area_base` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `cvm_creation_record_user_FK` FOREIGN KEY (`created_by`) REFERENCES `user` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='CVM创建记录表';

-- 插入一些示例数据到creat_cvm_hostname表（如果不存在）
INSERT IGNORE INTO `creat_cvm_hostname` (`nscode`, `current_hostname`) VALUES
('htsh', 'htsh101268'),
('htsz', 'htsz101268');

-- 插入一些示例数据到net_area_base表（如果不存在）
INSERT IGNORE INTO `net_area_base` (`id`, `net_area_name`, `net_area_des`) VALUES
(1, '生产区', '生产环境网络安全区'),
(2, '测试区', '测试环境网络安全区'),
(3, '开发区', '开发环境网络安全区'),
(4, '灾备区', '灾备环境网络安全区'),
(5, 'DMZ区', 'DMZ网络安全区');

-- 插入一些示例数据到env_net_conf表（如果不存在）
-- 注意：这里的数据需要根据实际环境配置进行调整
INSERT IGNORE INTO `env_net_conf` (`env_id`, `net_area_id`, `subnetid`, `placement`, `vpcid`, `imageid`, `instance_type`, `securitygroupid`) VALUES
(1, 1, 'subnet-prod001', 'ap-shanghai-fsi-1', 'vpc-prod001', 'img-centos76', 'S3.MEDIUM4', 'sg-prod001'),
(2, 2, 'subnet-test001', 'ap-shanghai-fsi-2', 'vpc-test001', 'img-centos76', 'S3.SMALL2', 'sg-test001'),
(3, 3, 'subnet-dev001', 'ap-shanghai-fsi-3', 'vpc-dev001', 'img-centos76', 'S3.SMALL1', 'sg-dev001'),
(4, 4, 'subnet-dr001', 'ap-shenzhen-fsi-1', 'vpc-dr001', 'img-centos76', 'S3.MEDIUM4', 'sg-dr001');
