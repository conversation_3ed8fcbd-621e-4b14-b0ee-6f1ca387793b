# 负载均衡 API 接口文档

本文档详细描述了负载均衡模块（`load_balancer.py`）中的所有 API 接口，包括请求方式、URL、参数和响应格式。

## 目录

- [查询负载均衡列表](#查询负载均衡列表)
- [同步腾讯云负载均衡数据](#同步腾讯云负载均衡数据)
- [获取负载均衡后端服务列表](#获取负载均衡后端服务列表)
- [通过后端绑定的IP查询负载均衡信息](#通过后端绑定的ip查询负载均衡信息)
- [下线负载均衡资源](#下线负载均衡资源)

## 查询负载均衡列表

**请求方式**：GET

**URL**：`/api/load-balancers`

**查询参数**：

| 参数名    | 类型    | 必填 | 描述         |
|-----------|---------|------|--------------|
| name      | string  | 否   | 负载均衡名称关键字 |
| vip       | string  | 否   | VIP地址关键字 |
| page      | integer | 否   | 页码，默认为1 |
| per_page  | integer | 否   | 每页记录数，默认为20 |

**响应示例**：
```json
{
  "items": [
    {
      "id": 1,
      "loadbalancer_id": "lb-a1b2c3d4",
      "lb_name": "测试负载均衡",
      "vip": "********",
      "ip_version": "ipv4",
      "belong_network": "vpc-xyz123",
      "lb_type": 1,
      "zone_region": "ap-guangzhou",
      "create_time": "2023-01-01 12:00:00",
      "original_source": "tencent",
      "lb_status": "online",
      "offline_time": null
    }
  ],
  "pagination": {
    "total": 1,
    "page": 1,
    "per_page": 20,
    "pages": 1
  }
}
```

**说明**：
- 支持按名称和VIP地址进行模糊查询
- 返回结果按创建时间降序排列
- 分页信息包含总记录数、当前页码、每页记录数和总页数
- 返回完整的负载均衡字段信息，包括lb_status和offline_time

## 同步腾讯云负载均衡数据

**请求方式**：POST

**URL**：`/api/load-balancers/sync-tencent`

**请求头**：
```
Content-Type: application/json
```

**请求体**：
```json
{}
```

**重要说明**：
- 即使不需要传递数据，也必须发送一个空的JSON对象 `{}`
- 必须设置正确的 `Content-Type: application/json` 请求头，否则会出现 400 Bad Request 错误

**响应示例**：
```json
{
  "message": "腾讯云负载均衡同步完成",
  "stats": {
    "total": 10,
    "added": 3,
    "updated": 2,
    "unchanged": 5,
    "recycled": 2,
    "errors": 0,
    "error_details": []
  }
}
```

**说明**：
- 同步腾讯云所有区域的负载均衡数据到系统，采用增量同步方式
- 腾讯云同步的负载均衡默认状态为online
- 通过分页机制获取所有负载均衡数据，确保不遗漏任何资源
- 对于在数据库中存在但在腾讯云API返回结果中不存在的负载均衡，会被标记为offline状态
- 离线状态判断机制会考虑腾讯云返回的分页数据，确保检查所有页面的数据后再做判断
- 下线的负载均衡会自动解除与应用环境的绑定关系
- 返回同步统计信息，包括总数、新增数、更新数、未变更数、下线数和错误数
- 如果出现错误，会在error_details中列出详细信息
- 需要预先配置腾讯云API密钥和区域信息

## 获取负载均衡后端服务列表

**请求方式**：GET

**URL**：`/api/load-balancers/<region>/<lb_id>/backends`

**路径参数**：

| 参数名  | 类型   | 必填 | 描述         |
|---------|--------|------|--------------|
| region  | string | 是   | 区域代码，如ap-guangzhou |
| lb_id   | string | 是   | 负载均衡实例ID |

**响应示例**：
```json
{
  "backends": [
    {
      "protocol": "TCP",
      "listener_port": 80,
      "domain": "",
      "instance_id": "ins-abcdef12",
      "ip": "********",
      "port": 8080,
      "weight": 10
    },
    {
      "protocol": "HTTP",
      "listener_port": 443,
      "domain": "example.com",
      "instance_id": "ins-xyz98765",
      "ip": "********",
      "port": 8443,
      "weight": 10
    }
  ]
}
```

**说明**：
- 返回指定负载均衡实例绑定的后端服务列表
- 根据负载均衡类型自动选择不同的API调用方式
- 对于通用负载均衡(lb_type=1)，会根据监听器协议类型进行不同处理：
  - TCP协议：提取监听器端口，以及后端服务器的实例ID、IP地址、端口和权重
  - HTTP/HTTPS协议：提取监听器端口、域名，以及后端服务器的实例ID、IP地址、端口和权重
- 对于传统负载均衡(lb_type=0)：
  - 获取监听器信息，提取Protocol、ListenerPort、InstancePort字段
  - 获取后端目标信息，提取InstanceId、PrivateIpAddresses、Weight字段
  - 将监听器信息和后端目标信息合并，为每个后端目标添加对应监听器的协议和端口信息
  - 如果一个负载均衡有多个监听器，每个监听器都会与所有后端目标组合生成多条记录
- 所有情况下返回的数据结构保持一致，对于没有的字段（如TCP协议没有域名）返回空字符串，方便前端处理
- 需要预先配置腾讯云API密钥

## 通过后端绑定的IP查询负载均衡信息

**请求方式**：GET

**URL**：`/api/load-balancers/by-backend-ip`

**查询参数**：

| 参数名 | 类型   | 必填 | 描述         |
|--------|--------|------|--------------|
| ip     | string | 是   | 后端服务器IP地址 |

**响应示例**：
```json
{
  "load_balancers": [
    {
      "loadbalancer_id": "lb-a1b2c3d4",
      "lb_name": "测试负载均衡",
      "region": "ap-guangzhou",
      "vip": "********"
    }
  ]
}
```

**说明**：
- 通过后端服务器IP地址查询绑定的负载均衡信息
- 会在所有腾讯云区域中查询，直到找到结果
- 返回负载均衡ID、名称、区域和VIP地址
- 需要预先配置腾讯云API密钥和区域信息

## 下线负载均衡资源

将负载均衡资源设置为下线状态。

**请求方式**：PUT

**URL**：`/api/load-balancers/<lb_id>/offline`

**路径参数**：

| 参数名 | 类型    | 必填 | 描述         |
|--------|---------|------|--------------|
| lb_id  | integer | 是   | 负载均衡资源ID |

**响应示例**：
```json
{
  "message": "负载均衡已成功下线"
}
```

**错误响应**：
```json
{
  "message": "负载均衡已经是下线状态"
}
```

或

```json
{
  "error": "下线负载均衡时出错: [错误详情]"
}
```

**说明**：
- 将负载均衡资源状态设置为"offline"，并记录下线时间
- 下线操作会自动解除该负载均衡资源与所有应用环境的绑定关系
- 如果负载均衡资源已经是下线状态，将返回400错误
- 下线操作会被记录到资源变更历史
- 如果下线过程中出现异常，将返回500错误
