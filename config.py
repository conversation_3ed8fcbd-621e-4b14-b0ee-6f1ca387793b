import os
from datetime import timedelta

class Config:
    # 基本配置
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'hard-to-guess-string'

    # 数据库配置
    SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URL') or 'mysql+pymysql://icmsopr:Icopr1234@10.64.21.234/icms2'
    SQLALCHEMY_TRACK_MODIFICATIONS = False

    # JWT 配置
    JWT_ACCESS_TOKEN_EXPIRES = timedelta(hours=24)  # Token 有效期 24 小时

    # CORS 配置
    CORS_SUPPORTS_CREDENTIALS = True  # 允许跨域请求发送 Cookie

    # 日志配置
    LOG_DIR = os.environ.get('LOG_DIR') or 'logs'  # 日志目录
    LOG_FILE = 'icms2.log'  # 日志文件名
    LOG_LEVEL = os.environ.get('LOG_LEVEL') or 'INFO'  # 日志级别
    LOG_BACKUP_COUNT = 30  # 保留30天的日志
    LOG_TO_CONSOLE = True  # 是否输出到控制台
    # 日志格式
    LOG_FORMAT = '%(asctime)s [%(levelname)s] [%(remote_addr)s] [用户:%(username)s(%(user_id)s)] %(message)s [in %(pathname)s:%(lineno)d]'

    # 定时任务配置
    SCHEDULER_API_ENABLED = True  # 启用调度器API
    SCHEDULER_TIMEZONE = 'Asia/Shanghai'  # 调度器时区

    @staticmethod
    def init_app(app):
        # 使用自定义日志工具初始化日志
        from app.utils.logger import setup_logger
        setup_logger(app)

class DevelopmentConfig(Config):
    DEBUG = True

class TestingConfig(Config):
    TESTING = True
    SQLALCHEMY_DATABASE_URI = os.environ.get('TEST_DATABASE_URL') or 'mysql+pymysql://icmsopr:Icopr1234@10.64.21.234/icms2_test'

class ProductionConfig(Config):
    # 生产环境可以使用更强的密钥
    SECRET_KEY = os.environ.get('SECRET_KEY') or os.urandom(24)

    # 生产环境日志级别设置为INFO
    LOG_LEVEL = 'INFO'

    @staticmethod
    def init_app(app):
        # 调用父类方法
        Config.init_app(app)

        # 生产环境特定配置
        app.logger.info("生产环境配置已加载")

# 配置字典，用于选择不同环境的配置
config = {
    'development': DevelopmentConfig,
    'testing': TestingConfig,
    'production': ProductionConfig,

    'default': DevelopmentConfig
}

