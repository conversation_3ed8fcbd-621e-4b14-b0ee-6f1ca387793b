# 安装指南

本指南提供了设置和运行ICMS2应用程序的说明。

## 前提条件

在安装ICMS2之前，请确保您具备以下前提条件：

- Python 3.8或更高版本
- MySQL 5.7或更高版本
- pip（Python包管理器）
- Git（可选，用于克隆仓库）

## 安装步骤

### 1. 克隆仓库（可选）

如果您使用Git，请克隆仓库：

```bash
git clone [仓库URL]
cd icms2
```

### 2. 创建虚拟环境

建议为Python项目使用虚拟环境：

```bash
# 创建虚拟环境
python -m venv .venv

# 激活虚拟环境
# 在Windows上：
.venv\Scripts\activate
# 在macOS/Linux上：
source .venv/bin/activate
```

### 3. 安装依赖项

安装所需的Python包：

```bash
pip install -r requirements.txt
```

### 4. 配置数据库

编辑`config.py`文件以设置数据库连接：

```python
SQLALCHEMY_DATABASE_URI = 'mysql+pymysql://用户名:密码@主机名/数据库名'
```

将`用户名`、`密码`、`主机名`和`数据库名`替换为您的MySQL凭据。

### 5. 初始化数据库

创建数据库表：

```bash
# 进入Python交互式shell
python

# 在Python shell中
from app import create_app, db
from config import config
app = create_app(config['development'])
with app.app_context():
    db.create_all()
    
# 退出Python shell
exit()
```

### 6. 运行应用程序

启动应用程序：

```bash
python run.py
```

应用程序将在`http://localhost:5000`上可用。

## 环境配置

ICMS2支持不同的环境配置：

- **开发环境**：默认配置，启用调试
- **测试环境**：用于运行测试的配置
- **生产环境**：针对生产使用进行优化

要指定环境，请设置`FLASK_CONFIG`环境变量：

```bash
# 在Windows上：
set FLASK_CONFIG=production
# 在macOS/Linux上：
export FLASK_CONFIG=production
```

## LDAP配置

如果您使用LDAP认证，您需要通过应用程序的管理界面或直接在数据库中配置LDAP设置。

## 故障排除

### 常见问题

1. **数据库连接错误**：
   - 验证`config.py`中的数据库凭据
   - 确保MySQL服务器正在运行
   - 检查数据库是否存在

2. **包安装错误**：
   - 确保您使用的是正确的Python版本
   - 更新pip：`pip install --upgrade pip`
   - 单独安装包以识别特定问题

3. **应用程序启动错误**：
   - 检查日志文件（`icms2.log`）以获取详细的错误消息
   - 验证所有必需的环境变量是否已设置

有关更详细的故障排除，请参阅应用程序日志或联系系统管理员。
