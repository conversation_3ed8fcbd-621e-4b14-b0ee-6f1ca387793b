# 角色管理 API 文档

本文档描述了角色管理模块（`role.py`）中的所有 API 接口。

## 目录

- [角色权限存储说明](#角色权限存储说明)
- [获取所有角色](#获取所有角色)
- [获取角色详情](#获取角色详情)
- [创建角色](#创建角色)
- [更新角色](#更新角色)
- [删除角色](#删除角色)
- [获取角色下的用户](#获取角色下的用户)

## 角色权限存储说明

本系统采用优化的角色授权方式，每个角色的授权在 `role_module` 表中只存储一行记录，多个模块ID或菜单ID以英文逗号分隔。

**数据库表结构**：
```sql
CREATE TABLE `role_module` (
  `role_id` bigint NOT NULL COMMENT 'role_id',
  `module_id` varchar(256) NOT NULL COMMENT 'module_id',
  `menu_id` varchar(256) NOT NULL COMMENT '菜单ID',
  KEY `role_id` (`role_id`),
  CONSTRAINT `role_module_ibfk_1` FOREIGN KEY (`role_id`) REFERENCES `role` (`id`)
)
```

**字段说明**：
- `role_id`: 角色ID，关联到 `role` 表的 `id` 字段
- `module_id`: 逗号分隔的模块ID字符串，例如 "1,2,3"
- `menu_id`: 逗号分隔的菜单ID字符串，例如 "1,2,3,4,5"

**权限控制说明**：
- 权限控制粒度在菜单级别，用户登录后只能看到被授权的菜单
- 系统会根据用户所属角色的权限，动态构建导航菜单
- 首页权限默认分配给所有用户

## 获取所有角色

获取系统中所有角色及其权限信息。

**请求方式**：GET

**URL**：`/api/roles`

**响应示例**：
```json
[
  {
    "id": 1,
    "role_name": "管理员",
    "description": "系统管理员",
    "modules": [
      {
        "id": 1,
        "name": "资源查询",
        "menus": [
          {
            "id": 1,
            "name": "应用查询"
          },
          {
            "id": 2,
            "name": "主机查询"
          }
        ]
      },
      {
        "id": 2,
        "name": "用户管理",
        "menus": [
          {
            "id": 10,
            "name": "用户查询"
          }
        ]
      }
    ]
  }
]
```

**说明**：
- 返回所有角色的列表
- 每个角色包含基本信息（ID、名称、描述）和权限信息
- 权限信息以树形结构组织，包含模块和菜单
- 系统会自动将存储在数据库中的逗号分隔ID字符串转换为对应的模块和菜单对象

## 获取角色详情

获取特定角色的详细信息，包括权限结构。

**请求方式**：GET

**URL**：`/api/roles/<role_id>`

**URL 参数**：

| 参数名  | 类型    | 必填 | 描述   |
|---------|---------|------|--------|
| role_id | integer | 是   | 角色ID |

**响应示例**：
```json
{
  "id": 1,
  "role_name": "管理员",
  "description": "系统管理员",
  "modules": [
    {
      "id": 1,
      "name": "资源查询",
      "menus": [
        {
          "id": 1,
          "name": "应用查询"
        },
        {
          "id": 2,
          "name": "主机查询"
        }
      ]
    }
  ],
  "module_ids": [1, 2, 3],
  "menu_ids": [1, 2, 3, 4, 5]
}
```

**说明**：
- 返回指定角色的详细信息
- `modules` 字段以树形结构返回角色拥有的模块和菜单权限
- `module_ids` 和 `menu_ids` 字段以扁平数组形式返回角色拥有的所有模块ID和菜单ID，方便前端处理
- 这些ID是从数据库中存储的逗号分隔字符串转换而来

## 创建角色

创建新角色并分配权限。

**请求方式**：POST

**URL**：`/api/roles`

**请求参数**：

| 参数名      | 类型   | 必填 | 描述     |
|-------------|--------|------|----------|
| role_name   | string | 是   | 角色名称 |
| description | string | 否   | 角色描述 |
| module_ids  | array  | 否   | 模块ID列表 |
| menu_ids    | array  | 否   | 菜单ID列表 |

**请求示例**：
```json
{
  "role_name": "运维人员",
  "description": "负责系统运维的人员",
  "module_ids": [1, 2],
  "menu_ids": [1, 2, 3, 4]
}
```

**响应示例**：
```json
{
  "message": "角色创建成功",
  "id": 3
}
```

**说明**：
- 创建新角色并分配指定的模块和菜单权限
- 角色名称必须唯一
- 返回新创建角色的ID
- 系统会将提供的模块ID数组和菜单ID数组转换为逗号分隔的字符串存储在数据库中

## 更新角色

更新现有角色的信息和权限。

**请求方式**：PUT

**URL**：`/api/roles/<role_id>`

**URL 参数**：

| 参数名  | 类型    | 必填 | 描述   |
|---------|---------|------|--------|
| role_id | integer | 是   | 角色ID |

**请求参数**：

| 参数名      | 类型   | 必填 | 描述     |
|-------------|--------|------|----------|
| role_name   | string | 否   | 角色名称 |
| description | string | 否   | 角色描述 |
| module_ids  | array  | 否   | 模块ID列表 |
| menu_ids    | array  | 否   | 菜单ID列表 |

**请求示例**：
```json
{
  "role_name": "高级运维",
  "description": "负责核心系统运维的人员",
  "module_ids": [1, 2, 3],
  "menu_ids": [1, 2, 3, 4, 5, 6]
}
```

**响应示例**：
```json
{
  "message": "角色更新成功"
}
```

**说明**：
- 更新指定角色的基本信息和权限
- 如果提供了模块ID和菜单ID，会更新对应的权限
- 如果某个字段未提供，则保持原值不变
- 系统会将提供的模块ID数组和菜单ID数组转换为逗号分隔的字符串存储在数据库中

## 删除角色

删除指定的角色。

**请求方式**：DELETE

**URL**：`/api/roles/<role_id>`

**URL 参数**：

| 参数名  | 类型    | 必填 | 描述   |
|---------|---------|------|--------|
| role_id | integer | 是   | 角色ID |

**响应示例**：

成功响应：
```json
{
  "message": "角色删除成功"
}
```

失败响应（角色已分配给用户）：
```json
{
  "error": "无法删除角色，该角色已分配给用户",
  "user_count": 15,
  "users": [
    {
      "id": 1,
      "username": "user1",
      "display_name": "用户1"
    },
    {
      "id": 2,
      "username": "user2",
      "display_name": "用户2"
    }
  ],
  "has_more": true
}
```

**说明**：
- 删除角色前会检查该角色是否已分配给用户
- 如果角色已分配给用户，则返回错误信息，并提供使用该角色的用户列表（最多10个）
- 如果用户数量超过10个，`has_more` 字段为 `true`，表示还有更多用户
- 只有当角色未分配给任何用户时，才能成功删除
- 删除角色时会同时删除该角色在 `role_module` 表中的权限记录

## 获取角色下的用户

获取拥有指定角色的所有用户。

**请求方式**：GET

**URL**：`/api/roles/<role_id>/users`

**URL 参数**：

| 参数名  | 类型    | 必填 | 描述   |
|---------|---------|------|--------|
| role_id | integer | 是   | 角色ID |

**响应示例**：
```json
[
  {
    "id": 1,
    "username": "user1",
    "display_name": "用户1",
    "email": "<EMAIL>",
    "is_active": true
  },
  {
    "id": 2,
    "username": "user2",
    "display_name": "用户2",
    "email": "<EMAIL>",
    "is_active": true
  }
]
```

**说明**：
- 返回拥有指定角色的所有用户列表
- 每个用户包含基本信息（ID、用户名、显示名称、邮箱、是否激活）
- 如果没有用户拥有该角色，则返回空数组

